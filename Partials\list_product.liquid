<div class="product-image-block">
  {% if product.discount > 0 %}
  <span class="flag sale">{% t "Sale" %}</span>
  {% endif %}
  {% if product.stock == 0 and product.stock_unlimited == false %}
  <span class="flag out-stock">{% t "Out of Stock" %}</span>
  {% elsif product.status == 'not-available' %}
  <span class="flag out-stock">{% t "Not Available" %}</span>
  {% endif %}
  {% if product.images != empty %}
  <a href="{{product.url}}"><img class="img-fluid img-portfolio img-hover mb-3" src="{{product.images.first | resize:'600x700'}}" srcset="{{product.images.first | resize:'600x700'}} 1x, {{product.images.first | resize:'1200x1400'}} 2x" alt="{{product.name | escape}}" /></a>
  {% else %}
  <a href="{{product.url}}"><img class="img-fluid img-portfolio img-hover mb-3" src="{{ 'no-image-home.jpg' | asset }}" alt="{{product.name | escape}}"></a>
  {% endif %}
</div>
<div class="caption {% if options.hide_price %}no-price{% endif %}">
  <h3><a href="{{product.url}}">{{ product.name | truncate:50 }}</a></h3>
  {% if product.brand != empty %}<h6>{{product.brand}}</h6>{% endif %}

  {% if product.stock == 0 and product.stock_unlimited == false %}
  {% if options.hide_price != true %}
  <div class="price-mob">
    <span class="product-block-price"> {{ product.price | price }} </span>
  </div>
  {% endif %}
  {% elsif product.status == 'not-available' %}
  <div class="pt-md-3">
    <span class="product-block-not-available text-muted"><b>{% t "Not Available" %}</b></span>
  </div>
  {% else %}
  {% if options.hide_price != true %}
  <div class="price-mob">
    {% if product.discount > 0 %}
    <span class="product-block-price">{{ product.price | minus:product.discount | price }}</span> <span class="product-block-discount"> {{ product.price | price }} </span>
    {% else %}
    <span class="product-block-price">{{ product.price | price }}</span>
    {% endif %}
  </div>
  {% endif %}
  {% endif %}
  <div class="clearfix"></div>
</div>
