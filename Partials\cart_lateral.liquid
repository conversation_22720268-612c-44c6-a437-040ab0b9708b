<button id="close-cart-btn" class="btn btn-link btn-block text-left lateral-header-btn">
  <span>{% t 'Shopping cart' %}</span> <i class="linear-icon icon-0811-cross"></i>
</button>
<div class="cart-wrapper mt-3">
  <form id="minicart-empty" {% if order.products.size !=0 %}hidden{% endif %}>
    <div class="minicart-content-wrapper ">
      <div class="subtitle text-center">
        {% t "The shopping cart is currently empty. You can go back and start adding products." %}
      </div>
    </div>
  </form>
  <form id="cart-products-display" class="{% if order.products == empty %}hidden{% endif %}">
    <div class="subtitle text-center">
      {% t "You have" %} <span class="count">{{ order.products_count }}</span> {% t "item(s) in your shopping cart" %}
    </div>
    <div class="cart-list">
      {% for ordered_product in order.products %}
      <div class="row">

        <div class="col-5">
          <a class="product-item-photo" href="{{ ordered_product.url }}" title="{{ ordered_product.name }}">
            {% if ordered_product.image %}
            <img class="product-image-photo" src="{{ ordered_product.image }}" alt="Cart Product">
            {% else %}
            <img class="img-hover" src="{{ 'no-image-home.jpg' | asset }}" alt="{{ordered_product.name | escape}}">
            {% endif %}
          </a>
        </div>
        <div class="col-7 pl-0">
          <div class="product-item-details text-left">
            <div class="product-item-name product-block">
              <h3 class="product-name"><a href="{{ordered_product.url}}">{{ ordered_product.name | truncate:50 }}</a></h3>
              {% if ordered_product.brand.size > 0 %}
              <h6 class="product-brand">{{ordered_product.brand}}</h6>
              {% endif %}
              {{ordered_product.qty}} x
              <span class="price-mob">
                {{ ordered_product.price | minus:ordered_product.discount | price }}
              </span>
              {% if ordered_product.discount > 0 %}
              <span class="price-mob product-form-discount">
                {{ ordered_product.price | price }}
              </span>
              {% endif%}
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
    <div class="row total mt-2 mb-4">
      <div class="col-4 text-left">
        <h4>{% t "Cart Total" %}</h4>
      </div>
      <div class="col-8">
        <span class="price-mob">{{order.total | price }}</span>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <div class="actions lateral-cart-actions">
          <a id="cart-link" href="{{order.url}}" class="btn btn-primary btn-block">
            <span>{% t "View Cart" %}</span>
          </a>
          <a href="{{order.checkout_url}}" class="btn btn-primary accent-bg btn-block" title="Checkout">
            <span>{% t "Checkout" %}</span>
          </a>
        </div>
      </div>
    </div>
  </form>
</div>
