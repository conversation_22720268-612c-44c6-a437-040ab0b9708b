{% comment %}
  Component: luxury_brands_showcase_v2.liquid
  Descripción: Showcase premium para marcas de lujo con diseño elegante y personalizable
  Versión: 2.0
  Autor: Desarrollado para Jumpseller
{% endcomment %}

<style>
  .luxury-showcase-v2 {
    --primary-color: {{ options.showcase_v2_primary_color | default: '#1a1a1a' }};
    --secondary-color: {{ options.showcase_v2_secondary_color | default: '#ffffff' }};
    --accent-color: {{ options.showcase_v2_accent_color | default: '#d4af37' }};
    --text-primary: {{ options.showcase_v2_text_primary | default: '#1a1a1a' }};
    --text-secondary: {{ options.showcase_v2_text_secondary | default: '#ffffff' }};
    --transition-duration: 0.5s;
    width: 100%;
    background-color: #f5f5f5;
  }

  .luxury-content {
    position: relative;
    padding: 4rem 0;
    text-align: center;
  }

  .luxury-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 0 2rem;
  }

  .luxury-header h2 {
    font-family: 'Didot', serif;
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-weight: 400;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }

  .luxury-section-title {
    font-size: 1.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 2rem;
    color: var(--text-primary);
  }

  .luxury-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    width: 100%;
  }

  @media (max-width: 768px) {
    .luxury-grid {
      grid-template-columns: 1fr;
    }
  }

  .luxury-item {
    position: relative;
    text-decoration: none;
    display: block;
    overflow: hidden;
  }

  .luxury-item-image {
    position: relative;
    aspect-ratio: 3/4;
  }

  .luxury-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-duration);
  }

  .luxury-item:hover .luxury-item-image img {
    transform: scale(1.05);
  }

  .luxury-item-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, transparent 100%);
    height: 50%;
    opacity: 0;
    transition: opacity var(--transition-duration);
  }

  .luxury-item:hover .luxury-item-overlay {
    opacity: 1;
  }

  .luxury-item-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 2rem;
    color: var(--secondary-color);
    text-align: center;
    transform: translateY(20px);
    opacity: 0;
    transition: all var(--transition-duration);
  }

  .luxury-item:hover .luxury-item-content {
    transform: translateY(0);
    opacity: 1;
  }

  .luxury-item-title {
    font-family: 'Didot', serif;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 400;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    color: var(--secondary-color);
  }

  .luxury-item-price {
    font-size: 1.125rem;
    color: var(--secondary-color);
    font-weight: 500;
    margin-top: 0.5rem;
    opacity: 0.9;
  }

  .luxury-item-button {
    display: inline-block;
    padding: 0.75rem 2rem;
    margin-top: 1rem;
    border: 1px solid var(--secondary-color);
    color: var(--secondary-color);
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.1em;
    transition: all var(--transition-duration);
    text-decoration: none;
  }

  .luxury-item:hover .luxury-item-button {
    background: var(--secondary-color);
    color: var(--text-primary);
  }

  .luxury-explore-button {
    display: inline-block;
    padding: 1rem 3rem;
    margin-top: 3rem;
    border: 2px solid var(--text-primary);
    color: var(--text-primary);
    text-transform: uppercase;
    font-size: 1rem;
    letter-spacing: 0.2em;
    transition: all var(--transition-duration);
    text-decoration: none;
    font-weight: 500;
  }

  .luxury-explore-button:hover {
    background: var(--text-primary);
    color: var(--secondary-color);
  }
</style>

{% if options.showcase_v2_enabled %}
<div class="luxury-showcase-v2">
  <div class="luxury-content">
    <div class="luxury-header">
      <h2>{{ options.showcase_v2_title | default: 'MARCAS DE TENDENCIA' }}</h2>
      <div class="luxury-section-title">{{ options.showcase_v2_subtitle | default: 'Descubre lo más top' }}</div>
    </div>

    <div class="luxury-grid">
      {% for i in (1..4) %}
        {% assign image_key = 'showcase_v2_image_' | append: i %}
        {% assign title_key = 'showcase_v2_title_' | append: i %}
        {% assign price_key = 'showcase_v2_price_' | append: i %}
        {% assign link_key = 'showcase_v2_link_' | append: i %}
        
        {% if options[image_key] != blank %}
          <a href="{{ options[link_key] }}" class="luxury-item">
            <div class="luxury-item-image">
              <img src="{{ options[image_key].url }}" alt="{{ options[title_key] | default: 'Luxury Item' }}">
              <div class="luxury-item-overlay"></div>
            </div>
            <div class="luxury-item-content">
              <h3 class="luxury-item-title">{{ options[title_key] }}</h3>
              {% if options[price_key] != blank %}
                <p class="luxury-item-price">{{ options[price_key] }}</p>
              {% endif %}
              <span class="luxury-item-button">EXPLORAR</span>
            </div>
          </a>
        {% endif %}
      {% endfor %}
    </div>

    <a href="{{ options.showcase_v2_explore_link | default: '#' }}" class="luxury-explore-button">
      {{ options.showcase_v2_explore_text | default: 'EXPLORAR' }}
    </a>
  </div>
</div>
{% endif %} 