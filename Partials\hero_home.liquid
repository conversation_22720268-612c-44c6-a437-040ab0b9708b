{% comment %}
============================================================
  Partial: hero_home.liquid
  Descripción: Sección de inicio rediseñada, moderna y elegante,
               enfocada en la conversión. Configurable vía 'options'.
  Version: 3.1 (Refined data handling for loops)
  Autor: Asistente IA para Jumpseller
  Dependencias: Swiper.js (global)
  Notas: Incluye CSS y JS embebidos. Elimina testimonios.
============================================================
{% endcomment %}

{% comment %} Variables globales y configuraciones iniciales {% endcomment %}
{% assign section_id = 'hero-home-partial-' | append: section.id | default: 'hero-home-partial-main' %}
{% assign max_width_style = options.sucla_general_max_width | default: '1660px' %}

{% comment %} ==================== BLOQUE DE DEPURACIÓN ==================== {% endcomment %}
{% assign debug_enabled = true %}
{% if debug_enabled %}
<div style="background-color: #f8f9fa; border: 2px solid #dc3545; padding: 15px; margin: 15px; border-radius: 5px; font-family: monospace; font-size: 14px; line-height: 1.6; color: #212529; max-height: 500px; overflow-y: auto;">
  <h2 style="color: #dc3545; border-bottom: 1px solid #dee2e6; padding-bottom: 8px; margin-top: 0;">Debug Hero Home Partial</h2>
  
  <h3 style="margin-top: 15px; color: #0d6efd;">Variables Generales</h3>
  <ul style="list-style-type: none; padding-left: 10px;">
    <li><strong>section_id:</strong> {{ section_id }}</li>
    <li><strong>max_width_style:</strong> {{ max_width_style }}</li>
  </ul>
  
  <h3 style="margin-top: 15px; color: #0d6efd;">Hero Banner Principal</h3>
  <ul style="list-style-type: none; padding-left: 10px;">
    <li><strong>hero_enabled:</strong> {{ options.sucla_hero_enabled | default: "no definido (default: true)" }}</li>
    <li><strong>options.hero_image existe:</strong> {% if options.sucla_hero_image != blank %}Sí{% else %}No{% endif %}</li>
    {% if options.sucla_hero_image != blank %}
      <li><strong>options.hero_image tipo:</strong> {{ options.sucla_hero_image | typeof }}</li>
      <li><strong>options.hero_image url:</strong> {{ options.sucla_hero_image | image_url: 'master' }}</li>
      <li><strong>options.hero_image width:</strong> {{ options.sucla_hero_image.width }}</li>
      <li><strong>options.hero_image height:</strong> {{ options.sucla_hero_image.height }}</li>
    {% endif %}
    <li><strong>options.hero_title:</strong> {{ options.sucla_hero_title | default: "no definido" }}</li>
    <li><strong>options.hero_subtitle:</strong> {{ options.sucla_hero_subtitle | default: "no definido" }}</li>
    <li><strong>options.hero_cta_text:</strong> {{ options.sucla_hero_cta_text | default: "no definido" }}</li>
    <li><strong>options.hero_cta_link:</strong> {{ options.sucla_hero_cta_link | default: "no definido" }}</li>
    <li><strong>Condición para mostrar hero banner:</strong> {% if options.sucla_hero_enabled != blank %}{{ options.sucla_hero_enabled }}{% else %}true (valor por defecto){% endif %}</li>
  </ul>
  
  <h3 style="margin-top: 15px; color: #0d6efd;">Categorías Destacadas</h3>
  <ul style="list-style-type: none; padding-left: 10px;">
    <li><strong>featured_cats_enabled:</strong> {{ options.sucla_featured_cats_enabled | default: "no definido (default: true)" }}</li>
    <li><strong>featured_cats_layout:</strong> {{ options.sucla_featured_cats_layout | default: "no definido (default: grid)" }}</li>
    <li><strong>featured_cats_title:</strong> {{ options.sucla_featured_cats_title | default: "no definido" }}</li>
    
    {% for i in (1..6) %}
      {% capture image_option %}sucla_category_{{ i }}_image{% endcapture %}
      {% capture title_option %}sucla_category_{{ i }}_title{% endcapture %}
      {% capture link_option %}sucla_category_{{ i }}_link{% endcapture %}
      
      {% assign cat_image = options[image_option] %}
      {% assign cat_title = options[title_option] %}
      {% assign cat_link = options[link_option] %}
      
      {% if cat_image != blank %}
        {% assign img = cat_image %}
        {% assign img_url = img.url %}
        {% assign img_alt = img.alt | default: "Categoría " | append: i %}
        
        {% assign cat_title_escaped = cat_title | escape %}
        
        {% assign data_part1 = img_url | append: '||' | append: img_alt %}
        {% assign data_part2 = data_part1 | append: '||' | append: cat_title_escaped %}
        {% assign data_string = data_part2 | append: '||' | append: cat_link %}
        
        {% assign featured_categories_data = featured_categories_data | push: data_string %}
      {% endif %}
    {% endfor %}
    
    <li><strong>Número de categorías válidas:</strong> {{ featured_categories_data.size }}</li>
    <li><strong>Condición para mostrar categorías:</strong> {% if options.sucla_featured_cats_enabled != blank %}{{ options.sucla_featured_cats_enabled }}{% else %}true (valor por defecto){% endif %} AND {{ featured_categories_data.size }} > 0</li>
  </ul>
  
  <h3 style="margin-top: 15px; color: #0d6efd;">Carrusel de Productos</h3>
  <ul style="list-style-type: none; padding-left: 10px;">
    <li><strong>product_carousel_enabled:</strong> {{ options.sucla_product_carousel_enabled | default: "no definido (default: true)" }}</li>
    <li><strong>product_carousel_collection:</strong> {{ options.sucla_product_carousel_collection | default: "no definido (default: all)" }}</li>
    <li><strong>product_carousel_title:</strong> {{ options.sucla_product_carousel_title | default: "no definido" }}</li>
    <li><strong>product_carousel_limit:</strong> {{ options.sucla_product_carousel_limit | default: "no definido (default: 8)" }}</li>
    <li><strong>¿Colección existe?:</strong> {% if product_carousel_collection != blank %}Sí{% else %}No{% endif %}</li>
    {% if product_carousel_collection != blank %}
      <li><strong>Número de productos en la colección:</strong> {{ product_carousel_collection.products_count }}</li>
      {% if product_carousel_collection.products_count > 0 %}
        <li><strong>Primer producto:</strong> {{ product_carousel_collection.products.first.name }}</li>
      {% endif %}
    {% endif %}
    
    <li><strong style="color: {% if template contains 'product_card' %}green{% else %}red{% endif %};">Verificación de product_card.liquid:</strong> 
      {% if template contains 'product_card' %}
        Existe en el tema (detectado en templates)
      {% else %}
        <span style="color: red;">⚠️ NO ENCONTRADO - El partial "product_card.liquid" parece no existir o no está disponible</span> 
      {% endif %}
    </li>
    
    <li><strong>Condición para mostrar carrusel:</strong> {% if options.sucla_product_carousel_enabled != blank %}{{ options.sucla_product_carousel_enabled }}{% else %}true (valor por defecto){% endif %} AND collection != blank AND collection.products_count > 0</li>
  </ul>
  
  <h3 style="margin-top: 15px; color: #0d6efd;">Banner Promocional</h3>
  <ul style="list-style-type: none; padding-left: 10px;">
    <li><strong>promo_banner_enabled:</strong> {{ options.sucla_promo_banner_enabled | default: "no definido (default: false)" }}</li>
    <li><strong>options.promo_banner_image existe:</strong> {% if options.sucla_promo_banner_image != blank %}Sí{% else %}No{% endif %}</li>
    {% if options.sucla_promo_banner_image != blank %}
      <li><strong>options.promo_banner_image url:</strong> {{ options.sucla_promo_banner_image | image_url: 'master' }}</li>
    {% endif %}
    <li><strong>promo_banner_title:</strong> {{ options.sucla_promo_banner_title | default: "no definido" }}</li>
    <li><strong>promo_banner_subtitle:</strong> {{ options.sucla_promo_banner_subtitle | default: "no definido" }}</li>
    <li><strong>Condición para mostrar banner promo:</strong> {% if options.sucla_promo_banner_enabled != blank %}{{ options.sucla_promo_banner_enabled }}{% else %}false (valor por defecto){% endif %} AND options.sucla_promo_banner_image != blank</li>
  </ul>
  
  <h3 style="margin-top: 15px; color: #0d6efd;">Bloques de Contenido</h3>
  <ul style="list-style-type: none; padding-left: 10px;">
    <li><strong>content_blocks_enabled:</strong> {{ options.sucla_content_blocks_enabled | default: "no definido (default: false)" }}</li>
    <li><strong>content_blocks_title:</strong> {{ options.sucla_content_blocks_title | default: "no definido" }}</li>
    
    {% for i in (1..3) %}
      {% capture image_option %}sucla_block_{{ i }}_image{% endcapture %}
      {% capture title_option %}sucla_block_{{ i }}_title{% endcapture %}
      {% capture text_option %}sucla_block_{{ i }}_text{% endcapture %}
      {% capture link_option %}sucla_block_{{ i }}_link{% endcapture %}
      
      {% assign block_image = options[image_option] %}
      {% assign block_title = options[title_option] %}
      {% assign block_text = options[text_option] %}
      {% assign block_link = options[link_option] %}
      
      {% if block_image != blank or block_title != blank or block_text != blank %}
        <li style="margin-top: 5px;"><strong>Bloque {{ i }}:</strong></li>
        <ul style="list-style-type: none; padding-left: 15px;">
          <li><strong>Imagen:</strong> {% if block_image != blank %}Existe{% else %}No existe{% endif %}</li>
          <li><strong>Título:</strong> {{ block_title | default: "no definido" }}</li>
          <li><strong>Texto:</strong> {{ block_text | default: "no definido" }}</li>
          <li><strong>Enlace:</strong> {{ block_link | default: "no definido" }}</li>
          <li><strong>Válido para mostrar:</strong> {% if block_image != blank and block_title != blank %}Sí{% else %}No{% endif %}</li>
        </ul>
      {% endif %}
    {% endfor %}
    
    <li><strong>Número de bloques válidos:</strong> {{ content_blocks_data.size }}</li>
    <li><strong>Condición para mostrar bloques:</strong> {% if options.sucla_content_blocks_enabled != blank %}{{ options.sucla_content_blocks_enabled }}{% else %}false (valor por defecto){% endif %} AND {{ content_blocks_data.size }} > 0</li>
  </ul>
  
  <h3 style="margin-top: 15px; color: #0d6efd;">Instagram Feed</h3>
  <ul style="list-style-type: none; padding-left: 10px;">
    <li><strong>instagram_enabled:</strong> {{ options.sucla_instagram_enabled | default: "no definido (default: false)" }}</li>
    <li><strong>instagram_title:</strong> {{ options.sucla_instagram_title | default: "no definido" }}</li>
    <li><strong>instagram_username:</strong> {{ options.sucla_instagram_username | default: "no definido" }}</li>
    
    {% for i in (1..6) %}
      {% capture image_setting %}sucla_instagram_image_{{ i }}{% endcapture %}
      {% assign insta_img_obj = options[image_setting] %}
      
      {% if insta_img_obj != blank %}
        <li style="margin-top: 5px;"><strong>Instagram Imagen {{ i }}:</strong> Existe</li>
      {% endif %}
    {% endfor %}
    
    <li><strong>Número de imágenes Instagram válidas:</strong> {{ insta_images_data.size }}</li>
    <li><strong>Condición para mostrar Instagram:</strong> {% if options.sucla_instagram_enabled != blank %}{{ options.sucla_instagram_enabled }}{% else %}false (valor por defecto){% endif %} AND {{ insta_images_data.size }} > 0</li>
  </ul>
  
  <h3 style="margin-top: 15px; color: #0d6efd;">Verificación de Archivos Relacionados</h3>
  <ul style="list-style-type: none; padding-left: 10px;">
    <li><strong style="color: {% if template contains 'product_card' %}green{% else %}orange{% endif %};">Partial product_card.liquid:</strong> 
      {% if template contains 'product_card' %}
        Existe en el tema (detectado en templates)
      {% else %}
        Se ha creado un nuevo archivo product_card.liquid
      {% endif %}
    </li>
    
    <li><strong>Sliders disponibles:</strong>
      <ul>
        {% assign slider_principal = false %}
        {% assign slider_secondary = false %}
        
        {% for file in template %}
          {% if file contains 'home_slider_principal' %}
            {% assign slider_principal = true %}
          {% endif %}
          {% if file contains 'home_slider_secondary' %}
            {% assign slider_secondary = true %}
          {% endif %}
        {% endfor %}
        
        <li>
          <strong>home_slider_principal.liquid:</strong> 
          {% if slider_principal %}
            <span style="color: green;">✓ Disponible</span>
          {% else %}
            <span style="color: orange;">⚠️ No detectado en templates</span>
          {% endif %}
        </li>
        <li>
          <strong>home_slider_secondary.liquid:</strong> 
          {% if slider_secondary %}
            <span style="color: green;">✓ Disponible</span>
          {% else %}
            <span style="color: orange;">⚠️ No detectado en templates</span>
          {% endif %}
        </li>
      </ul>
    </li>
    
    <li><strong>Librerías JS requeridas:</strong> 
      <span style="color: orange;">⚠️ Se requiere Swiper.js (asegurarse que esté incluido en el layout principal)</span>
    </li>
  </ul>
  
  <h3 style="margin-top: 15px; color: #0d6efd;">Problemas Potenciales</h3>
  <ul style="list-style-type: none; padding-left: 10px;">
    <li>1. <strong>Hero Banner Principal:</strong> {% if options.sucla_hero_image == blank %}<span style="color: red;">⚠️ No hay imagen configurada para el banner principal</span>{% else %}Imagen configurada correctamente{% endif %}</li>
    <li>2. <strong>Categorías Destacadas:</strong> {% if featured_categories_data.size == 0 %}<span style="color: red;">⚠️ No hay categorías destacadas configuradas completamente (imagen+título+enlace)</span>{% else %}{{ featured_categories_data.size }} categorías configuradas{% endif %}</li>
    <li>3. <strong>Carrusel de Productos:</strong> {% if product_carousel_collection == blank or product_carousel_collection.products_count == 0 %}<span style="color: red;">⚠️ Colección de productos no encontrada o vacía</span>{% else %}Colección con {{ product_carousel_collection.products_count }} productos{% endif %}</li>
    <li>4. <strong>Referencias a CSS/JS:</strong> <span style="color: orange;">⚠️ Verificar que el tema incluya Swiper.js para los carruseles</span></li>
    <li>5. <strong>Variables en settings.json:</strong> <span style="color: orange;">⚠️ Verificar que el archivo Config/options.json tenga definidas las opciones utilizadas aquí</span></li>
  </ul>
  
  <h3 style="margin-top: 15px; color: #0d6efd;">Problemas Detectados y Soluciones</h3>
  <ul style="list-style-type: none; padding-left: 10px;">
    <li>1. <strong>Hero Home no estaba activado:</strong> El partial hero_home estaba comentado en Templates/Inicio. <span style="color: green;">✓ Solucionado - Se descomentó el código.</span></li>
    <li>2. <strong>Opciones no definidas en options.json:</strong> Las opciones específicas para hero_home no estaban definidas en options.json. <span style="color: green;">✓ Solucionado - Se agregaron las opciones necesarias.</span></li>
    <li>3. <strong>Partial product_card faltante:</strong> No existía el partial product_card.liquid necesario para el carrusel de productos. <span style="color: green;">✓ Solucionado - Se creó el archivo.</span></li>
    <li>4. <strong>Errores de sintaxis en código Liquid:</strong> Había varios errores de sintaxis en construcciones complejas de Liquid. <span style="color: green;">✓ Solucionado - Se corrigió la sintaxis.</span></li>
    <li>5. <strong>Swiper.js está incluido:</strong> <span style="color: green;">✓ Verificado - Swiper.js está correctamente incluido en el archivo Templates/Diseño.</span></li>
  </ul>
  
  <h3 style="margin-top: 15px; color: #0d6efd;">Problemas Detectados y Soluciones Adicionales</h3>
  <ul style="list-style-type: none; padding-left: 10px;">
    <li>1. <strong>Formato de imágenes:</strong> <span style="color: green;">✓ Solucionado - Ajustado para manejar formato JSON de imágenes { url, alt }</span></li>
    <li>2. <strong>Categorías sin título ni enlace:</strong> <span style="color: green;">✓ Solucionado - Se usan valores por defecto</span></li>
    <li>3. <strong>Partial product_card no detectado:</strong> <span style="color: green;">✓ Solucionado - Se cambió {% raw %}{% render %}{% endraw %} por {% raw %}{% include %}{% endraw %}</span></li>
    <li>4. <strong>Colección de productos:</strong> <span style="color: orange;">⚠️ Es necesario configurar una colección en el panel de Jumpseller</span></li>
  </ul>
  
  <p style="margin-top: 15px; font-weight: bold; color: #198754;">Ahora deberías ver el contenido correctamente después de guardar y configurar las opciones en el panel de administración de Jumpseller.</p>
  
</div>
{% endif %}

<div id="{{ section_id }}" class="hero-home-partial-wrapper">

  {% comment %} ==================== HERO BANNER PRINCIPAL ==================== {% endcomment %}
  {% assign hero_enabled = options.sucla_hero_enabled | default: true %}
  {% if hero_enabled %}
    <section class="hero-banner-section">
      <div class="hero-banner-container">
        <div class="hero-content-wrapper">
          {% comment %} Manejo de imagen del hero banner {% endcomment %}
          {% if options.sucla_hero_image != blank %}
            {% assign hero_alt_text = options.sucla_hero_alt_text | default: options.sucla_hero_title | default: store.name | escape %}
            {% assign hero_image = options.sucla_hero_image %}
            {% assign hero_image_url = hero_image.url %}
            <img
              src="{{ hero_image_url }}"
              alt="{{ hero_image.alt | default: hero_alt_text }}"
              class="hero-banner-image"
              loading="eager"
            >
          {% else %}
            <div class="hero-banner-placeholder"></div>
          {% endif %}
          <div class="hero-banner-overlay">
            <div class="hero-text-content" style="max-width: {{ max_width_style }};">
              {% if options.sucla_hero_title != blank %}
                <h1 class="hero-title">{{ options.sucla_hero_title }}</h1>
              {% endif %}
              {% if options.sucla_hero_subtitle != blank %}
                <p class="hero-subtitle">{{ options.sucla_hero_subtitle }}</p>
              {% endif %}
              {% if options.sucla_hero_cta_text != blank and options.sucla_hero_cta_link != blank %}
                <a href="{{ options.sucla_hero_cta_link }}" class="hero-cta-button">
                  {{ options.sucla_hero_cta_text }}
                </a>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </section>
  {% endif %}

  {% comment %} ==================== CATEGORÍAS DESTACADAS ==================== {% endcomment %}
  {% assign featured_cats_enabled = options.sucla_featured_cats_enabled | default: true %}
  {% assign featured_categories_data = "" | split: "," %}

  {% if featured_cats_enabled %}
    {% for i in (1..6) %}
      {% capture image_option %}sucla_category_{{ i }}_image{% endcapture %}
      {% capture title_option %}sucla_category_{{ i }}_title{% endcapture %}
      {% capture link_option %}sucla_category_{{ i }}_link{% endcapture %}

      {% assign cat_image = options[image_option] %}
      {% assign cat_title = options[title_option] %}
      {% assign cat_link = options[link_option] %}

      {% if cat_image != blank %}
        {% assign img = cat_image %}
        {% assign img_url = img.url %}
        {% assign img_alt = img.alt | default: "Categoría " | append: i %}
        
        {% assign cat_title_escaped = cat_title | escape %}
        
        {% assign data_part1 = img_url | append: '||' | append: img_alt %}
        {% assign data_part2 = data_part1 | append: '||' | append: cat_title_escaped %}
        {% assign data_string = data_part2 | append: '||' | append: cat_link %}
        
        {% assign featured_categories_data = featured_categories_data | push: data_string %}
      {% endif %}
    {% endfor %}
  {% endif %}

  {% if featured_cats_enabled and featured_categories_data.size > 0 %}
    <section class="featured-categories-section">
      <div class="section-container" style="max-width: {{ max_width_style }};">
        {% if options.sucla_featured_cats_title != blank %}
          <h2 class="section-title">{{ options.sucla_featured_cats_title }}</h2>
        {% endif %}

        {% assign featured_cats_layout = options.sucla_featured_cats_layout | default: 'grid' %}
        {% assign num_categories = featured_categories_data.size %}
        {% assign featured_cats_min_carousel = options.sucla_featured_cats_min_carousel | default: 2 %}

        {% if featured_cats_layout == 'carousel' and num_categories > featured_cats_min_carousel %}
          {% comment %} Layout Carrusel para Categorías {% endcomment %}
          <div class="swiper featured-categories-swiper">
            <div class="swiper-wrapper">
              {% for cat_data in featured_categories_data %}
                {% assign parts = cat_data | split: '||' %}
                {% assign image_url = parts[0] %}
                {% assign image_alt = parts[1] %}
                {% assign title = parts[2] %}
                {% assign link = parts[3] %}
                <div class="swiper-slide category-slide">
                  <a href="{{ link }}" class="category-card-link">
                    <div class="category-card">
                      <div class="category-image-wrapper">
                        <img
                          src="{{ image_url }}"
                          alt="{{ image_alt }}"
                          class="category-image"
                          loading="lazy"
                        >
                      </div>
                      <div class="category-title-overlay">
                        <h3 class="category-card-title">{{ title }}</h3>
                      </div>
                    </div>
                  </a>
                </div>
              {% endfor %}
            </div>
            <div class="swiper-pagination featured-categories-pagination"></div>
            <div class="swiper-button-prev featured-categories-prev"></div>
            <div class="swiper-button-next featured-categories-next"></div>
          </div>
        {% else %}
          {% comment %} Layout Grid para Categorías {% endcomment %}
          {% assign grid_cols = num_categories | at_most: 4 %}
          <div class="featured-categories-grid grid-cols-{{ grid_cols }}">
            {% for cat_data in featured_categories_data limit: grid_cols %}
               {% assign parts = cat_data | split: '||' %}
               {% assign image_url = parts[0] %}
               {% assign image_width = parts[1] %}
               {% assign image_height = parts[2] %}
               {% assign title = parts[3] %}
               {% assign link = parts[4] %}
              <a href="{{ link }}" class="category-card-link">
                <div class="category-card">
                  <div class="category-image-wrapper">
                    <img
                      src="{{ image_url }}"
                      alt="{{ title }}"
                      class="category-image"
                      loading="lazy"
                      {% if image_width != blank %}width="{{ image_width }}"{% endif %}
                      {% if image_height != blank %}height="{{ image_height }}"{% endif %}
                    >
                  </div>
                   <div class="category-title-overlay">
                     <h3 class="category-card-title">{{ title }}</h3>
                   </div>
                </div>
              </a>
            {% endfor %}
          </div>
        {% endif %}
      </div>
    </section>
  {% endif %}


  {% comment %} ================= CARRUSEL DE PRODUCTOS (Ej: Novedades) ================= {% endcomment %}
  {% assign product_carousel_enabled = options.sucla_product_carousel_enabled | default: true %}
  {% if product_carousel_enabled %}
    {% assign product_carousel_collection_handle = options.sucla_product_carousel_collection | default: 'all' %}
    {% comment %} Debug de colección {% endcomment %}
    {% if debug_enabled %}
      <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 4px;">
        <p><strong>Debug Colección:</strong></p>
        <ul style="margin: 0; padding-left: 20px;">
          <li>Handle solicitado: {{ product_carousel_collection_handle }}</li>
          <li>Colecciones disponibles: 
            {% for collection in collections %}
              {{ collection.handle }}{% unless forloop.last %}, {% endunless %}
            {% endfor %}
          </li>
        </ul>
      </div>
    {% endif %}

    {% assign product_carousel_collection = collections[product_carousel_collection_handle] %}
    {% assign product_carousel_limit = options.sucla_product_carousel_limit | default: 8 %}

    {% if product_carousel_collection != blank %}
      <section class="product-carousel-section">
        <div class="section-container" style="max-width: {{ max_width_style }};">
          {% if options.sucla_product_carousel_title != blank %}
            <h2 class="section-title">{{ options.sucla_product_carousel_title }}</h2>
          {% endif %}

          {% if product_carousel_collection.products_count > 0 %}
            <div class="swiper product-carousel-swiper">
              <div class="swiper-wrapper">
                {% for product in product_carousel_collection.products limit: product_carousel_limit %}
                  <div class="swiper-slide product-slide">
                    <div class="container">
                      {% include 'product_card' with product %}
                    </div>
                  </div>
                {% endfor %}
              </div>
              <div class="swiper-pagination product-carousel-pagination"></div>
              <div class="swiper-button-prev product-carousel-prev"></div>
              <div class="swiper-button-next product-carousel-next"></div>
            </div>
          {% else %}
            {% if debug_enabled %}
              <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 4px;">
                <p><strong>Error:</strong> La colección está vacía (no tiene productos)</p>
                <p>Colección: {{ product_carousel_collection.name }}</p>
                <p>Handle: {{ product_carousel_collection.handle }}</p>
              </div>
            {% endif %}
          {% endif %}

          {% assign collection_url = product_carousel_collection.url %}
          {% if options.sucla_product_carousel_view_all_link != blank %}
            {% assign collection_url = options.sucla_product_carousel_view_all_link %}
          {% endif %}
          {% assign product_carousel_show_view_all_button = options.sucla_product_carousel_show_view_all_button | default: true %}
          {% if collection_url != blank and product_carousel_show_view_all_button %}
            <div class="section-view-all">
              {% assign view_all_text_default = 'Ver todo en %{collection_name}' | t: collection_name: product_carousel_collection.name %}
              {% assign view_all_text = options.sucla_product_carousel_view_all_text | default: view_all_text_default %}
              <a href="{{ collection_url }}" class="view-all-button">
                {{ view_all_text }}
              </a>
            </div>
          {% endif %}
        </div>
      </section>
    {% else %}
      {% if debug_enabled %}
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 4px;">
          <p><strong>Error:</strong> No se encontró la colección</p>
          <p>Handle buscado: {{ product_carousel_collection_handle }}</p>
        </div>
      {% endif %}
    {% endif %}
  {% endif %}

  {% comment %} ==================== BANNER PROMOCIONAL / INFORMATIVO ==================== {% endcomment %}
  {% assign promo_banner_enabled = options.sucla_promo_banner_enabled | default: false %}
  {% if promo_banner_enabled and options.sucla_promo_banner_image != blank %}
    <section class="promo-banner-section">
      <div class="section-container promo-container-fullwidth" style="padding-left: 0; padding-right: 0; max-width: none;">
        <div class="promo-banner-wrapper">
           {% assign promo_banner_alt_text = options.sucla_promo_banner_alt_text | default: options.sucla_promo_banner_title | default: 'Promoción' | escape %}
           {% assign promo_image = options.sucla_promo_banner_image %}
           {% assign promo_image_url = promo_image.url %}
           <img
             src="{{ promo_image_url }}"
             alt="{{ promo_image.alt | default: promo_banner_alt_text }}"
             class="promo-banner-image"
             loading="lazy"
           >
           <div class="promo-banner-overlay">
             <div class="promo-banner-content" style="max-width: {{ max_width_style }};">
                {% if options.sucla_promo_banner_title != blank %}
                  <h2 class="promo-banner-title">{{ options.sucla_promo_banner_title }}</h2>
                {% endif %}
                {% if options.sucla_promo_banner_subtitle != blank %}
                  <p class="promo-banner-subtitle">{{ options.sucla_promo_banner_subtitle }}</p>
                {% endif %}
                {% if options.sucla_promo_banner_cta_text != blank and options.sucla_promo_banner_cta_link != blank %}
                  <a href="{{ options.sucla_promo_banner_cta_link }}" class="promo-banner-button">
                    {{ options.sucla_promo_banner_cta_text }}
                  </a>
                {% endif %}
             </div>
           </div>
        </div>
      </div>
    </section>
  {% endif %}

  {% comment %} ==================== BLOQUES DE CONTENIDO (Hasta 3) ==================== {% endcomment %}
  {% assign content_blocks_enabled = options.sucla_content_blocks_enabled | default: false %}
  {% assign content_blocks_data = "" | split: "," %}

  {% if content_blocks_enabled %}
    {% for i in (1..3) %}
      {% capture image_option %}sucla_block_{{ i }}_image{% endcapture %}
      {% capture title_option %}sucla_block_{{ i }}_title{% endcapture %}
      {% capture text_option %}sucla_block_{{ i }}_text{% endcapture %}
      {% capture link_option %}sucla_block_{{ i }}_link{% endcapture %}
      {% capture link_text_option %}sucla_block_{{ i }}_link_text{% endcapture %}

      {% assign block_image = options[image_option] %}
      {% assign block_title = options[title_option] %}
      {% assign block_text = options[text_option] %}
      {% assign block_link = options[link_option] %}
      {% assign link_text_default = 'Leer más' | t %}
      {% assign link_text = options[link_text_option] | default: link_text_default %}

      {% if block_image != blank %}
         {% assign img_url = block_image.url | default: block_image %}
         {% assign img_width = block_image.width %}
         {% assign img_height = block_image.height %}
         
         {% assign block_title_to_use = block_title | default: "Bloque " | append: i %}
         {% assign block_title_escaped = block_title_to_use | escape %}
         {% assign block_text_escaped = block_text | escape %}
         {% assign link_text_escaped = link_text | escape %}
         
         {% assign data_part1 = img_url | append: '||' | append: img_width %}
         {% assign data_part2 = data_part1 | append: '||' | append: img_height %}
         {% assign data_part3 = data_part2 | append: '||' | append: block_title_escaped %}
         {% assign data_part4 = data_part3 | append: '||' | append: block_text_escaped %}
         {% assign data_part5 = data_part4 | append: '||' | append: block_link %}
         {% assign data_string = data_part5 | append: '||' | append: link_text_escaped %}
         
         {% assign content_blocks_data = content_blocks_data | push: data_string %}
      {% endif %}
    {% endfor %}
  {% endif %}

  {% if content_blocks_enabled and content_blocks_data.size > 0 %}
    <section class="content-blocks-section">
        <div class="section-container" style="max-width: {{ max_width_style }};">
            {% if options.sucla_content_blocks_title != blank %}
              <h2 class="section-title">{{ options.sucla_content_blocks_title }}</h2>
            {% endif %}
            <div class="content-blocks-grid grid-cols-{{ content_blocks_data.size }}">
                {% for block_data in content_blocks_data %}
                    {% assign parts = block_data | split: '||' %}
                    {% assign image_url = parts[0] %}
                    {% assign image_width = parts[1] %}
                    {% assign image_height = parts[2] %}
                    {% assign title = parts[3] %}
                    {% assign text = parts[4] %}
                    {% assign link = parts[5] %}
                    {% assign link_text = parts[6] %}

                    <div class="content-block-item">
                        <div class="content-block-image-wrapper">
                            <img
                                src="{{ image_url }}"
                                alt="{{ title }}"
                                class="content-block-image"
                                loading="lazy"
                                {% if image_width != blank %}width="{{ image_width }}"{% endif %}
                                {% if image_height != blank %}height="{{ image_height }}"{% endif %}
                            >
                        </div>
                        <div class="content-block-text">
                            <h3 class="content-block-title">{{ title }}</h3>
                            {% if text != blank %}<p class="content-block-description">{{ text }}</p>{% endif %}
                            {% if link != blank %}
                              <a href="{{ link }}" class="content-block-link">{{ link_text }}</a>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </section>
  {% endif %}


  {% comment %} ==================== INSTAGRAM FEED (Opcional) ==================== {% endcomment %}
  {% assign instagram_enabled = options.sucla_instagram_enabled | default: false %}
  {% assign insta_images_data = "" | split: "," %}

  {% if instagram_enabled %}
    {% for i in (1..6) %} {% comment %} Permitir hasta 6 imágenes {% endcomment %}
      {% capture image_setting %}sucla_instagram_image_{{ i }}{% endcapture %}
      {% assign insta_img_obj = options[image_setting] %}
      {% if insta_img_obj != blank %}
        {% assign img = insta_img_obj %}
        {% assign img_url = img.url %}
        {% assign img_alt = img.alt | default: 'Instagram Post' | t %}
        
        {% assign data_string = img_url | append: '||' | append: img_alt %}
        
        {% assign insta_images_data = insta_images_data | push: data_string %}
      {% endif %}
    {% endfor %}
  {% endif %}

  {% assign insta_link = '#' %}
  {% if options.sucla_instagram_username != blank %}
      {% assign insta_link = 'https://instagram.com/' | append: options.sucla_instagram_username %}
  {% endif %}

  {% if instagram_enabled and insta_images_data.size > 0 %}
    <section class="instagram-feed-section">
      <div class="section-container" style="max-width: {{ max_width_style }};">
        {% if options.sucla_instagram_title != blank %}
          <h2 class="section-title">{{ options.sucla_instagram_title }}</h2>
        {% endif %}
        {% if options.sucla_instagram_username != blank %}
           <p class="instagram-username">
              <a href="{{ insta_link }}" target="_blank" rel="noopener noreferrer">
                @{{ options.sucla_instagram_username }}
              </a>
           </p>
        {% endif %}

        {% assign insta_grid_cols = insta_images_data.size | at_most: 6 %}
        <div class="instagram-grid grid-cols-{{ insta_grid_cols }}">
          {% for img_data_str in insta_images_data %}
            {% assign parts = img_data_str | split: '||' %}
            {% assign image_url = parts[0] %}
            {% assign image_width = parts[1] %}
            {% assign image_height = parts[2] %}
            <div class="instagram-item">
              <a href="{{ insta_link }}" target="_blank" rel="noopener noreferrer" class="instagram-link">
                <img
                  src="{{ image_url }}"
                  alt="{% t 'Instagram Post' %}"
                  class="instagram-image"
                  loading="lazy"
                  {% if image_width != blank %}width="{{ image_width }}"{% endif %}
                  {% if image_height != blank %}height="{{ image_height }}"{% endif %}
                >
                <div class="instagram-overlay-icon">
                   {% comment %} Instagram Icon SVG {% endcomment %}
                   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>
                </div>
              </a>
            </div>
          {% endfor %}
        </div>
      </div>
    </section>
  {% endif %}

</div>

{% comment %} ==================== ESTILOS CSS EMBEBIDOS ==================== {% endcomment %}
<style>
  :root {
    /* Variables CSS - Intenta usar colores de las options si existen */
    --hero-home-max-width: {{ options.sucla_general_max_width | default: '1660px' }};
    --hero-home-text-color: {{ options.sucla_color_text | default: '#333333' }};
    --hero-home-primary-color: {{ options.sucla_color_primary | default: '#007bff' }};
    --hero-home-headings-color: {{ options.sucla_color_headings | default: 'var(--hero-home-text-color)' }};
    --hero-home-light-text-color: #ffffff;
    --hero-home-section-padding-y: {{ options.sucla_section_padding_y | default: '3rem' }};
    --hero-home-section-padding-x: {{ options.sucla_section_padding_x | default: '1rem' }};
    --hero-home-section-padding: var(--hero-home-section-padding-y) var(--hero-home-section-padding-x);
    --hero-home-gap: {{ options.sucla_grid_gap | default: '1.5rem' }}; /* Espacio entre elementos en grids */
    --hero-home-font-body: {{ options.sucla_font_body | default: 'sans-serif' }};
    --hero-home-font-headings: {{ options.sucla_font_headings | default: 'serif' }};
    --hero-home-border-radius: {{ options.sucla_border_radius | default: '4px' }};

    /* Swiper specific vars */
    --swiper-theme-color: var(--hero-home-primary-color); /* Color principal para paginación activa */
    --swiper-navigation-color: {{ options.swiper_nav_color | default: 'var(--hero-home-text-color)' }};
    --swiper-navigation-size: {{ options.swiper_nav_icon_size | default: '18px' }};
    --swiper-pagination-color: var(--swiper-theme-color);
    --swiper-pagination-bullet-inactive-color: {{ options.swiper_pag_bullet_color | default: 'var(--hero-home-text-color)' }};
    --swiper-pagination-bullet-inactive-opacity: 0.3;
    --swiper-pagination-bullet-size: {{ options.swiper_pag_bullet_size | default: '10px' }};
    --swiper-pagination-bullet-horizontal-gap: 5px;
  }

  .hero-home-partial-wrapper {
    color: var(--hero-home-text-color);
    font-family: var(--hero-home-font-body);
    line-height: 1.6;
    overflow-x: hidden; /* Evita barras de scroll horizontales */
  }

  .section-container {
    max-width: var(--hero-home-max-width);
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--hero-home-section-padding-x);
    padding-right: var(--hero-home-section-padding-x);
  }

  /* --- Estilos Comunes --- */
  .section-title {
    font-size: clamp(1.6rem, 4vw, 2.2rem); /* Tamaño fluido */
    font-weight: {{ options.sucla_headings_font_weight | default: '600' }};
    text-align: center;
    margin-bottom: 2.5rem;
    color: var(--hero-home-headings-color);
    font-family: var(--hero-home-font-headings);
    line-height: 1.3;
  }
   .section-view-all {
    text-align: center;
    margin-top: 2.5rem;
  }
  .view-all-button {
    display: inline-block;
    padding: 0.7rem 1.8rem;
    border: 1px solid var(--hero-home-text-color);
    color: var(--hero-home-text-color);
    background-color: transparent;
    text-decoration: none;
    border-radius: var(--hero-home-border-radius);
    transition: background-color 0.3s, color 0.3s;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.03em;
  }
  .view-all-button:hover {
    background-color: var(--hero-home-text-color);
    color: var(--hero-home-light-text-color);
  }

  /* --- Hero Banner --- */
  .hero-banner-section {
    margin-bottom: var(--hero-home-section-padding-y);
    position: relative; /* Contexto para elementos internos */
  }
  .hero-banner-container {
    padding: 0;
    max-width: none !important;
    position: relative;
  }
  .hero-content-wrapper {
    position: relative;
    width: 100%;
    min-height: {{ options.sucla_hero_min_height_vh | default: '65vh' }}; /* Altura mínima con opción */
    max-height: 85vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background-color: #f0f0f0; /* Placeholder color */
  }
  .hero-banner-image {
    position: absolute;
    top: 0; left: 0;
    width: 100%; height: 100%;
    object-fit: cover;
    object-position: {{ options.sucla_hero_image_position | default: 'center' }};
  }
  .hero-banner-placeholder {
    position: absolute; top: 0; left: 0;
    width: 100%; height: 100%;
    background-color: #e0e0e0;
  }
  .hero-banner-overlay {
    position: absolute; /* Cubre toda la imagen */
    top: 0; left: 0; width: 100%; height: 100%;
    z-index: 2;
    background-color: {{ options.sucla_hero_overlay_color | default: 'rgba(0, 0, 0, 0.35)' }};
    padding: var(--hero-home-section-padding-x);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  .hero-text-content {
     color: {{ options.sucla_hero_text_color | default: 'var(--hero-home-light-text-color)' }};
     width: 100%; /* Ocupa el ancho del overlay padding */
     padding: clamp(1rem, 5vw, 3rem) 0; /* Padding vertical interno */
  }
  .hero-title {
    font-size: clamp(2.2rem, 6vw, 3.5rem);
    font-weight: {{ options.sucla_hero_title_weight | default: '700' }};
    margin-bottom: 1rem;
    line-height: 1.2;
    text-shadow: 1px 1px 4px rgba(0,0,0,0.6);
    font-family: var(--hero-home-font-headings);
    color: {{ options.sucla_hero_title_color | default: 'currentColor' }};
  }
  .hero-subtitle {
    font-size: clamp(1rem, 2.5vw, 1.2rem);
    margin-bottom: 2rem;
    opacity: 0.95;
    max-width: 700px; /* Limita ancho del subtítulo */
    margin-left: auto; margin-right: auto; /* Centra */
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
    color: {{ options.sucla_hero_subtitle_color | default: 'currentColor' }};
  }
  .hero-cta-button {
    display: inline-block;
    padding: 0.9rem 2.2rem;
    background-color: {{ options.sucla_hero_cta_bg_color | default: 'var(--hero-home-primary-color)' }};
    color: {{ options.sucla_hero_cta_text_color | default: 'var(--hero-home-light-text-color)' }};
    border: none;
    border-radius: var(--hero-home-border-radius);
    font-size: clamp(0.9rem, 2vw, 1.1rem);
    font-weight: 600;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
  }
  .hero-cta-button:hover {
    background-color: {{ options.sucla_hero_cta_bg_hover_color | default: 'color-mix(in srgb, var(--hero-home-primary-color) 85%, black)' }};
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
  }

  /* --- Categorías Destacadas --- */
  .featured-categories-section {
    padding: var(--hero-home-section-padding);
  }
  .featured-categories-grid {
    display: grid;
    gap: var(--hero-home-gap);
  }
  /* Grid responsivo inteligente */
  .featured-categories-grid.grid-cols-1 { grid-template-columns: 1fr; }
  .featured-categories-grid.grid-cols-2 { grid-template-columns: repeat(auto-fit, minmax(min(100%/2, 250px), 1fr)); }
  .featured-categories-grid.grid-cols-3 { grid-template-columns: repeat(auto-fit, minmax(min(100%/3, 250px), 1fr)); }
  .featured-categories-grid.grid-cols-4 { grid-template-columns: repeat(auto-fit, minmax(min(100%/4, 220px), 1fr)); }

  .category-card-link { text-decoration: none; display: block; color: inherit; height: 100%; }
  .category-card {
    position: relative;
    overflow: hidden;
    border-radius: var(--hero-home-border-radius);
    transition: box-shadow 0.3s ease, transform 0.3s ease;
    background-color: #fff;
    height: 100%; /* Asegura misma altura en grid */
    display: flex; /* Para control interno */
    flex-direction: column;
  }
   .category-card:hover {
     transform: translateY(-5px);
     box-shadow: 0 10px 25px rgba(0,0,0,0.1);
   }
  .category-image-wrapper {
    aspect-ratio: {{ options.sucla_category_aspect_ratio | default: '4 / 3' }};
    background-color: #f5f5f5;
    overflow: hidden;
    position: relative; /* Para overlay */
  }
  .category-image {
    display: block;
    width: 100%; height: 100%;
    object-fit: cover;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .category-card:hover .category-image {
    transform: scale(1.08);
  }
  .category-title-overlay {
    position: absolute;
    bottom: 0; left: 0; right: 0;
    padding: 1rem;
    background: linear-gradient(to top, rgba(0,0,0,0.7) 10%, rgba(0,0,0,0));
    text-align: center;
    transition: background 0.3s ease;
    z-index: 2; /* Encima de la imagen */
  }
   .category-card:hover .category-title-overlay {
       background: linear-gradient(to top, rgba(0,0,0,0.8) 20%, rgba(0,0,0,0));
   }
  .category-card-title {
    color: var(--hero-home-light-text-color);
    font-size: clamp(1rem, 2.5vw, 1.2rem);
    font-weight: 600;
    margin: 0;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.7);
  }

  /* Estilos para carrusel de categorías */
  .featured-categories-swiper {
    padding-bottom: 3.5rem;
    position: relative;
    margin-left: calc(var(--hero-home-gap) / -2); /* Compensación visual para spacing */
    margin-right: calc(var(--hero-home-gap) / -2);
  }
  .featured-categories-swiper .swiper-slide {
     height: auto;
     padding-left: calc(var(--hero-home-gap) / 2);
     padding-right: calc(var(--hero-home-gap) / 2);
     display: flex;
  }
   .featured-categories-swiper .category-card {
       width: 100%;
   }

  /* --- Carrusel de Productos --- */
  .product-carousel-section {
     padding: var(--hero-home-section-padding);
     background-color: {{ options.sucla_product_carousel_bg_color | default: '#f9f9f9' }};
     border-top: 1px solid #eee;
     border-bottom: 1px solid #eee;
     margin-top: var(--hero-home-section-padding-y); /* Espacio antes */
     margin-bottom: var(--hero-home-section-padding-y); /* Espacio después */
  }
  .product-carousel-swiper {
    padding-bottom: 3.5rem;
    position: relative;
    margin-left: calc(var(--hero-home-gap) / -2);
    margin-right: calc(var(--hero-home-gap) / -2);
  }
  .product-carousel-swiper .swiper-slide {
    height: auto;
    display: flex;
    padding-left: calc(var(--hero-home-gap) / 2);
    padding-right: calc(var(--hero-home-gap) / 2);
  }
   /* Los estilos específicos de product-card deberían estar en Partials/product_card.liquid */
   /* Aquí solo añadimos contexto del carrusel si es necesario */
  .product-carousel-swiper .product-card {
     width: 100%;
     margin-bottom: 0;
  }

  /* --- Banner Promocional --- */
  .promo-banner-section {
    padding: 0;
    margin-top: var(--hero-home-section-padding-y);
    margin-bottom: var(--hero-home-section-padding-y);
    position: relative;
  }
   .promo-container-fullwidth {
       padding-left: 0 !important;
       padding-right: 0 !important;
       max-width: none !important;
   }
  .promo-banner-wrapper {
    position: relative;
    overflow: hidden;
    min-height: {{ options.sucla_promo_banner_min_height | default: '400px' }};
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e0e0e0; /* Placeholder */
  }
  .promo-banner-image {
    position: absolute;
    top: 0; left: 0; width: 100%; height: 100%;
    object-fit: cover;
    object-position: {{ options.sucla_promo_banner_image_position | default: 'center' }};
  }
  .promo-banner-overlay {
    position: absolute;
    top: 0; left: 0; width: 100%; height: 100%;
    z-index: 2;
    background-color: {{ options.sucla_promo_banner_overlay_color | default: 'rgba(0, 0, 0, 0.45)' }};
    padding: var(--hero-home-section-padding-x);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  .promo-banner-content {
     color: {{ options.sucla_promo_banner_text_color | default: 'var(--hero-home-light-text-color)' }};
     max-width: var(--hero-home-max-width);
     width: 90%;
     padding: clamp(1rem, 5vw, 3rem) 0;
  }
  .promo-banner-title {
    font-size: clamp(1.8rem, 5vw, 2.8rem);
    font-weight: {{ options.sucla_promo_banner_title_weight | default: '600' }};
    margin-bottom: 0.8rem;
    line-height: 1.2;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.6);
    font-family: var(--hero-home-font-headings);
  }
  .promo-banner-subtitle {
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 650px;
    margin-left: auto; margin-right: auto;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
  }
  .promo-banner-button {
    display: inline-block;
    padding: 0.8rem 2rem;
    background-color: {{ options.sucla_promo_banner_cta_bg_color | default: 'transparent' }};
    color: {{ options.sucla_promo_banner_cta_text_color | default: 'var(--hero-home-light-text-color)' }};
    border: 2px solid {{ options.sucla_promo_banner_cta_border_color | default: 'var(--hero-home-light-text-color)' }};
    border-radius: var(--hero-home-border-radius);
    font-size: clamp(0.9rem, 2vw, 1rem);
    font-weight: 600;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.2s ease;
  }
  .promo-banner-button:hover {
    background-color: {{ options.sucla_promo_banner_cta_bg_hover_color | default: 'var(--hero-home-light-text-color)' }};
    color: {{ options.sucla_promo_banner_cta_text_hover_color | default: 'var(--hero-home-text-color)' }};
    border-color: {{ options.sucla_promo_banner_cta_border_hover_color | default: options.sucla_promo_banner_cta_bg_hover_color | default: 'var(--hero-home-light-text-color)' }};
    transform: translateY(-2px);
  }

  /* --- Bloques de Contenido --- */
  .content-blocks-section {
     padding: var(--hero-home-section-padding);
     background-color: {{ options.sucla_content_blocks_bg_color | default: '#ffffff' }};
  }
  .content-blocks-grid {
    display: grid;
    gap: var(--hero-home-gap);
  }
   /* Grid responsivo inteligente */
  .content-blocks-grid.grid-cols-1 { grid-template-columns: 1fr; }
  .content-blocks-grid.grid-cols-2 { grid-template-columns: repeat(auto-fit, minmax(min(100%/2, 300px), 1fr)); }
  .content-blocks-grid.grid-cols-3 { grid-template-columns: repeat(auto-fit, minmax(min(100%/3, 280px), 1fr)); }

  .content-block-item {
     text-align: center;
     background-color: #fff;
     border-radius: var(--hero-home-border-radius);
     overflow: hidden;
     transition: transform 0.3s ease, box-shadow 0.3s ease;
     border: 1px solid {{ options.sucla_content_blocks_border_color | default: '#eeeeee' }};
     height: 100%;
     display: flex;
     flex-direction: column;
  }
   .content-block-item:hover {
       transform: translateY(-6px);
       box-shadow: 0 10px 30px rgba(0,0,0,0.1);
   }
  .content-block-image-wrapper {
      aspect-ratio: {{ options.sucla_block_aspect_ratio | default: '16 / 9' }};
      background-color: #f5f5f5;
      overflow: hidden;
  }
  .content-block-image {
      display: block; width: 100%; height: 100%; object-fit: cover;
      transition: transform 0.4s ease;
  }
  .content-block-item:hover .content-block-image {
      transform: scale(1.04);
  }
  .content-block-text {
      padding: 1.5rem 1.2rem;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
  }
  .content-block-title {
      font-size: clamp(1.1rem, 3vw, 1.4rem);
      font-weight: 600;
      margin-bottom: 0.8rem;
      font-family: var(--hero-home-font-headings);
      color: var(--hero-home-headings-color);
  }
  .content-block-description {
      font-size: 0.95rem;
      line-height: 1.6;
      opacity: 0.85;
      margin-bottom: 1rem;
      flex-grow: 1;
  }
  .content-block-link {
      display: inline-block;
      margin-top: auto;
      padding-top: 0.5rem;
      color: var(--hero-home-primary-color);
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
  }
  .content-block-link:hover {
      color: color-mix(in srgb, var(--hero-home-primary-color) 80%, black);
      text-decoration: underline;
  }

  /* --- Instagram Feed --- */
  .instagram-feed-section {
    padding: var(--hero-home-section-padding);
    background-color: {{ options.instagram_bg_color | default: '#fafafa' }};
  }
  .instagram-username {
      text-align: center;
      margin-top: -1.8rem;
      margin-bottom: 2rem;
      font-size: 1.1rem;
  }
  .instagram-username a {
      text-decoration: none;
      color: var(--hero-home-primary-color);
      font-weight: 500;
      transition: color 0.3s ease;
  }
   .instagram-username a:hover {
       color: color-mix(in srgb, var(--hero-home-primary-color) 80%, black);
   }
  .instagram-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: {{ options.sucla_instagram_gap | default: '0.5rem' }};
    margin: 0 auto;
  }
   /* Grid responsivo inteligente */
  .instagram-grid.grid-cols-1 { grid-template-columns: 1fr; }
  .instagram-grid.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .instagram-grid.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .instagram-grid.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .instagram-grid.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
  .instagram-grid.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

  .instagram-item {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1 / 1;
    border-radius: var(--hero-home-border-radius);
    background-color: #e0e0e0;
  }
  .instagram-link { display: block; width: 100%; height: 100%; }
  .instagram-image {
    display: block; width: 100%; height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease, filter 0.4s ease;
  }
   .instagram-item:hover .instagram-image {
       transform: scale(1.1);
       filter: brightness(0.7) saturate(1.1);
   }
  .instagram-overlay-icon {
    position: absolute;
    top: 50%; left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    color: white;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
    pointer-events: none;
  }
   .instagram-item:hover .instagram-overlay-icon {
       opacity: 1;
       transform: translate(-50%, -50%) scale(1);
   }
   .instagram-overlay-icon svg {
       width: 32px; height: 32px;
       filter: drop-shadow(0 2px 4px rgba(0,0,0,0.6));
   }

  /* --- Estilos Swiper (Usando variables CSS de Swiper) --- */
  .swiper {
      padding: 0;
  }
  .swiper-button-prev,
  .swiper-button-next {
    width: {{ options.swiper_nav_size | default: '44px' }};
    height: {{ options.swiper_nav_size | default: '44px' }};
    background-color: {{ options.swiper_nav_bg | default: 'rgba(255, 255, 255, 0.9)' }};
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
    transition: background-color 0.3s, opacity 0.3s, box-shadow 0.3s;
    border: none;
    top: 50%;
    transform: translateY(calc(-50% - 1rem));
  }
   .swiper-button-prev:hover,
   .swiper-button-next:hover {
       background-color: {{ options.swiper_nav_bg_hover | default: '#ffffff' }};
       box-shadow: 0 5px 12px rgba(0,0,0,0.2);
   }
   .swiper-button-disabled {
       opacity: 0.35;
       cursor: not-allowed;
       box-shadow: none;
   }
   .swiper-button-prev { left: 15px; }
   .swiper-button-next { right: 15px; }
   .swiper-button-prev::after,
   .swiper-button-next::after {
     font-size: var(--swiper-navigation-size);
     font-weight: bold;
     color: var(--swiper-navigation-color);
   }

  .swiper-pagination {
    position: absolute;
    bottom: 8px;
  }
  .swiper-pagination-bullet {
    background-color: var(--swiper-pagination-bullet-inactive-color);
    opacity: var(--swiper-pagination-bullet-inactive-opacity);
    width: var(--swiper-pagination-bullet-size);
    height: var(--swiper-pagination-bullet-size);
    transition: opacity 0.3s, background-color 0.3s, transform 0.3s;
  }
   .swiper-pagination-bullet:hover {
       opacity: calc(var(--swiper-pagination-bullet-inactive-opacity) + 0.2);
       transform: scale(1.1);
   }
  .swiper-pagination-bullet-active {
    background-color: var(--swiper-pagination-color);
    opacity: 1;
    transform: scale(1.1);
  }

  @media (max-width: 1024px) {
      .swiper-button-prev { left: 10px; }
      .swiper-button-next { right: 10px; }
  }

  @media (max-width: 767px) {
    :root {
        --hero-home-section-padding-y: 2.5rem;
        --hero-home-gap: 1.2rem;
    }
    .section-title { font-size: 1.5rem; margin-bottom: 1.8rem;}
    .hero-title { font-size: clamp(1.8rem, 7vw, 2.5rem); }
    .hero-subtitle { font-size: clamp(0.9rem, 3.5vw, 1.1rem); margin-bottom: 1.5rem; }
    .hero-cta-button { padding: 0.8rem 1.8rem; }
    .hero-content-wrapper { min-height: {{ options.sucla_hero_min_height_vh_mobile | default: '55vh' }}; }
    .hero-banner-overlay { padding: 1.5rem var(--hero-home-section-padding-x); }
    .hero-text-content { padding: 1.5rem 0; }

    .featured-categories-grid.grid-cols-3,
    .featured-categories-grid.grid-cols-4 {
      grid-template-columns: repeat(auto-fit, minmax(min(100%/2, 150px), 1fr));
    }
    .content-blocks-grid.grid-cols-2,
    .content-blocks-grid.grid-cols-3 {
      grid-template-columns: 1fr;
    }
     .instagram-grid.grid-cols-4,
     .instagram-grid.grid-cols-5,
     .instagram-grid.grid-cols-6 {
         grid-template-columns: repeat(auto-fit, minmax(min(100%/3, 90px), 1fr));
     }

    .promo-banner-title { font-size: clamp(1.6rem, 6vw, 2rem); }
    .promo-banner-subtitle { font-size: clamp(0.9rem, 3vw, 1rem); }
    .promo-banner-button { padding: 0.7rem 1.6rem;}

     .swiper-button-prev, .swiper-button-next {
         width: 38px; height: 38px;
         transform: translateY(calc(-50% - 0.5rem));
     }
     .swiper-button-prev { left: 5px; }
     .swiper-button-next { right: 5px; }
     .swiper-button-prev::after, .swiper-button-next::after { font-size: 16px; }
     .swiper-pagination-bullet { width: 8px; height: 8px; }
     .swiper-pagination { bottom: 5px !important; }
  }

</style>

{% comment %} ==================== JAVASCRIPT EMBEBIDO (Swiper Init) ==================== {% endcomment %}
<script>
document.addEventListener('DOMContentLoaded', function () {
  const initSwiper = (selector, options) => {
    const element = document.querySelector(selector);
    if (element && element.querySelector('.swiper-slide')) {
      try {
        if (element.swiper) {
          element.swiper.destroy(true, true);
        }
        return new Swiper(selector, options);
      } catch (e) {
        console.error('Error initializing Swiper for:', selector, e);
        return null;
      }
    }
    return null;
  };

  const commonSwiperOptions = {
    watchOverflow: true,
    grabCursor: true,
    pagination: { clickable: true },
    navigation: true,
    keyboard: { enabled: true },
  };

  const featuredCatsOptions = {
    ...commonSwiperOptions,
    loop: {{ options.sucla_featured_cats_loop | default: false }},
    slidesPerView: {{ options.sucla_featured_cats_slides_mobile | default: 2 }},
    spaceBetween: {{ options.sucla_featured_cats_spacing | default: 15 }},
    pagination: {
      el: '.featured-categories-pagination',
      clickable: true,
    },
    navigation: {
      nextEl: '.featured-categories-next',
      prevEl: '.featured-categories-prev',
    },
    breakpoints: {
      768: {
        slidesPerView: {{ options.sucla_featured_cats_slides_tablet | default: 3 }},
        spaceBetween: {{ options.sucla_featured_cats_spacing_tablet | default: 20 }},
      },
      1024: {
        slidesPerView: {{ options.sucla_featured_cats_slides_desktop | default: 4 }},
        spaceBetween: {{ options.sucla_featured_cats_spacing_desktop | default: 25 }},
      },
      1280: {
        slidesPerView: {{ options.sucla_featured_cats_slides_large | default: num_categories | at_most: 5 }},
        spaceBetween: {{ options.sucla_featured_cats_spacing_desktop | default: 25 }},
      }
    }
  };

  const productCarouselOptions = {
    ...commonSwiperOptions,
    loop: {{ options.sucla_product_carousel_loop | default: false }},
    slidesPerView: {{ options.sucla_product_carousel_slides_mobile | default: 2 }},
    spaceBetween: {{ options.sucla_product_carousel_spacing | default: 15 }},
    pagination: {
      el: '.product-carousel-pagination',
      clickable: true,
    },
    navigation: {
      nextEl: '.product-carousel-next',
      prevEl: '.product-carousel-prev',
    },
    breakpoints: {
      640: {
        slidesPerView: {{ options.sucla_product_carousel_slides_sm_tablet | default: 3 }},
        spaceBetween: {{ options.sucla_product_carousel_spacing_sm_tablet | default: 15 }},
      },
      768: {
        slidesPerView: {{ options.sucla_product_carousel_slides_tablet | default: 4 }},
        spaceBetween: {{ options.sucla_product_carousel_spacing_tablet | default: 20 }},
      },
      1024: {
        slidesPerView: {{ options.sucla_product_carousel_slides_desktop | default: 5 }},
        spaceBetween: {{ options.sucla_product_carousel_spacing_desktop | default: 25 }},
      },
      1280: {
          slidesPerView: {{ options.sucla_product_carousel_slides_large | default: 6 }},
          spaceBetween: {{ options.sucla_product_carousel_spacing_desktop | default: 25 }},
      }
    }
  };

  const initializeAllSwipers = () => {
    initSwiper('.featured-categories-swiper', featuredCatsOptions);
    initSwiper('.product-carousel-swiper', productCarouselOptions);
  };

  initializeAllSwipers();

  if (typeof Jumpseller !== 'undefined' && typeof Jumpseller.livepreview !== 'undefined') {
    let debounceTimeout;
    Jumpseller.livepreview.on('change', function(option, value) {
      const relevantOptionsPrefixes = ['sucla_', 'featured_cats_', 'product_carousel_', 'swiper_'];
      const isRelevant = relevantOptionsPrefixes.some(prefix => option.startsWith(prefix));

      if (isRelevant) {
        clearTimeout(debounceTimeout);
        debounceTimeout = setTimeout(() => {
          initializeAllSwipers();
        }, 300);
      }
    });
  }

});
</script>