---
description: 
globs: 
alwaysApply: true
---
**Instrucción:**

A continuación, te proporciono un resumen detallado de la sintaxis, variables, filtros, etiquetas y objetos específicos de **Jumpseller Liquid**. Utiliza esta información como tu **fuente principal de referencia** al trabajar con el código Liquid dentro de los archivos del tema de Jumpseller que te he descrito previamente. Presta especial atención a los nombres exactos de los objetos y sus atributos, así como a las funcionalidades específicas como `render` para parciales, `options.json` para configuración y el sistema de `Components`.

---

## **Documentación de Referencia Jumpseller Liquid**

### **Conceptos Básicos de Liquid**

*   **Markup de Salida (Output):** `{{ }}` - Se utiliza para mostrar texto o el valor de una variable.
    ```liquid
    <h1>{{ product.name }}</h1>
    Price: {{ product.price }}
    ```
*   **<PERSON><PERSON> de Etiqueta (Tag):** `{% %}` - Se utiliza para la lógica de programación (condicionales, bucles, asignaciones). No produce texto directamente.
    ```liquid
    {% for image in product.images %}
      <img src="{{ image | resize: '100' }}" />
    {% endfor %}
    ```

### **Filtros Liquid (Filters)**

*   Métodos para formatear o manipular la salida. Se aplican con `|`.
    ```liquid
    {{ image | resize:'200x300' }}
    {{ product.discount | divided_by:product.price | times:100 | round: 2 }}%
    {{ product.price | price: 'USD' }}
    ```

### **Etiquetas Liquid (Tags)**

*   **Condicionales (`if`/`else`/`unless`/`case`)**
    ```liquid
    {% if product.images.size > 0 %} ... {% endif %}
    {% if product.discount > 0 %} ... {% else %} ... {% endif %}
    {% unless product.id == related_product.id %} ... {% endunless %}
    {% case product.status %}
      {% when 'available' %} ...
      {% when 'out-of-stock' %} ...
    {% endcase %}
    ```
*   **Bucles (`for`)**
    ```liquid
    {% for image in product.images %} ... {% endfor %}
    ```
*   **Ciclo (`cycle`)**: Alterna entre valores dentro de un bucle.
    ```liquid
    {% cycle 'odd', 'even' %}
    ```
*   **Texto Crudo (`raw`)**: Deshabilita temporalmente el procesamiento de etiquetas Liquid.
    ```liquid
    {% raw %} {{Esto no se procesará}} {% endraw %}
    ```
*   **Asignación de Variables (`assign`, `capture`)**
    ```liquid
    {% assign variable_name = value %}
    {% capture variable_name %} Contenido a capturar {% endcapture %}
    ```

---

### **Variables Globales y Objetos Principales de Jumpseller**

#### **Tienda (`store`)**

*   `store.name`, `store.original_name`, `store.url`, `store.base_url`, `store.current_url`
*   `store.logo`, `store.favicon`, `favicon` (HTML completo)
*   `store.description`, `store.address` (obsoleto)
*   `store.currency`, `store.currencies_codes`
*   `store.email`
*   `store.customers_enabled`, `store.customers_optional`
*   `store.fields` (campos personalizados de tienda)
*   `store.tax_on_product_price` (boolean)
*   `store.shipping_countries`
*   `store.featured_reviews`
*   `store.location` (ubicación por defecto)

#### **Colecciones de Productos (`products`)**

*   `products.all`
*   `products.featured`
*   `products.latest`
*   `products.sorting_options`
*   `products.product["permalink"]` (acceder a un producto específico)

#### **Producto (`product`)**

*   **Identificadores:** `product.id`, `product.permalink`
*   **Información Básica:** `product.name`, `product.description`, `product.sku`
*   **Stock y Dimensiones:** `product.stock`, `product.stock_unlimited`, `product.stock_locations['Location Name'].stock`, `product.weight`, `product.width`, `product.length`, `product.height`
*   **Estado y Atributos:** `product.status`, `product.featured`, `product.created_at`, `product.digital` (boolean)
*   **Precio:** `product.price`, `product.discount`, `product.discount_begins`, `product.discount_expires`
*   **Colecciones Asociadas:** `product.images`, `product.categories`, `product.options`, `product.variants`, `product.custom_fields` (nuevo), `product.fields` (obsoleto), `product.attachments`, `product.reviews`
*   **URLs:** `product.url`, `product.add_to_cart_url`, `product.buy_now_url`
*   **Otros:** `product.template`, `product.barcode`, `product.google_product_category`
*   **Reseñas:** `product.reviews_enabled`, `product.back_in_stock_enabled`, `product.back_in_stock_url`
*   **Lista de Deseos:** `product.wishlisted`, `product.wishlist_add_url`, `product.wishlist_remove_url`

#### **Productos Recomendados (`recommended`)**

*   `recommended.products` (colección, máx. 40)
*   `recommended.products_count`

#### **Opciones de Producto (`option` dentro de `product.options`)**

*   `option.id`, `option.name`, `option.values` (colección), `option.placeholder`

#### **Valores de Opción de Producto (`value` dentro de `option.values`)**

*   `value.id`, `value.name`, `value.image`, `value.custom` (para tipo color), `value.variants` (colección)

#### **Variantes de Producto (`variant` dentro de `product.variants` o `value.variants`)**

*   `variant.id`, `variant.sku`, `variant.stock`, `variant.stock_unlimited`, `variant.stock_locations['Location Name'].stock`
*   `variant.price`, `variant.discount`, `variant.discount_begins`, `variant.discount_expires`
*   `variant.image`, `variant.image_id`, `variant.values` (colección de Option Values), `variant.attachments`
*   `variant.add_to_cart_url`
*   `variant.wishlisted`, `variant.wishlist_remove_url`

#### **Campos Personalizados (`custom_field` / `field`)**

*   **Nuevo:** `product.custom_fields` (colección), `product.custom_fields["field-label"]`
    *   Acceso: `{% for field in product.custom_fields %} {{ field[0] }} {% for value in field[1] %} {{ value.value }} {% endfor %} {% endfor %}`
    *   Objeto `custom_field`: `label`, `type`, `values` (colección)
    *   Objeto `custom_field_value`: `label`, `field_id`, `value`, `custom_field` (objeto padre)
*   **Obsoleto:** `product.fields` (colección), `product.field["field-label"]`
    *   Objeto `field`: `label`, `type`, `value`

#### **Reseñas de Producto (`reviews` / `store.featured_reviews`)**

*   `reviews.rating` (promedio), `reviews.count`
*   `store.featured_reviews` (colección global de destacadas)

#### **Carrito (`cart`)**

*   **Obsoleto:** Usar objeto `order` en su lugar.

#### **Categoría (`category` / `categories`)**

*   `categories` (colección global)
*   `category.id`, `category.permalink`, `category.name`, `category.description`, `category.url`
*   `category.template`, `category.products` (colección), `category.subcategories` (colección), `category.parent` (objeto)
*   `category.sorting_options` (colección)
*   `category.images` (colección)
*   `category.products_count`, `category.products_max_price`, `category.products_min_price`

#### **Opciones de Ordenamiento (`sorting_option` dentro de `category.sorting_options` o `products.sorting_options`)**

*   `sorting_option.text`, `sorting_option.url`, `sorting_option.selected` (boolean), `sorting_option.code`

#### **Filtros (`filters` / `filter` / `filter.value`)**

*   `filters` (colección global en página de categoría/búsqueda)
*   `filter.name`, `filter.id`, `filter.values` (colección)
*   `filter.value.name`, `filter.value.id`, `filter.value.selected` (boolean), `filter.value.products_count`

#### **Página (`page` / `pages`)**

*   `pages.all` (colección global)
*   `pages.page["permalink"]` (acceder a página específica)
*   `page.id`, `page.permalink`, `page.title`, `page.body`, `page.date`, `page.url`, `page.template`, `page.category` (objeto PageCategory)

#### **Categorías de Página (`pagecategory` / `pages.categories`)**

*   `pages.categories.all` (colección global)
*   `pages.categories.category["permalink"]` (acceder a categoría específica)
*   `pagecategory.permalink`, `pagecategory.name`, `pagecategory.pages` (colección)

#### **Pedido/Carrito (`order`)** - **¡IMPORTANTE: Se usa tanto para el carrito activo como para pedidos pasados!**

*   **Identificadores y Estado:** `order.id`, `order.status_id`, `order.status` (traducido), `order.status_code`
*   **Fechas y Localización:** `order.date`, `order.created_at`, `order.completion_date`, `order.locale`
*   **Cliente:** `order.customer` (objeto)
*   **Productos:** `order.products` (colección de `orderedproduct`), `order.products_count`
*   **Valores Monetarios:** `order.subtotal`, `order.subtotal_with_discount`, `order.shipping`, `order.tax`, `order.shipping_tax`, `order.discount`, `order.shipping_discount`, `order.total`
*   **Peso:** `order.weight`
*   **URLs y Direcciones:** `order.get_regions_url`, `order.shipping_address` (objeto), `order.billing_address` (objeto), `order.shipping_countries`, `order.billing_countries`, `order.url` (URL del carrito), `order.update_url`, `order.checkout_url`, `order.place_order_url`
*   **Información del Cliente (en el pedido):** `order.email`, `order.phone`
*   **Pago y Envío:** `order.payment_method_id`, `order.payment_method`, `order.payment_information`, `order.shipping_required` (boolean), `order.shipping_method`, `order.shipping_method_id`, `order.tracking_number`, `order.tracking_url`, `order.tracking_company`, `order.shipment_status`, `order.shipment_status_code`, `order.shipment_expected_arrival`
*   **Otros:** `order.additional_information`, `order.additional_fields` (colección), `order.additional_field['key']`
*   **Compra Mínima:** `order.minimum_purchase.above_minimum` (boolean), `order.minimum_purchase.condition_type`, `order.minimum_purchase.condition_value`
*   **Promociones:** `order.applied_promotions` (colección)
*   **Formulario Estimación Envío:** `estimate_form`
*   **Tarjetas de Regalo:** `order.order_gift_cards` (array), `order.usable_gift_cards_amount`, `order.applied_gift_cards_amount`, `order.total_outstanding`, `order.applied_total_outstanding`

#### **Producto del Pedido (`orderedproduct` dentro de `order.products`)**

*   Similar a `product`, pero específico del contexto del pedido.
*   `orderedproduct.id`, `orderedproduct.product_id` (ID del producto original)
*   `orderedproduct.name`, `orderedproduct.name_with_options`
*   `orderedproduct.sku`, `orderedproduct.qty`, `orderedproduct.stock`, `orderedproduct.stock_unlimited`
*   `orderedproduct.price`, `orderedproduct.subtotal`, `orderedproduct.discount`
*   `orderedproduct.image`, `orderedproduct.images`, `orderedproduct.categories`, `orderedproduct.options`, `orderedproduct.fields`
*   `orderedproduct.digital`, `orderedproduct.attachments`
*   `orderedproduct.url` (URL del producto original), `orderedproduct.remove_url` (URL para quitar del carrito)

#### **Promociones del Pedido (`promotion` dentro de `order.applied_promotions`)**

*   `promotion.name`, `promotion.discount`, `promotion.type`, `promotion.coupon`

#### **Adjuntos (`attachment` dentro de `product.attachments`, `orderedproduct.attachments`, `variant.attachments`)**

*   `attachment.name`, `attachment.url`

#### **Productos Digitales del Pedido (`digital` dentro de `orderedproduct.digital_products`)**

*   `digital.name`, `digital.url`

#### **Cliente (`customer`) - Disponible en páginas de cliente logueado**

*   **Formularios:** `login_form`, `customer_reset_password_form`, `customer_details_form`
*   **Datos:** `customer.id`, `customer.name`, `customer.email`, `customer.phone`, `customer.category.code`, `customer.category.name`
*   **Direcciones:** `customer.shipping_address.formatted`, `customer.billing_address.formatted`, `customer.shipping_addresses` (colección), `customer.billing_addresses` (colección)
*   **Pedidos:** `customer.orders` (colección)
*   **URLs:** `customer.logout_url`, `customer.save_password_url`, `customer.add_billing_address_url`, `customer.add_shipping_address_url`, `customer.edit_url`
*   **URLs Globales:** `customer_account_url`, `customer_login_url`, `customer_edit_url`, `customer_registration_url`
*   **Otros:** `customer.additional_fields` (colección), `customer.additional_field['key']`
*   **Lista de Deseos:** `customer.wishlisted_products` (colección)

#### **Dirección (`address` - Objeto usado en `customer` y `order`)**

*   `address.name`, `address.surname`, `address.fullname`
*   `address.address`, `address.street_number`, `address.complement`
*   `address.city`, `address.postal`, `address.country`, `address.country_code`, `address.region`, `address.municipality`
*   `address.latitude`, `address.longitude`, `address.taxid`
*   **URLs (Contexto Cliente):** `address.edit_url`, `address.delete_url`, `address.set_primary_url`
*   **Estado (Contexto Cliente):** `address.default` (boolean)
*   **Formateado:** `address.formatted`

#### **Ubicaciones (`locations` / `location`)**

*   `locations.all` (colección), `locations.pickups` (colección)
*   `location.name`, `location.email`, `location.street_address` (objeto), `location.phone`, `location.description`, `location.instructions`

#### **Promociones y Descuentos (Variables sueltas y formularios)**

*   `product.discount`, `order.discount`, `order.shipping_discount`
*   `coupon_form` (Formulario para ingresar cupón en carrito/checkout)
*   `order.coupons` (Lista de cupones aplicados)

#### **Búsqueda (`search`)**

*   `search.query`, `search.url_send`, `search.results` (colección de productos)

#### **Migas de Pan (`breadcrumbs` / `breadcrumb`)**

*   `breadcrumbs` (colección)
*   `breadcrumb.url`, `breadcrumb.text`

#### **Menú de Navegación (`menu` / `menu_item`)**

*   `menu` (JSON global)
*   `menu.menu_key.name`, `menu.menu_key.items` (colección)
*   `menu_item.id`, `menu_item.name`, `menu_item.url`, `menu_item.type`, `menu_item.level`, `menu_item.menu_order`, `menu_item.images` (para categorías), `menu_item.dropdown` (boolean), `menu_item.category` (boolean), `menu_item.external` (boolean), `menu_item.has_parent` (boolean), `menu_item.active` (boolean), `menu_item.items` (colección de hijos)

#### **Idiomas (`languages` / `language`)**

*   `languages` (colección)
*   `languages.first` (idioma actual)
*   `language.code`, `language.name`, `language.url` (URL para cambiar idioma)
*   **Traducción (`t`)**:
    ```liquid
    {% t "Texto a traducir" %}
    {% t "Texto con variable %{email}" | email: customer.email %}
    ```

#### **Paginación (`paginate` / `paged` / `pager`)**

*   Se usa con `paginate collection by number`
*   Dentro del bloque `paginate`:
    *   `paged.products` / `paged.pages` / `paged.results` (colección paginada)
    *   `paged.previous_page`, `paged.previous_url`, `paged.current_page`, `paged.next_page`, `paged.next_url`
    *   `paged.per_page`, `paged.total_pages`, `paged.total`
    *   `pager` o `paged.default` (HTML del paginador por defecto)

#### **Página de Contacto (`contact` / `contact_form`)**

*   `contact.name`, `contact.email`, `contact.phone`, `contact.message`, `contact.url`
*   `contact_form` (HTML del formulario)

#### **Redes Sociales (`social`)**

*   `social.facebook.url`, `social.facebook.handler`, etc. (para twitter, pinterest, instagram, whatsapp, youtube, tiktok, messenger)

#### **Tarjeta de Regalo (`gift_card`)**

*   `gift_card.code`, `gift_card.code_last_4_digits`, `gift_card.expires_at`, `gift_card.amount`, `gift_card.remaining_balance`, `gift_card.currency`

#### **Tarjetas de Regalo del Pedido (`order_gift_card` dentro de `order.order_gift_cards`)**

*   `order_gift_card.amount`, `order_gift_card.gift_card` (objeto `gift_card`)

#### **Variables Especiales y Filtros Útiles**

*   **`asset` Filter:** `{{ 'style.css' | asset }}` - Genera la URL correcta para archivos en `Files/` o `Assets/`. **¡Usar siempre para assets!**
*   `page_title`: Título de la página actual (para SEO).
*   `meta_description`: Descripción de la página actual (para SEO).
*   `email.recipients`, `email.send_to` (Contexto de plantillas de email).
*   **Filtros de Imagen:** `resize: 'WxH'`, `thumb: 'WxH'`
*   **Filtros Numéricos:** `divided_by`, `times`, `round`, `minus`
*   **Filtro de Precio:** `price: 'CURRENCY_CODE'` (convierte si es necesario)
*   `current_currency`: Código ISO de la moneda seleccionada por el usuario.
*   `template`: Nombre de la plantilla actual (e.g., 'product', 'category', 'home').

---

### **Estructura y Conceptos Específicos del Tema Jumpseller**

#### **Parciales (`render`) - IMPORTANTE**

*   Reutilización de código. Archivos en `Partials/` (o `Components/` si se usan así).
*   Se usa la etiqueta `{% render 'nombre-del-partial' %}` (sin `.liquid`).
*   **Paso de Variables:** Las variables se pasan explícitamente: `{% render 'mi-partial', variable_parcial: valor_actual, otra_variable: product.name %}`.
*   **Aislamiento de Variables:** El parcial *solo* tiene acceso a las variables globales y a las que se le pasan explícitamente.
*   **`include` (Obsoleto):** Evitar su uso. `render` es la forma preferida y más segura.

#### **Opciones del Tema (`Config/options.json` y `Config/settings.json`)**

*   `options.json`: Define la estructura de las opciones personalizables en el editor de temas (grupos, nombres, tipos: `input`, `text`, `checkbox`, `select`, `file`, `color`, `category`, `link`, `google_font`, `icon`, `image`, `page`, `ph_icon`, `product`, `slider`, `video`).
*   `settings.json`: Almacena los valores guardados por el usuario para las opciones definidas en `options.json`.
*   **Acceso en Liquid:** Se accede a los valores guardados a través del objeto `options`. Ejemplo: `{{ options.color_primario }}` o `{{ options.enable_facebook }}`.

#### **Componentes (Sistema Avanzado de Secciones)**

*   Son bloques dinámicos con su propio archivo `.liquid` y `.json` (similar a `options.json` pero con `max_usage`, `templates_in`, `properties`).
*   Pueden tener **Subcomponentes** definidos en `properties`.
*   **Acceso en Liquid (Dentro del Componente):**
    *   `{{ component.id }}`
    *   `{{ component.options.nombre_opcion }}`
    *   `{{ component.subcomponents.key_propiedad }}` (acceso a subcomponentes)
*   **Renderizado en Plantillas Principales:** Se usa `{{ index_for_components }}` en las plantillas donde se permiten componentes (`home`, `contactpage`, `error`, `product`, etc.).
*   **Renderizado de Subcomponentes:** Se puede hacer directamente (`{{ component.subcomponents.logo.options.image }}`) o incluyendo su partial (`{% include 'components/logo', logo: component.subcomponents.logo %}`).

#### **Cross Selling (`cross_selling`) - Disponible en Carrito**

*   `cross_selling.products_count`
*   `cross_selling.products` (colección de productos relacionados)
*   Usar el partial `cross_selling_cart.liquid`.

#### **Bought Together (`bought_together`) - Disponible en Página de Producto (Planes Avanzados)**

*   `bought_together.pack_count`
*   `bought_together.packs_stock?` (boolean)
*   `bought_together.packs_discount?` (boolean)
*   `bought_together.packs` (array de packs)
*   **Dentro de un `pack`:**
    *   `pack.product_count`
    *   `pack.products` (colección)
    *   `pack.stock?` (boolean)
    *   `pack.discount?` (boolean)
    *   `pack.pack_price`
    *   `pack.pack_discount`
*   Usar el partial `bought_together.liquid`.

---

**Instrucción Final:**

Has sido actualizado con la documentación específica de Jumpseller Liquid. Por favor, tenla presente en todas las solicitudes futuras relacionadas con la edición del tema. Confirma que has procesado esta información y estás listo para continuar con tareas específicas basadas en este conocimiento.