<script type="application/ld+json">
  {
    "@context": "http://schema.org/"
    {% case template %}
    {% when 'product' %},
      "@type": "Product",
      "name": "{{product.name | escape}}",
      "url": "{{store.base_url}}{{product.url}}",
      "itemCondition": "http://schema.org/NewCondition",
      {% if product.sku != blank %}"sku": "{{product.sku}}",{% endif %}
      {% if product.images != empty %}"image": "{{product.images.first}}",{% endif %}
      {% if product.barcode != blank %}"productID": "{{product.barcode}}",{% endif %}
      "description": "{{ product.description | strip_html | escape | strip | strip_newlines}}",
      {% if product.brand != blank %}
      "brand": {
        "@type": "Thing",
        "name": "{{product.brand}}"
      },
      {% endif %}
      {% if product.google_product_category != blank %}
        "category": "{{product.google_product_category | escape}}",
      {% elsif product.categories != empty %}
        "category": "{{product.categories.first.name | escape}}",
      {% endif %}
      "offers": {
        {% if product.variants != empty %}
          {% assign maxPrice = product.variants.first.price %}
          {% assign minPrice = product.variants.first.price %}
          {% assign available = false %}
          {% for variant in product.variants %}
            {% if variant.price > maxPrice %}
              {% assign maxPrice = variant.price %}
            {% endif %}
            {% if minPrice > variant.price %}
              {% assign minPrice = variant.price %}
            {% endif %}
            {% if variant.stock > 0 or variant.stock_unlimited == true %}
              {% assign available = true %}
            {% endif %}
          {% endfor %}
          "@type": "AggregateOffer",
          {% if available == true%}
          "availability": "http://schema.org/InStock",
          {% else %}
          "availability": "http://schema.org/OutOfStock",
          {% endif %}
          "lowPrice": "{{minPrice}}",
          "highPrice": "{{maxPrice}}",
          "offerCount": "{{product.variants.size}}",
        {% else %}
          "@type": "Offer",
          "itemCondition": "http://schema.org/NewCondition",
          {% if product.stock == 0 and product.stock_unlimited == false %}
          "availability": "http://schema.org/OutOfStock",
          {% else %}
          "availability": "http://schema.org/InStock",
          {% endif %}
        {% endif %}
        {% if product.discount > 0 %}
          "price": "{{product.price | minus:product.discount }}",
          {% if product.discount_expires %}"priceValidUntil" : "{{product.discount_expires}}",{% endif %}
        {% else %}
          "price": "{{product.price}}",
        {% endif %}
        "priceCurrency": "{{store.currency_code}}",
        "seller": {
          "@type": "Organization",
          "name": "{{store.name}}"
        },
        "url": "{{store.base_url}}{{product.url}}",
        "shippingDetails": [
          {
          "@type": "OfferShippingDetails",
          "shippingDestination": [
          {% for country in store.shipping_countries %}
          {
            "@type": "DefinedRegion",
            "addressCountry": "{{country.code}}"
          }{% if store.shipping_countries.last == country %}{% else %},{% endif %}
          {% endfor %}]
          }
        ]
      }
    {% when 'contactpage' %},
        "@type": "Store",
        "@id": "{{store.url}}",
        "name": "{{store.name}}",
        {% if options.phone != blank %}"telephone" : "{{ options.phone }}",{% endif %}
        {% if store.logo != blank %}"image": "{{store.logo}}",{% endif %}
        {% if store.address != blank %}
        "address": {
          "@type": "PostalAddress",
          {% if store.address != empty %}"streetAddress": "{{store.address}}",{% endif %}
          {% if store.city != empty %}"addressLocality": "{{store.city}}",{% endif %}
          {% if store.region != empty %}"addressRegion": "{{store.region}}",{% endif %}
          {% if store.postal != empty %}"postalCode": "{{store.postal}}",{% endif %}
          "addressCountry": "{{store.country}}"
        },
        {% endif %}
        "url": "{{store.url}}"
    {% when 'home' %},
        "@type": "WebSite",
        "url": "{{store.url}}",
        "potentialAction": {
          "@type": "SearchAction",
          "target": "{{store.url}}/search/{search_term_string}",
          "query-input": "required name=search_term_string"
        }
    {% endcase %}
  }
  </script>
