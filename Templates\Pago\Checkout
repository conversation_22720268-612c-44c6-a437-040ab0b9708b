<div class="checkout-content">
  <div class="container">
    <!-- Breadcrumb Checkout -->
    <div class="row">
      <div class="col-12 col-md-8 mr-auto ml-auto d-flex breadcrumb-cart">
        <div class="item complete">
          <a id="cart-link" href="{{order.url}}"><span>1</span></a>
          <a id="cart-link" href="{{order.url}}">{% t "Cart" %}</a>
        </div>
        <div class="item active">
          <a href="{{order.checkout_url}}"><span>2</span></a>
          <a href="{{order.checkout_url}}">{% t "Checkout" %}</a>
        </div>
        <div class="item">
          <span>3</span>
          <a href="#">{% t "Review" %}</a>
        </div>
        <div class="item">
          <span>4</span>
          <a href="#">{% t "Success" %}</a>
        </div>
      </div>
    </div>
    <!-- Page Heading -->
    <div class="row">
      <div class="col-12">
        <h1 class="page-header">{% t "Checkout" %}</h1>
      </div>
    </div>
    <div class="row">
      <!-- Checkout Container -->
      <div class="checkout-container col-12 col-lg-8 mb-2">
        {{checkout_form}}
        {% if options.currencies != blank and options.open_exchange_rates_token != blank %}
        {% assign store_currency = store.currency_code %}
        <p style="text-align: center; font-size: smaller; font-style: italic;">{% t "Payment will be processed in %{store_currency} at the current exchange rate" | store_currency: {{ store_currency }} %}</p>
        {% endif %}

      </div>
      <div id="order-summary" class="col-12 col-lg-4">
        <div class="card summary mb-3">
          <div class="card-header">
            <h2 class="card-title">{% t "Order Summary" %}</h2>
          </div>
          <div class="card-body">
            <!--AQUI ESTAMOS sobre la foto del pedido -->
            <div id="cart-update-form">
              <div class="cart-table">
                <table class="table">
                  {% for ordered_product in order.products %}
                  <tr>
                    <td>
                      <a href="{{ ordered_product.url }}" class="trsn" title="{{ordered_product.name | escape}}">
                        {% if ordered_product.image %}
                        <img src="{{ ordered_product.image | resize:'120x150'}}" alt="{{ordered_product.name | escape}}" title="{{ordered_product.name | escape}}">
                        {% else %}
                        <img src="{{ 'no-image-cart.jpg' | asset }}" alt="{{ordered_product.name | escape}}">
                        {% endif %}
                      </a>
                    </td>
                    <td colspan="2">
                      <h3>{{ordered_product.name}}</h3>
                      {% for option in ordered_product.options %}
                      <p><strong>{{option.name}}:</strong> {{option.value}}</p>
                      {% endfor %}
                      <div class="price mt-3">
                        {% if ordered_product.discount > 0 %}
                        <div class="cart-product-discount">
                          {{ordered_product.qty}} x <span class="order-product-price-amount">
                            {{ ordered_product.price | minus:ordered_product.discount | price }}
                          </span>
                          <br>
                          <small class="order-product-price-amount">({{ ordered_product.price | price }})</small>
                        </div>
                        {% else %}
                        {{ordered_product.qty}} x <span class="order-product-price-amount">{{ ordered_product.price | price }}</span>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </table>
                <table class="table">
                  {% if order.subtotal != order.total %}
                  <tr class="totals">
                    <td colspan="1" class="text-left">{% t "Subtotal" %}</td>
                    <td colspan="1" class="text-right"><span class="order-product-price-amount">{{order.subtotal | price }}</span></td>
                  </tr>
                  {% endif %}

                  {% if order.tax > 0 and store.tax_on_product_price != true %}
                  <tr class="totals">
                    <td colspan="1" class="text-left">{% t "Tax" %}</td>
                    <td colspan="1" class="text-right"><span class="order-product-price-amount">{{order.tax | price }}</span></td>
                  </tr>
                  {% endif %}

                  {% if order.discount > 0 %}
                  <tr class="totals">
                    <td colspan="1" class="text-left">{% t "Discount" %}</td>
                    <td colspan="1" class="text-right">-<span class="order-product-price-amount">{{order.discount | price }}</span></td>
                  </tr>
                  {% endif %}

                  <tr class="totals key">
                    <td colspan="1" class="text-left">
                      <strong>{% t "Total" %}</strong>
                    </td>
                    <td colspan="1" class="text-right">
                      <strong><span class="order-product-price-amount">{{order.total | price }}</span></strong>
                    </td>
                  </tr>

                </table>
                <div class="cart-actions text-center">
                  <input type="submit" value="{% t 'Review Order' %}" class="btn btn-block btn-primary" id="submit_review_order_2">
                  <ul class="list-inline small mb-0 mt-2">
                    <li class="list-inline-item">
                      <a href="{{store.url}}/cart" title="{% t 'Edit Cart' %}">{% t 'Edit Cart' %}</a>
                    </li>
                    <li class="list-inline-item">|</li>
                    <li class="list-inline-item">
                      <a href="{{store.url}}" title="{% t 'Continue Shopping' %}">{% t 'Continue Shopping' %}</a>
                    </li>
                  </ul>
                </div>
                
              </div>
              
            </div>
             <!--AQUI ESTAMOS dentro de la caja del resumen del producto -->
          </div>
          <!--AQUI ESTAMOS dentro de la caja del resumen del producto-->
        </div>
         <!--AQUI ESTAMOS DEBAJO DEL DETALLE DEL PRODUCTO -->
      </div>
    </div>
  </div>
  <script>
  document.getElementById("submit_review_order_2").addEventListener("click", function () {
    document.getElementById("submit_review_order").click();
  });
</script>
  <!-- Estimate shipping values on the checkout page -->
  <script>
    function cleanEstimates() {
      // remove all prices and errors
      $('#shipping_options li').each(function() {
        $(this).children().last().detach();
      });

      // add empty messages - placeholders
      $('#shipping_options li').each(function() {
        $(this).append('<span></span>')
      });
    }

    function shippingEstimates() {
      if ($('#order_shipping_address_country').val() != "" && $('#order_shipping_address_region').val() != "") {
        $.ajax({
          method: "POST",
          url: "/checkout/shipping_estimate",
          data: {
            estimate: {
              country: $('#order_shipping_address_country').val(),
              region: $('#order_shipping_address_region').val(),
              municipality: $('#order_shipping_address_municipality').val(),
              postal: $('#order_shipping_address_postal').val(),
              city: $('#order_shipping_address_city').val(),
              address: $('#order_shipping_address_address').val(),
            }
          }
        }).done(function(data) {
          for(var i = 0; i < data.length; i++) {
            let amount;
            let old_currency;
            let new_currency;
            let currency_amount;
            if(typeof(accounting) != "undefined"){
              old_currency = sessionStorage.getItem('store_currency');
              new_currency = $.trim(sessionStorage.getItem('global_currency'));
              if(old_currency == new_currency){
                currency_amount = data[i].table.price;
              } else {
                amount = accounting.unformat(data[i].table.price, i18n_decimal_mark);
                currency_amount = accounting.formatMoney(
                  fx.convert(amount, { from: old_currency, to: new_currency }),
                  { symbol: { EUR: "€", GBP: "₤" }[new_currency] }
                )
              }
            }

            let shipping_method = $('#shipping_options #order_shipping_method_' + data[i].table.id);

            // remove any previous messages & placeholders
            shipping_method.parent().children().last().detach();

            if(data[i].table.error){
              // disable options with errors
              shipping_method.attr('disabled', 'disabled');
              shipping_method.prop('checked', false);

              // add error messages
              shipping_method.parent().append("<p class='shipping_information'><i>" + data[i].table.error_message + "</i></p>")
            } else {
              // enable options
              shipping_method.attr('disabled', false);
              if($("#shipping_options").find("input[type='radio']:checked").not("[disabled]").length == 0) { shipping_method.prop('checked', true); }

              // add formatted shipping prices
              shipping_method.parent().append("<p class='shipping_information'><i>" + (typeof(accounting) != "undefined" ? currency_amount : data[i].table.price) + "</i></p>")
            }
          }
          // Disable Review Order Button if Invalid Shipping Method
          function CheckShippingMethods(){
            if($("#shipping_options").find("input[type='radio']:checked").not("[disabled]").length > 0) {
              $("#submit_review_order_2").prop("disabled", false );
            }
            else {
              $("#submit_review_order_2").prop("disabled", true);
            }
          }
          $(document).ready(CheckShippingMethods);
          $("#shipping_options input[type='radio']").on('change', CheckShippingMethods);
      });
      } else {
        // no Country or Region filled, clear shipping estimate info
        cleanEstimates();
      }
    }

    $('#order_shipping_address_country').change(function() {
      shippingEstimates()
    });
    $('#order_shipping_address_region').change(function() {
      shippingEstimates()
    });
    $('#order_shipping_address_municipality').change(function() {
      shippingEstimates()
    });
    $('#order_shipping_address_address').change(function() {
      shippingEstimates()
    });
    $(document).ready(function() {
      // add empty messages - placeholders
      $('#shipping_options li').each(function() {
        $(this).append('<span></span>')
      });
      shippingEstimates();
    })
  </script>
  
