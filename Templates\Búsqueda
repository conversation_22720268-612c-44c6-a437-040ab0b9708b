<div class="container">
 <!-- Page Heading -->
  <div class="row">
    <div class="col-12 text-center">
      <h1 class="page-header">{% t "Search Results" %}</h1>
    </div>
  </div>
  {% if search.results == empty and filters == empty %}

  <!-- /.row -->
  <div class="row">
    <section class="col-lg-6 offset-lg-3 text-center">
      <h6 class="mb-3">{% t "There aren't any products that matches your search. Try searching again" %}</h6>
      <form id="searchpage_search-form" class="search-form" method="get" action="{{search.url_send}}">
        <input type="text" value="{{search.query}}" name="q" class="form-control" onFocus="javascript:this.value=''" placeholder="{% t 'Search for products' %}" />
        <button type="submit"><i class="fas fa-search"></i></button>
      </form>
    </section>
    {% if options.fetured_on_search and products.featured != empty %}
    <div class="col-12 text-center mb-3 mt-4">
      <hr>
      <h4 class="font-weight-bold pt-3">{% t "You might also like" %}</h4>
    </div>
    {% for product in products.featured limit:6 %}
    <div class="col-lg-2 col-md-4 col-6 product-block mb-4 mt-3 px-2">
      {% include 'list_product' with product %}
    </div>
    {% endfor %}
    {% endif %}
    <div class="col-12 text-center mt-3">
      <p>{% t "You do not see what you are looking for?" %} <br><a class="btn btn-primary mt-2" href="{{ contact.url }}">{% t "Contact Us" %}</a></p>
    </div>
  </div>
  {% else %}

  <!-- /.row -->
  {% paginate search.results by options.products_search_limit %}
<div class="row">
    <!-- filter mobile -->
    <div class="col-12 d-md-none text-center mb-3">
      <span>{% t "You've searched for" %}:</span> <strong>{{search.query}}</strong>
    </div>
    <div class="{% if filters != empty %}col-6{% else %}col-12{% endif %} d-md-none">
      <div class="row justify-content-between mt-2 text-center">
        <div class="col-12 product-qty mb-2">
          <strong>{{paged.total}} {% t "Product(s)" %}</strong>
        </div>
      </div>
    </div>
    {% if filters != empty %}
    <div class="col-6 d-md-none">
      <button id="show_filters" type="button" class="btn btn-outline-secondary btn-filter btn-block my-0 mb-3 d-md-none">
        <i class="fas fa-sliders-h"></i> {% t "Filters" %} <span class="badge badge-light"></span>
      </button>
    </div>
    {% endif %}
    <!-- end filter mobile -->
    {% if filters != empty %}
    <div class="col-lg-3 col-md-4">
      {% include 'category_filter' %}
    </div>
    {% endif %}
    <div class="{% if filters != empty %}col-lg-9 col-md-8{% else %}col-12{% endif %}">
      <div class="row justify-content-between mb-4 {% if filters != empty %}mt-4{% endif %}">
        <div class="col-md-5 product-qty d-none d-md-block">
          <strong>{{paged.total}} {% t "Product(s)" %}</strong>
        </div>
        <div class="{% if filters != empty %}col-lg-6 col-md-6{%else%}col-lg-5 col-md-7{% endif %} d-none d-md-block text-right">
          <span>{% t "You've searched for" %}:</span> <strong>{{search.query}}</strong>
        </div>
      </div>
      <div class="row mb-md-5 mb-4 mx-n2">
        {% for product in paged.products %}
        <div class="{% if filters != empty %}col-lg-3{% else %}col-md-3{% endif %} col-6 product-block mb-4 px-2">
          {% include 'list_product' with product %}
        </div>
        {% endfor %}
        {% if filters != empty and paged.total == 0 %}
        <section class="col-12 text-center px-2">
          <div class="card">
            <div class="card-body">
              <h5>{% t "There aren't any products available with the selected filters" %}.</h5>
              <a href="#" class="all mt-4 button btn btn-primary" title="{% t 'Clear filters' %}" onclick="window.location = window.location.href.split('?')[0];">
                {% t "Clear filters" %}
                <i class="fas fa-eraser"></i>
              </a>
            </div>
          </div>
        </section>
        {% endif %}
        <div class="clearfix"></div>
        <div class="col-12 product-gallery-pager">
          {{pager}}
        </div>
      </div>
    </div>
  </div>

  {% endpaginate %}
  {% endif %}
</div>
