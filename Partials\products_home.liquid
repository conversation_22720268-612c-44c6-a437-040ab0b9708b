{% case options.products_home %}
{% when "featured-home" or "latest-and-featured-home"  %}

{% if products.featured != empty %}
<!-- Products Section -->
<div class="row">
  <div class="col-12">
    <h2 class="page-header">{% t 'Featured Products' %}</h2>
  </div>

  {% if products.all == empty %}
  {% assign placeholders = "1,2,3,4,5,6" | split: "," %}
  {% for placeholder in placeholders %}
  <div class="col-md-4 col-6 product-block">
    <img class="img-fluid img-portfolio img-hover" src="{{ 'no-image-home.jpg' | asset }}" alt="{% t 'Add your products' %}">
    <div class="caption">
      <h3><a href="{{store.url}}/admin/products/new">{% t 'Add your products' %}</a></h3>
      <h6>Brand</h6>
      <div class="price-mob">{{ 0.0 | price }}</div>
        <div class="clearfix"></div>
    </div>
  </div>
  {% endfor %}
  {%else%}
  <!-- Featured Products -->
  {% include 'features-products-home' %}
{% endif %}
</div><!-- /.row -->
{% endif %}

{% when "latest-home" or "latest-and-featured-home" %}
<!-- Products Section -->
<div class="row">
  <div class="col-12">
    <h2 class="page-header">{% t 'Latest Products' %}</h2>
  </div>

  {% if products.all == empty %}
  {% assign placeholders = "1,2,3,4,5,6" | split: "," %}
  {% for placeholder in placeholders %}
  <div class="col-md-4 col-6 product-block">
    <img class="img-fluid img-portfolio img-hover" src="{{ 'no-image-home.jpg' | asset }}" alt="{% t 'Add your products' %}">
    <div class="caption">
      <h3><a href="{{store.url}}/admin/products/new">{% t 'Add your products' %}</a></h3>
      <h6>Brand</h6>
      <div class="price-mob">{{ 0.0 | price }}</div>
        <div class="clearfix"></div>
    </div>
  </div>
  {% endfor %}
  {%else%}
  <!-- Featured Products -->
  {% for product in products.latest limit:options.products_home_limit %}
  <div class="col-md-4 col-6 product-block">
    {% include 'list_product' with product %}
  </div>
{% endfor %}
{% endif %}
</div><!-- /.row -->

{% when "not-display"%}

{% endcase%}