function addToCart2(id, productName, qty, options, img, price, url) {
    // Mostrar animación de carga
    Swal.fire({
      html: `
        <div class="loading-spinner">
          <div class="spinner"></div>
          <p>Agregando al carrito...</p>
        </div>
      `,
      showConfirmButton: false,
      allowOutsideClick: false,
    });
  
    Jumpseller.addProductToCart(id, qty, options, {
      callback: function (data, status) {
        Swal.close();
  
        if (data.status === 404) {
          handleError(data);
          return;
        }
  
        // Mostrar notificación de éxito
        showSuccessNotification(productName, qty, options, img, price);
  
        // Actualizar elementos del carrito
        updateCartElements(qty, productName, img, price, url, data.total);
  
        // Recargar si es necesario
        if ($(".no-currency-selected").length < 1) {
          setTimeout(() => location.reload(), 3000);
        }
      },
    });
  }
  
  function handleError(data) {
    let errorMessage = data.responseJSON.message;
    errorMessage = errorMessage.replace(
      /&lt;a href='([^']+)'&gt;([^<]+)&lt;\/a&gt;/g,
      `<a href="$1" class="swal-error-link">$2</a>`
    );
  
    Swal.fire({
      icon: "error",
      title: "Atención",
      html: errorMessage,
      confirmButtonColor: "#3085d6",
      confirmButtonText: "Entendido",
      customClass: {
        htmlContainer: 'swal-stock-error'
      }
    });
  }
  
  function showSuccessNotification(productName, qty, options, img, price) {
    Swal.fire({
      html: `
        <div class="mk-cart-notification">
          <h3 class="mk-notification-title">PRODUCTO AGREGADO AL CARRITO</h3>
          <div class="mk-product-details">
            <div class="mk-product-image">
              <img src="${img}" alt="${productName}">
            </div>
            <div class="mk-product-info">
              <h4>${productName}</h4>
              <p class="mk-product-qty"><strong>CANTIDAD:</strong> ${qty}</p>
              <p class="mk-product-price">${price}</p>
            </div>
          </div>
          <div class="mk-notification-actions">
            <button class="mk-view-bag" onclick="window.location.href='${$(
              "#cart-link"
            ).attr("href")}'">VER CARRITO</button>
            <button class="mk-continue-shopping" onclick="Swal.close()">SEGUIR COMPRANDO</button>
          </div>
        </div>
      `,
      showConfirmButton: false,
      customClass: {
        popup: 'mk-notification-popup'
      },
      width: '400px',
    });
    console.log(options)
  }
  
  function updateCartElements(qty, productName, img, price, url, total) {
    // Actualizar contadores
    const cartCounters = [".cart-size", "#cart-btn span", "span.count"];
    cartCounters.forEach((selector) => {
      const element = $(selector);
      const currentCount = parseInt(element.text());
      element
        .prop("counter", currentCount)
        .animate(
          {
            counter: currentCount + parseInt(qty),
          },
          {
            duration: 1000,
            easing: "swing",
            step: function (now) {
              $(this).text(Math.ceil(now));
            },
          }
        );
    });
  
    // Actualizar mini carrito
    $("#cart-products-display").removeClass("hidden");
    $("#minicart-empty").addClass("hidden");
    $(".cart-wrapper .cart-list").append(`
      <div class="cart-item" style="animation: slideIn 0.3s ease-out">
        <div class="cart-item-image">
          <a href="${url}">
            <img src="${img}" alt="${productName}" class="img-fluid rounded">
          </a>
        </div>
        <div class="cart-item-details">
          <h4 class="cart-item-name">
            <a href="${url}">${productName}</a>
          </h4>
          <div class="cart-item-price">
            <span class="quantity">${qty}</span> x 
            <span class="price">${price}</span>
          </div>
        </div>
        <button class="remove-item" onclick="removeCartItem(this)">
          <i class="fas fa-times"></i>
        </button>
      </div>
    `);
  
    // Actualizar total
    $(".total .price-mob")
      .fadeOut(300)
      .text(`$ ${total}`)
      .fadeIn(300);
  }
  