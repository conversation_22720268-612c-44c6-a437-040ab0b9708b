<form id="filters" method="get" action="{{current_url}}">
  <input type="hidden" name="q" value="{{search.query}}">

  <div id="side-filters">
      {% capture selected_filters %}
      {% for filter in filters %}
      {% assign filter_values_sorted = filter.values | sort:"name" %}
      {% for filter_value in filter_values_sorted %}
      {% if filter_value.selected %}
      <div class="badge badge-secondary">
        {{filter_value.name}}
        <a href="#" class="clearFilterBtn" onclick="$('#{{filter_value.id}}').removeAttr('checked'); $('#filters').submit();"><i class="fas fa-times"></i></a>
      </div>
      {% endif %}
      {% endfor %}
    {% endfor %}
    {% endcapture %}
    {% if selected_filters != blank %}
    <div class="side-filters_selected mb-4">
      {{ selected_filters }}

      <div class="clearAll small mt-2">
        <a href="#" class="all" title="{% t 'Clear filters' %}" onclick="window.location = window.location.href.split('?')[0];">
          {% t "Clear filters" %}
          <i class="fas fa-eraser"></i>
        </a>
      </div>
    </div>
    {% endif %}

    <h4>{% t "Filter by" %}</h4>

    {% for filter in filters %}
    <div class="filter_wrapper card p-3 mb-3">
      <h5>{{filter.name}}</h5>
      <ul class="category_menu_list small">
        {% assign filter_values_sorted = filter.values | sort:"name" %}
        {% for filter_value in filter_values_sorted %}
        <li>
          <div class="custom-control custom-checkbox">
            <input type="checkbox" id="{{filter_value.id}}" class="custom-control-input checkbox" name="{{filter.id}}" value="{{filter_value.id}}" {% if filter_value.selected %}checked{% endif %} onChange='this.form.submit()'>
            <label class="custom-control-label name" for="{{filter_value.id}}">{{filter_value.name}}</label>
            <span class="count">{{filter_value.products_count}}</span>
          </div>
        </li>
        {% endfor %}
      </ul>
    </div>
    {% endfor %}

    {% if template == 'category' %}

    {% if options.hide_price != true %}
    {% assign price_sorted_products = category.products | sort: 'price' %}
    <div id="price-filter" class="filter_wrapper card p-3 mb-3 mt-3 d-inline-block" data-url="price">
      <h5>{% t "Price" %}</h5>
      <div class="form-row align-items-center">
        <div class="col-6">
          <label class="mb-0" for="min"><small>{% t "From" %}</small></label>
          <input id="min" placeholder="{{category.products_min_price | price}}" class="form-control form-control-sm" name="min" type="number" min="0">
        </div>
        <div class="col-6">
          <label class="mb-0" for="max"><small>{% t "To" %}</small></label>
          <input id="max" placeholder="{{category.products_max_price | price}}" class="form-control form-control-sm" name="max" type="number" min="0">
        </div>
        <div class="col-12 mt-2">
          <small class='price_message alert alert-warning py-2 px-3 mb-2'>{% t "The maximum price cannot be lower than the minimum" %}.</small>
          <button class="btn btn-primary btn-block" type="submit"><i class="fas fa-arrow-right"></i></button>
        </div>
      </div>
    </div>
    {% endif %}

    <div class="d-block d-md-none">
      {% if category.products != empty %}
      <div class="form-group row mt-3">
        <div class="col-12"><h5>{% t "Sort by" %}</h5></div>
        <div class="col-sm-12 col-md-8">
          <div class="field-group select">
            <select class="select form-control form-control-sm" onchange="window.location.href = this.value">
              {% for sorting_option in category.sorting_options %}
              <option {% if sorting_option.selected %}selected="selected"{% endif %} value="{{sorting_option.url}}#body">{{sorting_option.text}}</option>
              {% endfor %}
            </select>
          </div>
        </div>
      </div>
      {% endif %}
    </div>
    {% endif %}
  </div>
</form>

<script>
  $(document).ready(function(){
    $("#side-filters a.category_menu_trigger_child i" ).click(function(event){event.preventDefault();});

    $(".category_menu_trigger_child i" ).click(function() {
      $(this).parent().next('ul').slideToggle();
      $(this).parent().toggleClass('active');
    });

    var url = new URL(location.href);
    $("#min").val(url.searchParams.get("min"));
    $("#max").val(url.searchParams.get("max"));

    $( "#show_filters" ).click(function() {
      $( "#side-filters" ).slideToggle( "slow", function() {
      });
    });

    var filterQty = $('.side-filters_selected .badge-secondary').length
    $('#show_filters .badge-light').text(filterQty);

    // Show message for prices
    function handleError(){
      $('#price-filter .btn').attr('disabled',true)
      $('#price-filter .price_message').css('display','block')
    }
    function handleSuccess(){
      $('#price-filter .btn').attr('disabled',false)
      $('#price-filter .price_message').css('display','none')
    }
    $('#min').on('blur', function() {
      var minValue = parseInt($('#min').val());
      var maxValue = parseInt($('#max').val());
      if(minValue > maxValue){
        handleError()
      } else {
        handleSuccess()
      }
    });
    $('#max').on('blur', function() {
      var minValue = parseInt($('#min').val());
      var maxValue = parseInt($('#max').val());
      if(minValue > maxValue){
        handleError()
      } else {
        handleSuccess()
      }
    });
  });
</script>
