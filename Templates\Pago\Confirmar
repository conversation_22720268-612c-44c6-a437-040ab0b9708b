<div class="checkout-content">
  <div class="container">
    <!-- Breadcrumb Checkout -->
    <div class="row">
      <div class="col-12 col-md-8 mr-auto ml-auto d-flex breadcrumb-cart">
        <div class="item complete">
          <a id="cart-link" href="{{order.url}}"><span>1</span></a>
          <a id="cart-link" href="{{order.url}}">{% t "Cart" %}</a>
        </div>
        <div class="item complete">
          <a href="{{order.checkout_url}}"><span>2</span></a>
          <a href="{{order.checkout_url}}">{% t "Checkout" %}</a>
        </div>
        <div class="item active">
          <span>3</span>
          <a href="#">{% t "Review" %}</a>
        </div>
        <div class="item">
          <span>4</span>
          <a href="#">{% t "Success" %}</a>
        </div>
      </div>
    </div>
    <!-- Page Heading -->
    <div class="row">
      <div class="col-12">
        <h1 class="page-header">{% t "Review Order" %}</h1>
      </div>
    </div>
    <!-- /.row -->


    {% if order.products == empty %}
    <div class="row">
      <section class="col-12">
        <div class="bg-info alert text-white">{% t "The shopping cart is currently empty. You can go back and start adding products." %}</div>
        <a href="{{store.url}}" class="btn btn-secondary" title="&larr; {% t 'Go back & Keep Shopping' %}">&larr; {% t "Go back & Keep Shopping" %}</a>
      </section>
    </div>
    {% else %}

    <!-- Review Order Table -->
    <div class="row ">
      <div class="col-lg-8 checkout-container">
        <div class="row review-order-info">
          <div class="col-12">
            {% if order.shipping_required %}
            <div class="info">
              <h2>{% t "Shipping Address" %}</h2>
              <p class="address">{{ order.shipping_address.formatted | newline_to_br }}</p>
            </div>
            <div class="info">
              <h2>{% t "Shipping Method" %}</h2>
              <p>{{order.shipping_method}}</p>
            </div>
            {% endif %}
          </div>
          <div class="col-12">
            <div class="info">
              <h2>{% t "Billing Address" %}</h2>
              <p class="address">{{ order.billing_address.formatted | newline_to_br }}</p>
            </div>
            {% if order.additional_information != empty %}
            <div class="info">
              <h2>{% t "Additional Information" %}</h2>
              <p>{{order.additional_information}}</p>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
      <div id="order-summary" class="col-12 col-lg-4">
        <div class="card summary mb-3">
          <div class="card-header">
            <h2 class="card-title">{% t "Order Summary" %}</h2>
          </div>
          <div class="card-body">
            <div id="cart-update-form">
              <div class="cart-table">
                <table class="table">
                  {% for ordered_product in order.products %}
                  <tr>
                    <td class="p-0">
                      {% if ordered_product.image %}
                      <img src="{{ ordered_product.image | resize:'120x150'}}" alt="{{ordered_product.name | escape}}" title="{{ordered_product.name | escape}}">
                      {% else %}
                      <img src="{{ 'no-image-cart.jpg' | asset }}" alt="{{ordered_product.name | escape}}">
                      {% endif %}
                    </td>
                    <td colspan="2">
                      <h3>{{ordered_product.name}}</h3>
                      {% for option in ordered_product.options %}
                      <p><strong>{{option.name}}:</strong> {{option.value}}</p>
                      {% endfor %}
                      <div class="price mt-3">
                        {% if ordered_product.discount > 0 %}
                        <div class="cart-product-discount">
                          {{ordered_product.qty}} x <span class="order-product-price-amount">
                          {{ ordered_product.price | minus:ordered_product.discount | price }}
                          </span>
                          <small class="order-product-price-amount">{{ ordered_product.price | price }}</small>
                        </div>
                        {% else %}
                        {{ordered_product.qty}} x <span class="order-product-price-amount">{{ ordered_product.price | price }}</span>
                        {% endif %}
                      </div>

                    </td>
                  </tr>
                  {% endfor %}
                </table>
                <table class="table">
                  {% if order.subtotal != order.total %}
                  <tr class="totals">
                    <td colspan="1" class="text-left">{% t "Subtotal" %}</td>
                    <td colspan="1" class="text-right"><span class="order-product-price-amount">{{order.subtotal | price }}</span></td>
                  </tr>
                  {% endif %}

                  {% if order.shipping_required %}
                  <tr class="totals">
                    <td colspan="1" class="text-left">{% t "Shipping" %}</td>
                    <td colspan="1" class="text-right"><span class="order-product-price-amount">{{order.shipping | price }}</span></td>
                  </tr>
                  {% endif %}

                  {% if order.tax > 0 and store.tax_on_product_price != true %}
                  <tr class="totals">
                    <td colspan="1" class="text-left">{% t "Tax" %}</td>
                    <td colspan="1" class="text-right"><span class="order-product-price-amount">{{order.tax | price }}</span></td>
                  </tr>
                  {% endif %}

                  {% if order.discount > 0 %}
                  <tr class="totals">
                    <td colspan="1" class="text-left">{% t "Discount" %}</td>
                    <td colspan="1" class="text-right">-<span class="order-product-price-amount">{{order.discount | price }}</span></td>
                  </tr>
                  {% endif %}

                  <tr class="totals key">
                    <td colspan="1" class="text-left">
                      <strong>{% t "Total" %}</strong>
                    </td>
                    <td colspan="1" class="text-right">
                      <strong><span class="order-product-price-amount">{{order.total | price }}</span></strong>
                    </td>
                  </tr>

                </table>
                <form id="review_form" class="text-right" action="{{order.place_order_url}}" method="post">
                  <input type="submit" id="place_order" class="btn btn-primary btn-block" value="{% t 'Place Order' %}" title="{% t 'Place Order' %}" />
                </form>
                {% if options.currencies != blank and options.open_exchange_rates_token != blank %}
                {% assign store_currency = store.currency_code %}
                <p style="text-align: center; font-size: smaller; font-style: italic;">{% t "Payment will be processed in %{store_currency} at the current exchange rate" | store_currency: {{ store_currency }} %}</p>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</div>
