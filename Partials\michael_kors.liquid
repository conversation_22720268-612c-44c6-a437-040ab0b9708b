{% comment %}
  Component: micha<PERSON>-kors-gallery.liquid
  Descripción: Layout premium para ecommerce de moda de lujo con banners destacados,
  secciones de productos nuevos, categorías y galería de Instagram
  Versión: 2.0
  Autor: Desarrollado para Jumpseller
  Dependencias: TailwindCSS
{% endcomment %}

<!-- CDN de Tailwind con configuración personalizada para estética premium -->
<script src="https://cdn.tailwindcss.com"></script>
<script>
  tailwind.config = {
    theme: {
      extend: {
        fontFamily: {
          sans: ['Helvetica Neue', 'Arial', 'sans-serif'],
          serif: ['Didot', 'Garamond', 'serif'],
        },
        colors: {
          gold: '#d4af37',
          premium: {
            100: '#f5f5f5',
            900: '#212121',
          }
        }
      }
    }
  }
</script>

<!-- Banner Principal Hero con animación sutil -->
<div class="max-w-[1660px] mx-auto px-4 py-8">
  <div class="relative h-[500px] md:h-[600px] lg:h-[700px] overflow-hidden">
    <img 
      src="{{ 'hero-banner.jpg' | asset_url }}" 
      alt="{{ settings.store_name }}" 
      class="w-full h-full object-cover object-center transform scale-105 hover:scale-100 transition duration-7000"
    >
    <div class="absolute top-0 left-0 w-full h-full bg-black bg-opacity-20 flex flex-col justify-end p-8 md:p-12">
      <h2 class="text-white text-3xl md:text-5xl font-serif font-light tracking-wide mb-3 opacity-0 animate-[fadeIn_1s_ease-in_forwards]" style="animation-delay: 0.3s">{{ hero_title | default: 'A PLACE IN THE SUN' }}</h2>
      <p class="text-white text-sm md:text-base font-light mb-6 opacity-0 animate-[fadeIn_1s_ease-in_forwards]" style="animation-delay: 0.6s">{{ hero_subtitle | default: 'Discover the new collection designed for your summer getaways - effortless style.' }}</p>
      <a href="{{ hero_button_url | default: '/collection/new-in' }}" class="relative overflow-hidden bg-white text-premium-900 px-8 py-3 w-fit text-sm uppercase tracking-widest group opacity-0 animate-[fadeIn_1s_ease-in_forwards]" style="animation-delay: 0.9s">
        <span class="relative z-10 transition-colors duration-500 group-hover:text-white">{{ hero_button_text | default: 'Shop Now' }}</span>
        <span class="absolute inset-0 w-full h-full bg-premium-900 transform origin-left scale-x-0 group-hover:scale-x-100 transition duration-500"></span>
      </a>
    </div>
  </div>
</div>

<!-- Categorías Destacadas - 4 columnas con hover mejorado -->
<div class="max-w-[1660px] mx-auto px-4 py-10">
  <h2 class="text-center text-2xl font-serif tracking-widest uppercase mb-8">SHOP BY CATEGORY</h2>
  <div class="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-5">
    {% for i in (1..4) %}
      {% assign category_image = 'category-' | append: i | append: '.jpg' %}
      {% assign category_title = 'category_' | append: i | append: '_title' %}
      {% assign category_url = 'category_' | append: i | append: '_url' %}
      
      <div class="relative group overflow-hidden">
        <a href="{{ liquid_object | default: '/collection/category-' | append: i }}">
          <div class="aspect-square overflow-hidden">
            <img 
              src="{{ category_image | asset_url }}" 
              alt="{{ liquid_object | default: 'Category ' | append: i }}" 
              class="w-full h-full object-cover object-center transition duration-700 transform group-hover:scale-105"
            >
          </div>
          <div class="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-3 text-center transform translate-y-full group-hover:translate-y-0 transition duration-500">
            <h3 class="text-premium-900 uppercase text-sm font-medium tracking-wider">
              {{ liquid_object | default: 'CATEGORY ' | append: i }}
            </h3>
            <div class="h-0.5 w-10 bg-gold mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-700 delay-200"></div>
          </div>
          <div class="absolute bottom-0 left-0 right-0 p-3 text-center bg-white bg-opacity-70 transition duration-500 group-hover:opacity-0">
            <h3 class="text-premium-900 uppercase text-sm font-medium tracking-wider">
              {{ liquid_object | default: 'CATEGORY ' | append: i }}
            </h3>
          </div>
        </a>
      </div>
    {% endfor %}
  </div>
</div>

<!-- Sección "NEW IN" con carrusel de productos y más funciones de conversión -->
<div class="max-w-[1660px] mx-auto px-4 py-12 bg-premium-100">
  <h2 class="text-center text-2xl font-serif tracking-widest uppercase mb-2">NEW ARRIVALS</h2>
  <p class="text-center text-sm text-gray-500 mb-8">Just landed and ready to elevate your wardrobe</p>
  
  <div class="relative">
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {% for i in (1..4) %}
        {% assign product_image = 'product-' | append: i | append: '.jpg' %}
        
        <div class="group relative">
          <div class="absolute top-3 right-3 z-10">
            {% if forloop.first %}
              <span class="inline-block bg-gold text-white px-2 py-1 text-xs uppercase tracking-wide">New</span>
            {% endif %}
            {% if forloop.index == 2 %}
              <span class="inline-block bg-premium-900 text-white px-2 py-1 text-xs uppercase tracking-wide">Trending</span>
            {% endif %}
          </div>
          <a href="/product/product-{{ i }}" class="block">
            <div class="relative aspect-[4/5] overflow-hidden bg-gray-100">
              <img 
                src="{{ product_image | asset_url }}" 
                alt="Product {{ i }}" 
                class="w-full h-full object-cover object-center transition duration-500 group-hover:scale-105"
              >
              <div class="absolute bottom-0 w-full p-3 bg-white bg-opacity-0 group-hover:bg-opacity-90 transform translate-y-full group-hover:translate-y-0 transition-all duration-500 flex justify-center">
                <button class="bg-premium-900 text-white px-4 py-2 text-xs uppercase tracking-wider hover:bg-gold transition">Añadir al carrito</button>
              </div>
            </div>
            <div class="pt-4 text-center">
              <div class="flex justify-center space-x-1 mb-2">
                {% for star in (1..5) %}
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-gold" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l2.4 7.4h7.6l-6 4.6 2.4 7.4-6-4.6-6 4.6 2.4-7.4-6-4.6h7.6z"/>
                  </svg>
                {% endfor %}
              </div>
              <h3 class="text-sm text-gray-700 font-light">{{ 'Product Name ' | append: i }}</h3>
              <p class="text-sm font-medium mt-1">${{ forloop.index | times: 99 | plus: 99 }}.00</p>
            </div>
          </a>
        </div>
      {% endfor %}
    </div>
    
    <!-- Flechas de navegación mejoradas -->
    <button class="absolute -left-2 md:-left-6 top-1/3 bg-white p-3 rounded-full shadow-md hover:bg-premium-900 hover:text-white transition-all z-10 focus:outline-none">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
      </svg>
    </button>
    <button class="absolute -right-2 md:-right-6 top-1/3 bg-white p-3 rounded-full shadow-md hover:bg-premium-900 hover:text-white transition-all z-10 focus:outline-none">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
      </svg>
    </button>
  </div>
  
  <div class="text-center mt-10">
    <a href="/collection/new-arrivals" class="inline-block relative overflow-hidden border border-premium-900 text-premium-900 px-8 py-3 text-sm uppercase tracking-widest hover:text-white group">
      <span class="relative z-10 transition-colors duration-500 group-hover:text-white">Ver toda la colección</span>
      <span class="absolute inset-0 w-full h-full bg-premium-900 transform origin-top scale-y-0 group-hover:scale-y-100 transition-transform duration-500"></span>
    </a>
  </div>
</div>

<!-- Banner Secundario con texto y mejor call-to-action -->
<div class="max-w-[1660px] mx-auto px-4 py-10">
  <div class="relative h-[400px] md:h-[500px] overflow-hidden">
    <img 
      src="{{ 'secondary-banner.jpg' | asset_url }}" 
      alt="Summer Collection" 
      class="w-full h-full object-cover object-center"
    >
    <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-t from-black to-transparent opacity-60 flex flex-col justify-end p-8 md:p-12">
      <h2 class="text-white text-3xl md:text-5xl font-serif font-light tracking-wide mb-2">WHEN IN IBIZA</h2>
      <p class="text-white text-sm md:text-base font-light mb-6 max-w-md">Your perfect companions for a beautiful island escape in our Spring 2025 collection</p>
      <a href="/collection/summer" class="relative overflow-hidden group bg-white bg-opacity-90 text-premium-900 px-8 py-3 w-fit text-sm uppercase tracking-widest">
        <span class="relative z-10 transition-colors duration-500 group-hover:text-white">DISCOVER MORE</span>
        <span class="absolute inset-0 w-full h-full bg-premium-900 transform origin-bottom scale-y-0 group-hover:scale-y-100 transition-transform duration-500"></span>
      </a>
    </div>
  </div>
</div>

<!-- Sección de 2 columnas - Categorías de Vestidos y Estilos con efecto mejorado -->
<div class="max-w-[1660px] mx-auto px-4 py-10">
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Columna 1 -->
    <div class="relative group overflow-hidden">
      <a href="/collection/dresses">
        <div class="aspect-[3/4] overflow-hidden">
          <img 
            src="{{ 'category-dress.jpg' | asset_url }}" 
            alt="Dresses for Days" 
            class="w-full h-full object-cover object-center transition duration-700 transform scale-100 group-hover:scale-105"
          >
        </div>
        <div class="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-5 transform translate-y-0 group-hover:translate-y-0 transition-transform duration-500">
          <h3 class="text-premium-900 uppercase text-lg font-medium tracking-wider">DRESSES FOR DAYS</h3>
          <p class="text-gray-700 text-sm mb-3">ELEGANT</p>
          <div class="h-0.5 w-12 bg-gold transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
        </div>
      </a>
    </div>
    
    <!-- Columna 2 -->
    <div class="relative group overflow-hidden">
      <a href="/collection/style">
        <div class="aspect-[3/4] overflow-hidden">
          <img 
            src="{{ 'category-style.jpg' | asset_url }}" 
            alt="Splashy Style" 
            class="w-full h-full object-cover object-center transition duration-700 transform scale-100 group-hover:scale-105"
          >
        </div>
        <div class="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-5 transform translate-y-0 group-hover:translate-y-0 transition-transform duration-500">
          <h3 class="text-premium-900 uppercase text-lg font-medium tracking-wider">SPLASHY STYLE</h3>
          <p class="text-gray-700 text-sm mb-3">STATEMENT</p>
          <div class="h-0.5 w-12 bg-gold transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
        </div>
      </a>
    </div>
  </div>
</div>

<!-- Sección limitada - Ofertas por tiempo limitado (nueva sección para conversión) -->
<div class="max-w-[1660px] mx-auto px-4 py-10 bg-premium-100">
  <div class="text-center mb-10">
    <h2 class="text-premium-900 text-2xl font-serif tracking-widest uppercase mb-2">OFERTAS EXCLUSIVAS</h2>
    <p class="text-gray-700 text-sm">Por tiempo limitado hasta <span class="font-medium">{{ 'now' | date: '%d/%m/%Y' }}</span></p>
    
    <!-- Contador regresivo -->
    <div class="flex justify-center gap-4 mt-6">
      <div class="bg-white p-3 w-16 shadow-sm">
        <span class="block text-2xl font-light text-premium-900">48</span>
        <span class="text-xs uppercase text-gray-500">Horas</span>
      </div>
      <div class="bg-white p-3 w-16 shadow-sm">
        <span class="block text-2xl font-light text-premium-900">23</span>
        <span class="text-xs uppercase text-gray-500">Min</span>
      </div>
      <div class="bg-white p-3 w-16 shadow-sm">
        <span class="block text-2xl font-light text-premium-900">59</span>
        <span class="text-xs uppercase text-gray-500">Seg</span>
      </div>
    </div>
  </div>
  
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    {% for i in (1..3) %}
      {% assign product_image = 'sale-' | append: i | append: '.jpg' %}
      
      <div class="group relative bg-white p-4 shadow-sm hover:shadow-md transition-shadow">
        <div class="absolute top-6 right-6 z-10">
          <span class="inline-block bg-gold text-white px-3 py-1 text-xs uppercase font-medium tracking-wide">-{{ forloop.index | times: 10 | plus: 20 }}%</span>
        </div>
        <a href="/product/sale-{{ i }}" class="block">
          <div class="relative aspect-square overflow-hidden bg-gray-100">
            <img 
              src="{{ product_image | asset_url }}" 
              alt="Product {{ i }}" 
              class="w-full h-full object-cover object-center transition duration-500 group-hover:scale-105"
            >
          </div>
          <div class="pt-4 text-center">
            <h3 class="text-sm text-gray-700 font-light">{{ 'Luxury Item ' | append: i }}</h3>
            <div class="mt-2 flex items-center justify-center gap-2">
              <span class="text-sm font-medium text-premium-900">${{ forloop.index | times: 119 | plus: 89 }}.00</span>
              <span class="text-xs text-gray-500 line-through">${{ forloop.index | times: 169 | plus: 199 }}.00</span>
            </div>
            <button class="mt-4 w-full bg-premium-900 text-white px-4 py-2 text-xs uppercase tracking-wider hover:bg-gold transition-colors">Añadir al carrito</button>
          </div>
        </a>
      </div>
    {% endfor %}
  </div>
</div>

<!-- Sección Accesorios y Destino con efecto overlay mejorado -->
<div class="max-w-[1660px] mx-auto px-4 py-10">
  <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
    <!-- Accesorios -->
    <div class="relative group overflow-hidden">
      <a href="/collection/luggage">
        <div class="aspect-square overflow-hidden">
          <img 
            src="{{ 'luggage-collection.jpg' | asset_url }}" 
            alt="Luggage Collection" 
            class="w-full h-full object-cover object-center transition duration-700 transform scale-100 group-hover:scale-105"
          >
        </div>
        <div class="absolute inset-0 bg-black bg-opacity-10 group-hover:bg-opacity-30 transition-all duration-500"></div>
        <div class="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 p-5">
          <h3 class="text-premium-900 uppercase text-lg font-medium tracking-wider">LOS ESENCIALES PARA CADA VIAJE</h3>
          <div class="h-0.5 w-12 bg-gold mt-3 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
        </div>
      </a>
    </div>
    
    <!-- Destino -->
    <div class="relative group overflow-hidden">
      <a href="/collection/destination">
        <div class="aspect-square overflow-hidden">
          <img 
            src="{{ 'destination.jpg' | asset_url }}" 
            alt="Destination: Ibiza" 
            class="w-full h-full object-cover object-center transition duration-700 transform scale-100 group-hover:scale-105"
          >
        </div>
        <div class="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-30 group-hover:opacity-60 transition-opacity duration-500"></div>
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="text-center transform translate-y-2 group-hover:translate-y-0 transition-transform duration-500">
            <p class="text-white text-sm uppercase tracking-widest mb-3">DESTINATION</p>
            <h2 class="text-white text-5xl md:text-7xl font-serif font-light tracking-widest">IBIZA</h2>
          </div>
        </div>
      </a>
    </div>
  </div>
</div>

<!-- Sección de Instagram - Share Your Style con mejor interacción -->
<div class="max-w-[1660px] mx-auto px-4 py-12">
  <h2 class="text-center text-2xl font-serif tracking-widest uppercase mb-2">SHARE YOUR STYLE</h2>
  <p class="text-center text-sm text-gray-600 mb-8">Síguenos en Instagram y usa #YourBrandName para aparecer en nuestra galería</p>
  
  <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
    {% for i in (1..4) %}
      {% assign insta_image = 'instagram-' | append: i | append: '.jpg' %}
      
      <div class="relative group overflow-hidden">
        <a href="https://instagram.com/yourbrand" target="_blank" class="block">
          <div class="aspect-square overflow-hidden">
            <img 
              src="{{ insta_image | asset_url }}" 
              alt="Instagram Post {{ i }}" 
              class="w-full h-full object-cover object-center transition duration-500 group-hover:scale-110"
            >
          </div>
          <div class="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-0 group-hover:opacity-80 transition-opacity duration-300 flex items-center justify-center">
            <div class="text-center transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500 opacity-0 group-hover:opacity-100">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white" class="mx-auto mb-2">
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
              </svg>
              <p class="text-white text-xs">@username</p>
            </div>
          </div>
        </a>
      </div>
    {% endfor %}
  </div>
  
  <div class="text-center mt-8">
    <a href="https://instagram.com/yourbrand" target="_blank" class="inline-block border border-premium-900 text-premium-900 px-6 py-2 text-xs uppercase tracking-wider hover:bg-premium-900 hover:text-white transition-all duration-300">Ver Galería Completa</a>
  </div>
</div>

<!-- Sección de testimonios (nueva para aumentar conversión) -->
<div class="max-w-[1660px] mx-auto px-4 py-12 bg-premium-100">
  <h2 class="text-center text-2xl font-serif tracking-widest uppercase mb-10">LO QUE DICEN NUESTROS CLIENTES</h2>
  
  <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
    {% for i in (1..3) %}
      <div class="bg-white p-6 shadow-sm">
        <div class="flex justify-center mb-4">
          {% for star in (1..5) %}
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gold" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l2.4 7.4h7.6l-6 4.6 2.4 7.4-6-4.6-6 4.6 2.4-7.4-6-4.6h7.6z"/>
            </svg>
          {% endfor %}
        </div>
        <p class="text-gray-700 text-sm text-center italic mb-6">"{{ 'Testimonio de cliente ' | append: i }} Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla facilisi. Sed euismod, nisl vitae ultricies."</p>
        <div class="text-center">
          <p class="text-premium-900 font-medium text-sm">Cliente {{ i }}</p>
          <p class="text-gray-500 text-xs">{{ 'now' | date: '%d/%m/%Y' }}</p>
        </div>
      </div>
    {% endfor %}
  </div>
</div>

<!-- Banner Michael Kors Collection con posicionamiento mejorado -->
<div class="max-w-[1660px] mx-auto px-4 py-12">
  <h2 class="text-center text-sm tracking-widest uppercase mb-8">COLECCIÓN EXCLUSIVA</h2>
  
  <div class="relative">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-0">
      <!-- Columna 1 -->
      <div class="h-[500px] md:h-[600px] overflow-hidden bg-gray-100 flex items-center justify-center">
        <img 
          src="{{ 'mk-collection-1.jpg' | asset_url }}" 
          alt="Michael Kors Collection" 
          class="w-full h-full object-cover object-center hover:scale-105 transition-transform duration-3000"
        >
      </div>
      
      <!-- Columna 2 -->
      <div class="h-[500px] md:h-[600px] overflow-hidden bg-gray-100 flex items-center justify-center">
        <img 
          src="{{ 'mk-collection-2.jpg' | asset_url }}" 
          alt="Michael Kors Collection" 
          class="w-full h-full object-cover object-center hover:scale-105 transition-transform duration-3000"
        >
      </div>
    </div>
    
    <!-- Texto superpuesto -->
    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
      <div class="text-center bg-white bg-opacity-60 backdrop-blur-sm px-12 py-8 transform transition-transform duration-700 hover:scale-105">
        <h2 class="text-3xl md:text-5xl lg:text-6xl font-serif font-light tracking-widest text-premium-900 mb-4">COLECCIÓN</h2>
        <p class="text-lg uppercase tracking-widest text-premium-900">EXCLUSIVA</p>
        <div class="h-0.5 w-20 bg-gold mx-auto mt-6"></div>
      </div>
    </div>
  </div>
</div>

<!-- Banner Final - Natural Woman con llamado a la acción -->
<div class="max-w-[1660px] mx-auto px-4 py-10">
  <div class="relative h-[400px] md:h-[500px] overflow-hidden">
    <img 
      src="{{ 'natural-woman.jpg' | asset_url }}" 
      alt="Natural Woman" 
      class="w-full h-full object-cover object-center"
    >
    <div class="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-50"></div>
    <div class="absolute inset-0 flex flex-col items-center justify-center">
      <h2 class="text-white text-4xl md:text-5xl font-serif font-light tracking-widest mb-6">NATURAL WOMAN</h2>
      <a href="/collection/natural" class="bg-white bg-opacity-90 text-premium-900 px-8 py-3 uppercase tracking-widest text-sm hover:bg-premium-900 hover:text-white transition-all duration-300">
        Explorar
      </a>
    </div>
  </div>
</div>

<!-- Newsletter rediseñado para alta conversión -->
<div class="max-w-[1660px] mx-auto px-4 py-16 bg-premium-100">
  <div class="max-w-2xl mx-auto text-center">
    <h2 class="text-2xl font-serif tracking-widest uppercase mb-2">ÚNETE A NOSOTROS</h2>
    <p class="text-sm text-gray-700 mb-6">Suscríbete a nuestro newsletter y obtén un 10% de descuento en tu primera compra</p>
    
    <form class="flex flex-col md:flex-row gap-3 max-w-md mx-auto">
      <input type="email" placeholder="Tu correo electrónico" class="flex-grow px-4 py-3 border border-gray-300 focus:outline-none focus:border-premium-900 bg-white" required>
      <button type="submit" class="bg-premium-900 text-white px-6 py-3 uppercase tracking-wider text-sm hover:bg-gold transition-colors">Suscribirse</button>
    </form>
    
    <p class="text-xs text-gray-500 mt-4">Al suscribirte, aceptas nuestra política de privacidad y recibirás actualizaciones exclusivas sobre nuevas colecciones y ofertas especiales.</p>
  </div>
</div>

<!-- Animaciones CSS -->
<style>
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
</style>