/* Estilos generales */
.site-header {
    position: relative;
    z-index: 1000;
    /* Asegura que el header esté siempre encima */
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Mobile Header */
.mobile-header {
    background-color: #fff;
    /* Fondo blanco para claridad */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* Sombra sutil para profundidad */
}

.mobile-header-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.logo-mobile img {
    max-height: 40px;
    /* Ajusta el tamaño del logo */
}

.mobile-actions button {
    background: none;
    border: none;
    font-size: 1.2rem;
    /* Tamaño de los iconos */
    margin-left: 10px;
    cursor: pointer;
}

.mobile-search .input-group {
    margin-top: 10px;
}

/* Mobile Navigation Drawer */
.mobile-nav-drawer {
    position: fixed;
    top: 0;
    left: -300px;
    /* Oculto por defecto */
    width: 300px;
    height: 100%;
    background-color: #fff;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    transition: left 0.3s ease;
    z-index: 1010;
    /* Encima del contenido */
}

.mobile-nav-drawer.open {
    left: 0;
    /* Visible cuando está abierto */
}

.mobile-nav-drawer .nav-header {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.mobile-nav-drawer .close-nav {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
}

.mobile-nav-drawer .nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-nav-drawer .nav-list li a {
    display: block;
    padding: 10px;
    text-decoration: none;
    color: #333;
}

.mobile-nav-drawer .mobile-user-actions {
    padding: 10px;
    border-top: 1px solid #eee;
}

.mobile-nav-drawer .mobile-user-actions a {
    display: block;
    padding: 10px 0;
    text-decoration: none;
    color: #333;
}

.mobile-nav-drawer .language-currency-selector {
    margin-top: 10px;
}

.mobile-nav-drawer .mobile-social-links {
    padding: 10px;
    text-align: center;
}

.mobile-nav-drawer .mobile-social-links a {
    font-size: 1.2rem;
    margin: 0 10px;
}

/* Desktop Header */
.desktop-header .top-bar {
    background-color: #f8f8f8;
    border-bottom: 1px solid #eee;
}

.desktop-header .top-bar-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.desktop-header .top-message {
    margin: 0;
    font-size: 0.9rem;
}

.desktop-header .main-header {
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.desktop-header .main-header-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.desktop-header .logo img {
    max-height: 50px;
    /* Ajusta el tamaño del logo */
}

.desktop-header .nav-list {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;
}

.desktop-header .nav-list li a {
    padding: 10px 15px;
    text-decoration: none;
    color: #333;
}

.desktop-header .header-actions button {
    background: none;
    border: none;
    font-size: 1.2rem;
    margin-left: 15px;
    cursor: pointer;
}

/* Search Overlay */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    /* Fondo oscuro */
    display: none;
    /* Oculto por defecto */
    justify-content: center;
    align-items: center;
    z-index: 1020;
    /* Encima de todo */
}

.search-overlay.open {
    display: flex;
    /* Visible cuando está abierto */
}

.search-overlay .search-form {
    width: 90%;
    max-width: 600px;
}

/* Cart Sidebar */
.cart-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    /* Oculto por defecto */
    width: 400px;
    height: 100%;
    background-color: #fff;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 1010;
    /* Encima del contenido */
}

.cart-sidebar.open {
    right: 0;
    /* Visible cuando está abierto */
}

.cart-sidebar .cart-sidebar-content {
    padding: 20px;
}

/* Media Queries para Responsividad */
@media (max-width: 991px) {
    .desktop-header {
        display: none;
        /* Oculta el header de escritorio en móviles */
    }
}

@media (min-width: 992px) {
    .mobile-header {
        display: none;
        /* Oculta el header móvil en escritorios */
    }
}