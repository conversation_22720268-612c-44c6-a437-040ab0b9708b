<div class="checkout-content">
  <div class="container">
    <!-- Breadcrumb Checkout -->
    <div class="row">
      <div class="col-12 col-md-8 mr-auto ml-auto d-flex breadcrumb-cart">
        <div class="item active">
          <a id="cart-link" href="{{order.url}}"><span>1</span></a>
          <a id="cart-link" href="{{order.url}}">{% t "Cart" %}</a>
        </div>
        <div class="item">
          <a href="{{order.checkout_url}}"><span>2</span></a>
          <a href="{{order.checkout_url}}">{% t "Checkout" %}</a>
        </div>
        <div class="item">
          <span>3</span>
          <a href="#">{% t "Review" %}</a>
        </div>
        <div class="item">
          <span>4</span>
          <a href="#">{% t "Success" %}</a>
        </div>
      </div>
    </div>
    <!-- Page Heading -->
    <div class="row">
      <div class="col-12">
        <h1 class="page-header">{% t "Shopping Cart" %}</h1>
      </div>
    </div>
    <!-- /.row -->

    {% if order.products == empty %}
    <div class="row">
      <section class="col-12">
        <div class="bg-secondary alert text-white">{% t "The shopping cart is currently empty. You can go back and start adding products." %}</div>
        <a href="/" class="btn btn-link" title="&larr; {% t 'Go back & Keep Shopping' %}">&larr; {% t "Go back & Keep Shopping" %}</a>
      </section>
    </div>
    {% else %}
    <!-- Cart Table -->
    <div class="row mb-4">
      <div class="col-12">
        <form id="cart-update-form" method="post" action="{{order.update_url}}">
          <div class="cart-table">
            <table class="table">
              <thead>
                <tr class="accent-bg text-white">
                  <th colspan="3" class="desktop-hide">{% t "Product" %}</th>
                  <th class="mob-hide">{% t "Product" %}</th>
                  <th class="mob-hide"></th>
                  <th class="mob-hide">{% t "Unit Price" %}</th>
                  <th class="mob-hide table-qty">{% t "Qty" %}</th>
                  <th class="mob-hide">{% t "Subtotal" %}</th>
                  <th class="mob-hide">{% t "Remove" %}</th>
                </tr>
              </thead>
              {% for ordered_product in order.products %}
              {% assign total_products_qty = ordered_product.qty | plus: total_products_qty %}
              <tr class="desktop-hide">
                <td>
                  <a href="{{ ordered_product.url }}" class="trsn" title="{{ordered_product.name | escape}}">
                    {% if ordered_product.image %}
                    <img src="{{ ordered_product.image | resize:'120x150'}}" alt="{{ordered_product.name | escape}}" title="{{ordered_product.name | escape}}">
                    {% else %}
                    <img src="{{ 'no-image-cart.jpg' | asset }}" alt="{{ordered_product.name | escape}}">
                    {% endif %}
                  </a>
                </td>
                <td colspan="2">
                  <div>
                    <h3>{{ordered_product.name}}</h3>
                    {% for option in ordered_product.options %}
                    <p><strong>{{option.name}}:</strong> {{option.value}}</p>
                    {% endfor %}
                    <div class="price">
                      {% if ordered_product.discount > 0 %}
                      <span><strong>{% t "Unit Price" %}:</strong> </span>
                      <span class="order-product-price-amount">{{ ordered_product.price | minus:ordered_product.discount | price }}</span>
                      <span class="cart-product-discount">
                          <small>{{ ordered_product.price | price }}</small>
                      </span>
                      {% else %}
                      <span><strong>{% t "Unit Price" %}:</strong> </span>
                      <span class="order-product-price-amount">{{ ordered_product.price | price }}</span>
                      {% endif %}
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="text-center mob-hide left-border">
                  <a href="{{ ordered_product.url }}" class="trsn" title="{{ordered_product.name | escape}}">
                    {% if ordered_product.image %}
                    <img src="{{ ordered_product.image | resize:'120x150'}}" alt="{{ordered_product.name | escape}}" title="{{ordered_product.name | escape}}">
                    {% else %}
                    <img src="{{ 'no-image-cart.jpg' | asset }}" alt="{{ordered_product.name | escape}}">
                    {% endif %}
                  </a>
                </td>
                <td class="mob-hide">
                  <h3>{{ordered_product.name}}</h3>
                  {% for option in ordered_product.options %}
                  <p><strong>{{option.name}}:</strong> {{option.value}}</p>
                  {% endfor %}
                </td>

                <td class="mob-hide text-center left-border">
                  {% if ordered_product.discount > 0 %}
                  <span class="order-product-price-amount">{{ ordered_product.price | minus:ordered_product.discount | price }}</span>
                  <span class="cart-product-discount">
                    <small>{{ ordered_product.price | price }}</small>
                  </span>
                  {% else %}
                  <span class="order-product-price-amount">{{ ordered_product.price | price }}</span>
                  {% endif %}
                </td>
                <td class="left-border">
                  <div class="select">
                    <select class="select select-qty form-control" name="{{ordered_product.id}}" title="Qty" onchange="$('#cart-update-form').submit();return false;">
                      {% if ordered_product.stock_unlimited %}
                      {% assign qty_limit = ordered_product.qty | plus: 30 %}
                      {% else %}
                      {% if ordered_product.stock > 50 %}
                      {% assign qty_limit = ordered_product.qty | plus: 30 %}
                      {% else %}
                      {% assign qty_limit = ordered_product.stock %}
                      {% endif %}
                      {% endif %}
                      {% for qty in (1..qty_limit) %}
                      <option value="{{qty}}" {% if ordered_product.qty == qty %}selected="selected"{% endif %}>{{qty}}</option>
                      {% endfor %}
                    </select>
                  </div>
                </td>
                <td class="text-center left-border ">
                  <span class="order-product-subtotal">{{ ordered_product.discount | times: ordered_product.qty | times: '-1' | plus: ordered_product.subtotal | price }}</span>
                </td>
                <td class="text-center both-border">
                  <span class="remove">
                    <a href="{{ordered_product.remove_url}}" class="cart-product-remove" title="{% t 'Remove Product' %}"><i class="linear-icon icon-0130-trash2"></i></a>
                  </span>
                </td>
              </tr>
              {% endfor %}
            </table>
          </div>
        </form>
      </div>
    </div>
    <div class="row {% if coupon_form or order.shipping_required %}justify-content-between{% else %}justify-content-end {% endif %}">
      {% if coupon_form %}
      <div class="col-12 col-md-4 cart-estimate">
        <div class="card secondary-card mb-3">
          <div class="card-header">
            <h2 class="card-title">{% t "Have a discount code?" %}</h2>
          </div>
          <div class="card-body">
            {{coupon_form}}
          </div>
        </div>
      </div>
      {% endif %}
      <!-- Cart Options -->
      {% if order.shipping_required and options.estimate_shipping_visibility %}
      <div class="col-12 col-md-4 cart-estimate">
        <div class="card secondary-card mb-3">
          <div class="card-header">
            <h2 class="card-title">{% t "Estimate Shipping Costs" %}</h2>
          </div>
          <div class="card-body">
            {{estimate_form}}
          </div>
        </div>
      </div>
      {% endif %}
      <div class="col-12 col-md-4 cart-totals">
        <div class="card summary mb-3">
          <div class="card-header">
            <h2 class="card-title">{% t "Order Summary" %}</h2>
          </div>
          <div class="card-body">
            <table class="table summary">

              {% if order.subtotal != order.total %}
              <tr class="totals">
                <td colspan="1" class="text-left">{% t "Subtotal" %}</td>
                <td colspan="1" class="text-right">{{order.subtotal | price }}</td>
              </tr>
              {% endif %}

              {% if order.shipping_required %}
              <tr class="totals">
                <td colspan="1" class="text-left">{% t "Shipping" %}</td>
                <td colspan="1" class="text-right">{{order.shipping | price }}</td>
              </tr>
              {% endif %}

              {% if order.tax > 0 and store.tax_on_product_price != true %}
              <tr class="totals">
                <td colspan="1" class="text-left">{% t "Tax" %}</td>
                <td colspan="1" class="text-right">{{order.tax | price }}</td>
              </tr>
              {% endif %}

              {% if order.discount > 0 %}
              <tr class="totals">
                <td colspan="1" class="text-left">{% t "Discount" %}</td>
                <td colspan="1" class="text-right">-{{order.discount | price }}</td>
              </tr>
              {% endif %}

              <tr class="totals key">
                <td colspan="1" class="text-left"><strong>{% t "Total" %}</strong></td>
                <td colspan="1" class="text-right"><strong>{{order.total | price }}</strong></td>
              </tr>

            </table>
            {% if order.minimum_purchase.above_minimum != true %}
            {% if order.minimum_purchase.condition_type == 'qty' %}
            {% capture minimum %}<strong>{{order.minimum_purchase.condition_value}}</strong>{% endcapture %}
            {% capture at_least %}<strong>{{ order.minimum_purchase.condition_value | minus: total_products_qty }}</strong>{% endcapture %}
            <div class="alert alert-warning">{% t "The minimum quantity of products to proceed with the purchase is %{minimum}. To continue please add at least %{at_least} product(s)" | minimum: '{{minimum}}' | at_least: '{{at_least}}' %}</div>
            {% else %}
            {% capture minimum %}<strong class="order-product-price">{{order.minimum_purchase.condition_value | price }}</strong>{% endcapture %}
            {% capture at_least %}<strong class="order-product-price">{{ order.minimum_purchase.condition_value | minus: order.total | price }}</strong>{% endcapture %}
            <div class="alert alert-warning">
              {% t "The minimum amount to proceed with your purchase is %{minimum}. To continue please add at least %{at_least}" | minimum: '{{minimum}}' | at_least: '{{at_least}}' %}
            </div>
            {% endif %}
            {% else %}
            <a href="{{order.checkout_url}}" class="btn btn-primary btn-block" title="{% t 'Proceed to Checkout' %}">{% t 'Proceed to Checkout' %}</a>
            {% endif %}

            <div class="text-center cart-actions mt-2">
              <a href="/" class="btn btn-link btn-block text-right" title="&larr; {% t 'Continue Shopping' %}">&larr; {% t 'Continue Shopping' %}</a>
            </div>

          </div>
        </div>

      </div>
    </div>

    {% endif %}
  </div>
</div>
