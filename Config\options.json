{"Hero Home Sections": {"icon": "home", "options": {"hero_enabled": {"name": "<PERSON><PERSON> Hero Banner Principal", "type": "checkbox", "default": true}, "hero_image": {"name": "<PERSON><PERSON> Principal", "type": "image"}, "hero_title": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "input", "default": "Bienvenidos a nuestra tienda"}, "hero_subtitle": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "default": "Descubre nuestra amplia selección de productos de calidad"}, "hero_cta_text": {"name": "Texto del Botón CTA", "type": "input", "default": "Ver productos"}, "hero_cta_link": {"name": "Enlace del Botón CTA", "type": "link", "default": "/categories"}, "featured_cats_enabled": {"name": "Mostrar Categorías Destacadas", "type": "checkbox", "default": true}, "featured_cats_title": {"name": "T<PERSON><PERSON>lo Sección Categorías", "type": "input", "default": "Nuestras Categorías"}, "featured_cats_layout": {"name": "Diseño de Categorías", "type": "select", "options": [{"label": "Grid", "value": "grid"}, {"label": "<PERSON><PERSON><PERSON>", "value": "carousel"}], "default": "grid"}, "category_1_image": {"name": "Categoría 1 - Imagen", "type": "image"}, "category_1_title": {"name": "Categoría 1 - Título", "type": "input"}, "category_1_link": {"name": "Categoría 1 - Enlace", "type": "link"}, "category_2_image": {"name": "Categoría 2 - Imagen", "type": "image"}, "category_2_title": {"name": "Categoría 2 - T<PERSON><PERSON>lo", "type": "input"}, "category_2_link": {"name": "Categoría 2 - <PERSON>lace", "type": "link"}, "category_3_image": {"name": "Categoría 3 - Imagen", "type": "image"}, "category_3_title": {"name": "Categoría 3 - <PERSON><PERSON><PERSON>lo", "type": "input"}, "category_3_link": {"name": "Categoría 3 - <PERSON>lace", "type": "link"}, "product_carousel_enabled": {"name": "Mostrar Carrusel de Productos", "type": "checkbox", "default": true}, "product_carousel_collection": {"name": "Colección de Productos a Mostrar", "type": "category", "info": "Selecciona la categoría de productos que deseas mostrar en el carrusel"}, "product_carousel_title": {"name": "Tí<PERSON>lo del Carrusel", "type": "text", "default": "Productos Destacados"}, "product_carousel_limit": {"name": "Límite de Productos", "type": "slider", "min": 4, "max": 12, "step": 1, "default": 8}, "promo_banner_enabled": {"name": "Mostrar Banner Promocional", "type": "checkbox", "default": false}, "promo_banner_image": {"name": "Imagen Banner Promocional", "type": "image"}, "promo_banner_title": {"name": "Título Banner Promocional", "type": "input"}, "promo_banner_subtitle": {"name": "Subtítulo Banner Promocional", "type": "text"}, "promo_banner_cta_text": {"name": "Texto Botón Promocional", "type": "input"}, "promo_banner_cta_link": {"name": "Enlace Botón Promocional", "type": "link"}}}, "General": {"icon": "cog", "options": {"favicon": {"name": "Favicon (Browser Icon)", "type": "file"}, "estimate_shipping_visibility": {"name": "Estimate Shipping on Cart Page", "type": "checkbox", "default": true}, "success_page_product_table": {"name": "Product Table at Success Page", "type": "checkbox", "default": false}, "home_blog": {"name": "Display Blog in homepage", "type": "checkbox", "default": true}, "megamenu": {"name": "Menu Megamenu", "type": "checkbox", "default": false}, "position_nav": {"name": "Posi<PERSON> on Header", "type": "select", "default": "flex-start", "options": [{"label": "Center", "value": "flex-center"}, {"label": "Left", "value": "flex-start"}, {"label": "Right", "value": "flex-end"}]}, "products_home": {"name": "Display products on Homepage", "type": "select", "default": "latest-and-featured-home", "options": [{"label": "Only Featured", "value": "featured-home"}, {"label": "Only Latest", "value": "latest-home"}, {"label": "Featured and Latest", "value": "latest-and-featured-home"}, {"label": "Do not display products", "value": "not-display"}]}, "fetured_on_search": {"name": "Show products when no search results", "type": "checkbox", "default": true}, "products_featured_limit": {"name": "Featured Products on Homepage", "type": "select", "default": "6", "options": [{"label": "3", "value": "3"}, {"label": "6", "value": "6"}, {"label": "9", "value": "9"}, {"label": "12", "value": "12"}, {"label": "15", "value": "15"}, {"label": "18", "value": "18"}, {"label": "21", "value": "21"}, {"label": "24", "value": "24"}, {"label": "27", "value": "27"}, {"label": "30", "value": "30"}]}, "products_home_limit": {"name": "Latest Products on Homepage", "type": "select", "default": "6", "options": [{"label": "3", "value": "3"}, {"label": "6", "value": "6"}, {"label": "9", "value": "9"}, {"label": "12", "value": "12"}, {"label": "15", "value": "15"}, {"label": "18", "value": "18"}, {"label": "21", "value": "21"}, {"label": "24", "value": "24"}, {"label": "27", "value": "27"}, {"label": "30", "value": "30"}]}, "products_category_limit": {"name": "Products per Page in Categories", "type": "select", "default": "18", "options": [{"label": "6", "value": "6"}, {"label": "9", "value": "9"}, {"label": "12", "value": "12"}, {"label": "15", "value": "15"}, {"label": "18", "value": "18"}, {"label": "21", "value": "21"}, {"label": "24", "value": "24"}, {"label": "27", "value": "27"}, {"label": "30", "value": "30"}, {"label": "33", "value": "33"}, {"label": "36", "value": "36"}, {"label": "39", "value": "39"}]}, "products_search_limit": {"name": "Products per Page in Search Result", "type": "select", "default": "18", "options": [{"label": "6", "value": "6"}, {"label": "9", "value": "9"}, {"label": "12", "value": "12"}, {"label": "15", "value": "15"}, {"label": "18", "value": "18"}, {"label": "21", "value": "21"}, {"label": "24", "value": "24"}, {"label": "27", "value": "27"}, {"label": "30", "value": "30"}, {"label": "33", "value": "33"}, {"label": "36", "value": "36"}, {"label": "39", "value": "39"}]}, "page_post_per_page": {"name": "Blog Posts per Page", "type": "select", "default": "9", "options": [{"label": "6", "value": "6"}, {"label": "9", "value": "9"}, {"label": "12", "value": "12"}, {"label": "15", "value": "15"}, {"label": "18", "value": "18"}, {"label": "21", "value": "21"}, {"label": "24", "value": "24"}, {"label": "27", "value": "27"}, {"label": "30", "value": "30"}, {"label": "33", "value": "33"}]}}}, "Store Info": {"icon": "store", "options": {"display_top_message": {"name": "Display Top Promotional Message", "type": "checkbox", "default": true}, "top_message": {"name": "Top Message", "type": "input", "default": "Edit this message in Theme Options"}, "top_message_icon": {"name": "Top Message Icon", "type": "select", "default": "0520-diamond2", "options": [{"label": "Home", "value": "0001-home"}, {"label": "Bathtub", "value": "0007-bathtub"}, {"label": "Bed", "value": "0009-bed"}, {"label": "Buildings", "value": "0012-city"}, {"label": "<PERSON><PERSON>", "value": "0010-couch"}, {"label": "Chair", "value": "0011-chair"}, {"label": "Pencil 1", "value": "0014-pencil"}, {"label": "Pencil 2", "value": "0017-pencil3"}, {"label": "Edit", "value": "0019-pencil4"}, {"label": "Bucket", "value": "0041-bucket"}, {"label": "Gun", "value": "0050-gun"}, {"label": "<PERSON><PERSON>", "value": "0051-bottle"}, {"label": "Snow", "value": "0056-snow2"}, {"label": "Fire", "value": "0057-fire"}, {"label": "Tornado", "value": "0067-tornado"}, {"label": "Sun", "value": "0072-sun"}, {"label": "Cloud upload", "value": "0076-cloud-upload"}, {"label": "Cloud rain", "value": "0078-cloud-rain"}, {"label": "Cloud sun", "value": "0084-cloud-sun"}, {"label": "Padlock", "value": "0108-lock"}, {"label": "Cog", "value": "0115-cog"}, {"label": "Hammer", "value": "0120-hammer"}, {"label": "Recycle", "value": "0128-recycle"}, {"label": "Joystick", "value": "0135-joystick"}, {"label": "<PERSON><PERSON>", "value": "0136-dice"}, {"label": "Diamond", "value": "0520-diamond2"}, {"label": "Clubs", "value": "0139-clubs"}, {"label": "At sign", "value": "0150-at-sign"}, {"label": "Envelope open", "value": "0152-envelope-open"}, {"label": "Inbox", "value": "0157-inbox"}, {"label": "Printer", "value": "0178-printer"}, {"label": "Scissors", "value": "0200-scissors"}, {"label": "Newspaper", "value": "0214-news"}, {"label": "Graduation hat", "value": "0219-graduation-hat"}, {"label": "Medal third", "value": "0225-medal-third"}, {"label": "Music note", "value": "0231-music-note3"}, {"label": "Speaker", "value": "0242-loudspeaker"}, {"label": "Picture", "value": "0272-picture2"}, {"label": "Book 1", "value": "0275-book"}, {"label": "Book 2", "value": "0277-book2"}, {"label": "2 users", "value": "0292-users2"}, {"label": "3 users", "value": "0291-users"}, {"label": "Man", "value": "0297-man"}, {"label": "Baby boy", "value": "0299-baby2"}, {"label": "Baby girl", "value": "0298-baby"}, {"label": "Woman figure", "value": "0306-woman2"}, {"label": "Man figure", "value": "0307-man2"}, {"label": "Man and woman figure", "value": "0308-man-woman"}, {"label": "Tie", "value": "0314-tie"}, {"label": "Shoes", "value": "0317-shoes"}, {"label": "Hat", "value": "0318-hat"}, {"label": "Shirt", "value": "0322-shirt"}, {"label": "Cashier", "value": "0331-cashier"}, {"label": "Shopping cart", "value": "0334-cart"}, {"label": "Wallet", "value": "0345-wallet"}, {"label": "Credit Card", "value": "0346-credit-card"}, {"label": "Telephone", "value": "0363-telephone"}, {"label": "Map", "value": "0387-map2"}, {"label": "Calendar", "value": "0393-calendar-31"}, {"label": "Laptop", "value": "0430-laptop"}, {"label": "<PERSON>", "value": "0442-power"}, {"label": "<PERSON><PERSON>", "value": "0445-lamp"}, {"label": "Bubble message", "value": "0473-bubble-dots"}, {"label": "Brain", "value": "0499-brain"}, {"label": "Glass", "value": "0529-glass2"}, {"label": "Chef", "value": "0532-chef"}, {"label": "Steak", "value": "0538-steak"}, {"label": "Burger", "value": "0539-hamburger"}, {"label": "Pizza", "value": "0541-pizza"}, {"label": "Cheese", "value": "0546-cheese"}, {"label": "Cherry", "value": "0554-cherry"}, {"label": "Grapes", "value": "0555-grapes"}, {"label": "Pine Tree", "value": "0560-pine-tree"}, {"label": "Christmas Tree", "value": "0678-christmas"}, {"label": "Rocket", "value": "0568-rocket"}, {"label": "Bus", "value": "0590-bus"}, {"label": "Truck", "value": "0600-truck"}, {"label": "Bicycle", "value": "0608-bicycle"}, {"label": "Glasses", "value": "0632-glasses"}, {"label": "Earth", "value": "0645-earth"}, {"label": "Smile", "value": "0651-smile"}, {"label": "Happy", "value": "0650-happy"}, {"label": "<PERSON><PERSON>", "value": "0655-wink"}, {"label": "Mad", "value": "0663-mad"}, {"label": "Evil", "value": "0665-evil"}, {"label": "Wow", "value": "0666-wow"}, {"label": "Mustache", "value": "0672-mustache"}, {"label": "Mustache 2", "value": "0680-mustache2"}, {"label": "Micophone", "value": "0718-mic2"}, {"label": "Clock", "value": "0744-clock2"}, {"label": "Link", "value": "0792-link2"}, {"label": "Thumb up", "value": "0794-thumbs-up"}, {"label": "Thumb down", "value": "0795-thumbs-down"}, {"label": "Magnifier", "value": "0803-magnifier"}, {"label": "Cross", "value": "0811-cross"}, {"label": "Percent circle", "value": "0862-percent-circle"}, {"label": "Arrow up", "value": "0834-arrow-up"}, {"label": "Arrow down", "value": "0835-arrow-down"}, {"label": "Notification", "value": "0853-notification"}, {"label": "Pointer hand", "value": "0961-pointer-up"}]}, "top_message_background_color": {"name": "Top Message Background Color", "type": "color", "default": "#000000"}, "top_message_font_color": {"name": "Top Message Font Color", "type": "color", "default": "#ffffff"}, "display_contact_email": {"name": "Show Contact Email", "type": "checkbox", "default": false}, "second_email": {"name": "Contact email 2", "type": "input", "default": ""}, "phone": {"name": "Contact Phone", "type": "input", "default": "+00 123 4567"}, "display_address": {"name": "Display Address in Contact Information", "type": "checkbox", "default": true}, "display_google_map": {"name": "Display Map in Contact Page", "type": "checkbox", "default": true}}}, "Product Page": {"icon": "archive", "options": {"product_stock_visibility": {"name": "Display Product Stock", "type": "checkbox", "default": true}, "product_sku_visibility": {"name": "Display Product SKU", "type": "checkbox", "default": true}, "zoom_product_image": {"name": "Zoom on Product Image", "type": "checkbox", "default": true}, "related_products_visibility": {"name": "Display Related Products", "type": "checkbox", "default": true}, "related_products_limit": {"name": "Max quantity of Related Products", "type": "select", "default": "12", "options": [{"label": "6", "value": "6"}, {"label": "12", "value": "12"}, {"label": "18", "value": "18"}, {"label": "24", "value": "24"}, {"label": "30", "value": "30"}, {"label": "36", "value": "36"}]}}}, "Colors": {"icon": "tint", "options": {"primary_color": {"name": "Primary Color", "type": "color", "default": "#000000"}, "accent_color": {"name": "Accent Color", "type": "color", "default": "#d9ae2d"}, "store_background": {"name": "Store Background", "type": "color", "default": "#ffffff"}, "footer_background": {"name": "Footer Background", "type": "color", "default": "#ffffff"}, "footer_color": {"name": "Footer Color", "type": "color", "default": "#000000"}}}, "Fonts": {"icon": "font", "options": {"general_font": {"name": "General", "type": "google_font", "default": "<PERSON><PERSON><PERSON>", "options": [{"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Montserrat", "value": "Montserrat"}, {"label": "Noto Sans", "value": "Noto Sans"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Open Sans", "value": "Open Sans"}, {"label": "Open Sans Condensed", "value": "Open Sans Condensed"}, {"label": "PT Sans", "value": "PT Sans"}, {"label": "PT Serif", "value": "PT Serif"}, {"label": "<PERSON><PERSON><PERSON> (by <PERSON><PERSON><PERSON>)", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Roboto", "value": "Roboto"}]}, "titles_font": {"name": "Titles", "type": "google_font", "default": "Playfair Display", "options": [{"label": "Abril Fatface", "value": "Abril Fatface"}, {"label": "Playfair Display (by <PERSON><PERSON><PERSON>)", "value": "Playfair Display"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Handlee", "value": "Handlee"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Libre Baskerville", "value": "Libre Baskerville"}, {"label": "Lobster", "value": "Lobster"}, {"label": "Montserrat", "value": "Montserrat"}, {"label": "Philosopher", "value": "Philosopher"}, {"label": "Poiret One", "value": "Poiret One"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON> Slab", "value": "Roboto"}]}, "price_font": {"name": "Prices", "type": "google_font", "default": "Playfair Display", "options": [{"label": "Abril Fatface", "value": "Abril Fatface"}, {"label": "Playfair Display (by <PERSON><PERSON><PERSON>)", "value": "Playfair Display"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Libre Baskerville", "value": "Libre Baskerville"}, {"label": "Montserrat", "value": "Montserrat"}, {"label": "Philosopher", "value": "Philosopher"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Roboto", "value": "Roboto"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Noto Sans", "value": "Noto Sans"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Open Sans", "value": "Open Sans"}, {"label": "Open Sans Condensed", "value": "Open Sans Condensed"}, {"label": "PT Sans", "value": "PT Sans"}, {"label": "PT Serif", "value": "PT Serif"}]}, "store_name_font": {"name": "Store Name", "type": "google_font", "default": "Abril Fatface", "options": [{"label": "<PERSON><PERSON><PERSON>  (by <PERSON><PERSON><PERSON>)", "value": "Abril Fatface"}, {"label": "Playfair Display", "value": "Playfair Display"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Handlee", "value": "Handlee"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Libre Baskerville", "value": "Libre Baskerville"}, {"label": "Lobster", "value": "Lobster"}, {"label": "Montserrat", "value": "Montserrat"}, {"label": "Philosopher", "value": "Philosopher"}, {"label": "Poiret One", "value": "Poiret One"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON> Slab", "value": "Roboto"}]}, "general_font_size": {"name": "General <PERSON><PERSON>", "type": "select", "default": "14px", "options": [{"label": "10px", "value": "10px"}, {"label": "11px", "value": "11px"}, {"label": "12px", "value": "12px"}, {"label": "13px", "value": "13px"}, {"label": "14px (by <PERSON><PERSON><PERSON>)", "value": "14px"}, {"label": "15px", "value": "15px"}, {"label": "16px", "value": "16px"}, {"label": "17px", "value": "17px"}, {"label": "18px", "value": "18px"}]}, "title_font_size": {"name": "Title Font Size", "type": "select", "default": "36px", "options": [{"label": "12px", "value": "12px"}, {"label": "14px", "value": "14px"}, {"label": "16px", "value": "16px"}, {"label": "18px", "value": "18px"}, {"label": "20px", "value": "20px"}, {"label": "22px", "value": "22px"}, {"label": "24px", "value": "24px"}, {"label": "26px", "value": "26px"}, {"label": "28px", "value": "28px"}, {"label": "30px", "value": "30px"}, {"label": "32px", "value": "32px"}, {"label": "34px", "value": "34px"}, {"label": "36px (by <PERSON><PERSON><PERSON>)", "value": "36px"}, {"label": "38px", "value": "38px"}, {"label": "40px", "value": "40px"}]}, "store_name_font_size": {"name": "Store Name Font Size", "type": "select", "default": "34px", "options": [{"label": "14px", "value": "14px"}, {"label": "15px", "value": "15px"}, {"label": "16px", "value": "16px"}, {"label": "17px", "value": "17px"}, {"label": "18px ", "value": "18px"}, {"label": "20px", "value": "20px"}, {"label": "22px", "value": "22px"}, {"label": "24px", "value": "24px"}, {"label": "26px", "value": "26px"}, {"label": "28px", "value": "28px"}, {"label": "30px", "value": "30px"}, {"label": "32px", "value": "32px"}, {"label": "34px (by <PERSON><PERSON><PERSON>)", "value": "34px"}, {"label": "36px", "value": "36px"}, {"label": "38px", "value": "38px"}, {"label": "40px", "value": "40px"}]}, "menu_font_size": {"name": "Menu Font Size", "type": "select", "default": "15px", "options": [{"label": "10px", "value": "10px"}, {"label": "11px", "value": "11px"}, {"label": "12px", "value": "12px"}, {"label": "13px", "value": "13px"}, {"label": "14px", "value": "14px"}, {"label": "15px (by <PERSON><PERSON><PERSON>)", "value": "15px"}, {"label": "16px", "value": "16px"}, {"label": "17px", "value": "17px"}, {"label": "18px", "value": "18px"}, {"label": "20px", "value": "20px"}]}}}, "Home Principal Slider (1)": {"icon": "image", "options": {"slider_loop": {"name": "Activate Loop", "type": "checkbox", "default": true}, "slider_autoplay": {"name": "Activate Autoplay", "type": "checkbox", "default": false}, "slider_pause": {"name": "Activate Pause on Hover", "type": "checkbox", "default": true}, "slider_autoplay_speed": {"name": "Autoplay Time Out", "type": "select", "default": "4000", "options": [{"label": "2 seconds", "value": "2000"}, {"label": "3 seconds", "value": "3000"}, {"label": "4 seconds", "value": "4000"}, {"label": "5 seconds", "value": "5000"}, {"label": "6 seconds", "value": "6000"}]}, "slider_background_color": {"name": "Slider Background Opacity color", "type": "color", "default": "#ffffff"}, "slider_background_opacity": {"name": "Slider Opacity Percentage", "type": "select", "default": "0.3", "options": [{"label": "0%", "value": "0"}, {"label": "10%", "value": "0.1"}, {"label": "20%", "value": "0.2"}, {"label": "30%", "value": "0.3"}, {"label": "40%", "value": "0.4"}, {"label": "50%", "value": "0.5"}, {"label": "60%", "value": "0.6"}, {"label": "70%", "value": "0.7"}, {"label": "80%", "value": "0.8"}, {"label": "90%", "value": "0.9"}]}, "slider-1-image": {"name": "Slide 1 Image (Suggested: 1900x600)", "type": "file"}, "slider-1-link": {"name": "Slide 1 Link", "type": "input", "default": ""}, "slider-1-text-button": {"name": "Slide 1 Button Text", "type": "input", "default": ""}, "slider-2-image": {"name": "Slide 2 Image (Suggested: 1900x600)", "type": "file"}, "slider-2-link": {"name": "Slide 2 Link", "type": "input", "default": ""}, "slider-2-text-button": {"name": "Slide 2 Button Text", "type": "input", "default": ""}, "slider-3-image": {"name": "Slide 3 Image (Suggested: 1900x600)", "type": "file"}, "slider-3-link": {"name": "Slide 3 Link", "type": "input", "default": ""}, "slider-3-text-button": {"name": "Slide 3 Button Text", "type": "input", "default": ""}}}, "Home Secondary Slider (3)": {"icon": "image", "options": {"secondary_slider_display": {"name": "Display Secondary Slider on Homepage", "type": "checkbox", "default": true}, "secondary_slider_loop": {"name": "Activate Loop", "type": "checkbox", "default": true}, "secondary_slider_autoplay": {"name": "Activate Autoplay", "type": "checkbox", "default": false}, "secondary_slider_pause": {"name": "Activate Pause on Hover", "type": "checkbox", "default": false}, "secondary_slider_autoplay_speed": {"name": "Autoplay Time Out", "type": "select", "default": "4000", "options": [{"label": "2 seconds", "value": "2000"}, {"label": "3 seconds", "value": "3000"}, {"label": "4 seconds", "value": "4000"}, {"label": "5 seconds", "value": "5000"}, {"label": "6 seconds", "value": "6000"}]}, "secondary_slider_background_color": {"name": "Slider Background Opacity color", "type": "color", "default": "#ffffff"}, "secondary_slider_background_opacity": {"name": "Slider Opacity Percentage", "type": "select", "default": "0.3", "options": [{"label": "0%", "value": "0"}, {"label": "10%", "value": "0.1"}, {"label": "20%", "value": "0.2"}, {"label": "30%", "value": "0.3"}, {"label": "40%", "value": "0.4"}, {"label": "50%", "value": "0.5"}, {"label": "60%", "value": "0.6"}, {"label": "70%", "value": "0.7"}, {"label": "80%", "value": "0.8"}, {"label": "90%", "value": "0.9"}]}, "secondary-slider-1-image": {"name": "Slide 1 Image (Suggested: 700x800)", "type": "file"}, "secondary-slider-1-link": {"name": "Slide 1 Link", "type": "input", "default": ""}, "secondary-slider-1-text-button": {"name": "Slide 1 Button Text", "type": "input", "default": ""}, "secondary-slider-2-image": {"name": "Slide 2 Image (Suggested: 700x800)", "type": "file"}, "secondary-slider-2-link": {"name": "Slide 2 Link", "type": "input", "default": ""}, "secondary-slider-2-text-button": {"name": "Slide 2 Button Text", "type": "input", "default": ""}, "secondary-slider-3-image": {"name": "Slide 3 Image (Suggested: 700x800)", "type": "file"}, "secondary-slider-3-link": {"name": "Slide 3 Link", "type": "input", "default": ""}, "secondary-slider-3-text-button": {"name": "Slide 3 Button Text", "type": "input", "default": ""}, "secondary-slider-4-image": {"name": "Slide 4 Image (Suggested: 700x800)", "type": "file"}, "secondary-slider-4-link": {"name": "Slide 4 Link", "type": "input", "default": ""}, "secondary-slider-4-text-button": {"name": "Slide 4 Button Text", "type": "input", "default": ""}, "secondary-slider-5-image": {"name": "Slide 5 Image (Suggested: 700x800)", "type": "file"}, "secondary-slider-5-link": {"name": "Slide 5 Link", "type": "input", "default": ""}, "secondary-slider-5-text-button": {"name": "Slide 5 Button Text", "type": "input", "default": ""}, "secondary-slider-6-image": {"name": "Slide 6 Image (Suggested: 700x800)", "type": "file"}, "secondary-slider-6-link": {"name": "Slide 6 Link", "type": "input", "default": ""}, "secondary-slider-6-text-button": {"name": "Slide 6 Button Text", "type": "input", "default": ""}}}, "Home Banners": {"icon": "th-large", "options": {"banners_display": {"name": "Show Banners on Homepage", "type": "checkbox", "default": true}, "banner_1_image": {"name": "Banner 1 Image (Suggested: 600x350)", "type": "file"}, "banner_1_title": {"name": "Banner 1 Title", "type": "input", "default": ""}, "banner_1_link_text": {"name": "Banner 1 Link Text", "type": "input", "default": ""}, "banner_1_link": {"name": "Banner 1 Link", "type": "input", "default": ""}, "banner_2_image": {"name": "Banner 2 Image (Suggested: 600x350)", "type": "file"}, "banner_2_title": {"name": "Banner 2 Title", "type": "input", "default": ""}, "banner_2_link_text": {"name": "Banner 2 Link Text", "type": "input", "default": ""}, "banner_2_link": {"name": "Banner 2 Link", "type": "input", "default": ""}, "banner_3_image": {"name": "Banner 3 Image (Suggested: 600x350)", "type": "file"}, "banner_3_title": {"name": "Banner 3 Title", "type": "input", "default": ""}, "banner_3_link_text": {"name": "Banner 3 Link Text", "type": "input", "default": ""}, "banner_3_link": {"name": "Banner 3 Link", "type": "input", "default": ""}, "banner_4_image": {"name": "Banner 4 Image (Suggested: 600x350)", "type": "file"}, "banner_4_title": {"name": "Banner 4 Title", "type": "input", "default": ""}, "banner_4_link_text": {"name": "Banner 4 Link Text", "type": "input", "default": ""}, "banner_4_link": {"name": "Banner 4 Link", "type": "input", "default": ""}, "banner_5_image": {"name": "Banner 5 Image (Suggested: 600x350)", "type": "file"}, "banner_5_title": {"name": "Banner 5 Title", "type": "input", "default": ""}, "banner_5_link_text": {"name": "Banner 5 Link Text", "type": "input", "default": ""}, "banner_5_link": {"name": "Banner 5 Link", "type": "input", "default": ""}, "banner_6_image": {"name": "Banner 6 Image (Suggested: 600x350)", "type": "file"}, "banner_6_title": {"name": "Banner 6 Title", "type": "input", "default": ""}, "banner_6_link_text": {"name": "Banner 6 Link Text", "type": "input", "default": ""}, "banner_6_link": {"name": "Banner 6 Link", "type": "input", "default": ""}}}, "Our Brands": {"icon": "tags", "options": {"show_brands_logos": {"name": "Show Brand Logos Slider", "type": "checkbox", "default": true}, "brands_logos_loop": {"name": "Activate Loop", "type": "checkbox", "default": true}, "brands_logos_autoplay": {"name": "Autoplay Slider", "type": "checkbox", "default": true}, "brands_logos_speed": {"name": "Autoplay Speed", "type": "select", "default": "3000", "options": [{"label": "1 second", "value": "1000"}, {"label": "2 seconds", "value": "2000"}, {"label": "3 seconds", "value": "3000"}, {"label": "4 seconds", "value": "4000"}, {"label": "5 seconds", "value": "5000"}]}, "brand_logo_1": {"name": "Brand Logo 1 (Suggested: 400x250)", "type": "file"}, "brand_link_1": {"name": "Brand Link 1", "type": "input", "default": ""}, "brand_logo_2": {"name": "Brand Logo 2 (Suggested: 400x250)", "type": "file"}, "brand_link_2": {"name": "Brand Link 2", "type": "input", "default": ""}, "brand_logo_3": {"name": "Brand Logo 3 (Suggested: 400x250)", "type": "file"}, "brand_link_3": {"name": "Brand Link 3", "type": "input", "default": ""}, "brand_logo_4": {"name": "Brand Logo 4 (Suggested: 400x250)", "type": "file"}, "brand_link_4": {"name": "Brand Link 4", "type": "input", "default": ""}, "brand_logo_5": {"name": "Brand Logo 5 (Suggested: 400x250)", "type": "file"}, "brand_link_5": {"name": "Brand Link 5", "type": "input", "default": ""}, "brand_logo_6": {"name": "Brand Logo 6 (Suggested: 400x250)", "type": "file"}, "brand_link_6": {"name": "Brand Link 6", "type": "input", "default": ""}, "brand_logo_7": {"name": "Brand Logo 7 (Suggested: 400x250)", "type": "file"}, "brand_link_7": {"name": "Brand Link 7", "type": "input", "default": ""}, "brand_logo_8": {"name": "Brand Logo 8 (Suggested: 400x250)", "type": "file"}, "brand_link_8": {"name": "Brand Link 8", "type": "input", "default": ""}, "brand_logo_9": {"name": "Brand Logo 9 (Suggested: 400x250)", "type": "file"}, "brand_link_9": {"name": "Brand Link 9", "type": "input", "default": ""}, "brand_logo_10": {"name": "Brand Logo 10 (Suggested: 400x250)", "type": "file"}, "brand_link_10": {"name": "Brand Link 10", "type": "input", "default": ""}}}, "Social Networks": {"icon": "share-alt", "options": {"facebook_button_on_products": {"name": "Facebook Button on Products", "type": "checkbox", "default": true}, "twitter_button_on_products": {"name": "Twitter Button on Products", "type": "checkbox", "default": true}, "pinterest_button_on_products": {"name": "Pinterest Button on Products", "type": "checkbox", "default": true}, "tumblr_button_on_products": {"name": "Tumblr Button on Products", "type": "checkbox", "default": true}, "whatsapp_button_on_products": {"name": "Whatsapp Button on Products in Mobile", "type": "checkbox", "default": true}, "instafeed": {"name": "Show Instagram Feed on Home Page", "type": "checkbox", "default": true}, "instagram-limit": {"name": "Number of instagram photos", "type": "select", "default": "12", "options": [{"label": "6", "value": "6"}, {"label": "12", "value": "12"}]}}}, "Footer Features": {"icon": "star", "options": {"footer_features": {"name": "Show features area on Footer", "type": "checkbox", "default": true}, "footer_features_background_image": {"name": "Background Image Features Footer", "type": "file"}, "footer_features_background_color": {"name": "Background Color Features Footer", "type": "color", "default": "#000000"}, "footer_features_background_opacity": {"name": "Background Features Opacity Percentage", "type": "select", "default": "0.6", "options": [{"label": "0%", "value": "0"}, {"label": "10%", "value": "0.1"}, {"label": "20%", "value": "0.2"}, {"label": "30%", "value": "0.3"}, {"label": "40%", "value": "0.4"}, {"label": "50%", "value": "0.5"}, {"label": "60%", "value": "0.6"}, {"label": "70%", "value": "0.7"}, {"label": "80%", "value": "0.8"}, {"label": "90%", "value": "0.9"}, {"label": "100%", "value": "1"}]}, "footer_features_color": {"name": "Text Color Features Footer", "type": "color", "default": "#FFFFFF"}, "icon-feature-1": {"name": "Feature 1 Icon", "type": "icon", "default": ""}, "title-feature-1": {"name": "Feature 1 Title", "type": "input", "default": ""}, "text-feature-1": {"name": "Feature 1 Text", "type": "text", "default": ""}, "icon-feature-2": {"name": "Feature 2 Icon", "type": "icon", "default": ""}, "title-feature-2": {"name": "Feature 2 Title", "type": "input", "default": ""}, "text-feature-2": {"name": "Feature 2 Text", "type": "text", "default": ""}, "icon-feature-3": {"name": "Feature 3 Icon", "type": "icon", "default": ""}, "title-feature-3": {"name": "Feature 3 Title", "type": "input", "default": ""}, "text-feature-3": {"name": "Feature 3 Text", "type": "text", "default": ""}}}, "Footer": {"icon": "ellipsis-h", "options": {"enable_subscribe": {"name": "Enable Suscription Form", "type": "checkbox", "default": true}, "mailchimp_post_url": {"name": "Url Mailchimp Form", "type": "input", "default": ""}, "subscribe_text": {"name": "Text Suscription Form", "type": "text", "default": "Subscribe to our email list \nto receive news and \noffers first."}}}, "Payment methods to show": {"icon": "credit-card", "options": {"webpay_payment": {"name": "Show Webpay Payment", "type": "checkbox", "default": false}, "chek_payment": {"name": "Show Chek Payment", "type": "checkbox", "default": false}, "khipu_payment": {"name": "Show Khipu Payment", "type": "checkbox", "default": false}, "servipag_payment": {"name": "Show Servipag Payment", "type": "checkbox", "default": false}, "mach_payment": {"name": "Show Mach Payment", "type": "checkbox", "default": false}, "flow_payment": {"name": "Show Flow Payment", "type": "checkbox", "default": false}, "caja_vecina_payment": {"name": "Show Caja Vecina Payment", "type": "checkbox", "default": false}, "multicaja_payment": {"name": "Show Multicaja Payment", "type": "checkbox", "default": false}, "pse_payment": {"name": "Show PSE Payment", "type": "checkbox", "default": false}, "baloto_payment": {"name": "Show Baloto Payment", "type": "checkbox", "default": false}, "epayco_payment": {"name": "Show ePayco Payment", "type": "checkbox", "default": false}, "efecty_payment": {"name": "Show Efecty Payment", "type": "checkbox", "default": false}, "oxxo_payment": {"name": "Show Oxxo Payment", "type": "checkbox", "default": false}, "mercadopago_payment": {"name": "Show Mercadopago Payment", "type": "checkbox", "default": false}, "multibanco_payment": {"name": "Show Multibanco Payment", "type": "checkbox", "default": false}, "mbway_payment": {"name": "Show MBWay Payment", "type": "checkbox", "default": false}, "payu_payment": {"name": "Show PayU Payment", "type": "checkbox", "default": false}, "ideal_payment": {"name": "Show iDEAL Payment", "type": "checkbox", "default": false}, "skrill_payment": {"name": "Show Skrill Payment", "type": "checkbox", "default": false}, "stripe_payment": {"name": "Show Stripe Payment", "type": "checkbox", "default": false}, "apple_payment": {"name": "Show Apple Pay Payment", "type": "checkbox", "default": false}, "bitcoin_payment": {"name": "Show Bitcoin Payment", "type": "checkbox", "default": false}, "visa_payment": {"name": "Show Visa Payment", "type": "checkbox", "default": true}, "mastercard_payment": {"name": "Show MasterCard Payment", "type": "checkbox", "default": true}, "american_payment": {"name": "Show American Express Payment", "type": "checkbox", "default": true}, "paypal_payment": {"name": "Show Paypal Payment", "type": "checkbox", "default": true}, "diners_payment": {"name": "Show Diners Club Payment", "type": "checkbox", "default": false}, "bank_transfer_payment": {"name": "Show Bank Transfer Payment", "type": "checkbox", "default": false}, "manual_payment": {"name": "Show Manual Payment", "type": "checkbox", "default": false}}}, "Add to Cart Notification": {"icon": "shopping-cart", "options": {"display_cart_notification": {"name": "Add to Cart Notification", "type": "checkbox", "default": true}, "cart_notification_color": {"name": "Notification Color", "type": "color", "default": "#098E46"}}}, "Other Options": {"icon": "cogs", "options": {"new_customer_message": {"name": "New Customer Message", "type": "text", "default": "By registering at our shop, you will make more expedite the checkout process, you can add multiple shipping addresses, view and track your orders, and more."}, "currencies": {"name": "Additional Currencies (e.g. USD, EUR)", "type": "input", "default": ""}, "open_exchange_rates_token": {"name": "OpenExchangeRates App ID", "type": "input", "default": ""}, "widget_blog_title": {"name": "Widget Blog Title", "type": "input", "default": "Side Widget Blog"}, "widget_blog_text": {"name": "Widget Blog Text", "type": "text", "default": "By registering at our shop, you will make more expedite the checkout process, you can add multiple shipping addresses, view and track your orders, and more. You can edit this text from: Themes > Theme Options > Blog"}, "disable_shopping_cart": {"name": "Disable Shopping Cart", "type": "checkbox", "default": false}, "hide_price": {"name": "Hide Prices", "type": "checkbox", "default": false}, "head_code": {"name": "Embed code on the end of <head>", "type": "text", "default": ""}, "body_code": {"name": "Embed code on the end of <body>", "type": "text", "default": ""}}}, "Galeria SUCLA Personalizada": {"icon": "image", "options": {"sucla_general_max_width": {"type": "text", "name": "<PERSON><PERSON> (ej: 1660px)", "default": "1660px"}, "sucla_section_padding_y": {"type": "text", "name": "Padding Vertical Secciones (ej: 3rem)", "default": "3rem"}, "sucla_section_padding_x": {"type": "text", "name": "Padding Horizontal Secciones (ej: 1rem)", "default": "1rem"}, "sucla_grid_gap": {"type": "text", "name": "Espacio en Grids/Carruseles (ej: 1.5rem)", "default": "1.5rem"}, "sucla_border_radius": {"type": "text", "name": "Radio de Borde General (ej: 4px)", "default": "4px"}, "sucla_hero_enabled": {"type": "checkbox", "name": "Mostrar Hero Banner", "default": true}, "sucla_hero_image": {"type": "image", "name": "Imagen de Fondo"}, "sucla_hero_image_position": {"type": "select", "name": "Posición Imagen Fondo", "options": [{"label": "Centro", "value": "center"}, {"label": "Arriba", "value": "top"}, {"label": "Abajo", "value": "bottom"}, {"label": "Iz<PERSON>erda", "value": "left"}, {"label": "Derecha", "value": "right"}], "default": "center"}, "sucla_hero_alt_text": {"type": "text", "name": "Texto Alternativo Imagen"}, "sucla_hero_min_height_vh": {"type": "text", "name": "Altura Mínima (%) Pantalla (vh)", "default": "65vh", "info": "Altura relativa al alto de la pantalla. Ej: 65vh"}, "sucla_hero_min_height_vh_mobile": {"type": "text", "name": "Altura Mínima <PERSON> (%) Pantalla (vh)", "default": "55vh"}, "sucla_hero_overlay_color": {"type": "color", "name": "Color Superposición (Overlay)", "default": "rgba(0, 0, 0, 0.35)"}, "sucla_hero_text_color": {"type": "color", "name": "Color Texto General (Hero)", "default": "#FFFFFF"}, "sucla_hero_title": {"type": "text", "name": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON> Aquí"}, "sucla_hero_title_color": {"type": "color", "name": "Color Título (Opcional)", "info": "Si vacío, usa Color Texto General", "default": "#000"}, "sucla_hero_title_weight": {"type": "text", "name": "<PERSON><PERSON><PERSON> (100-900)", "default": "700"}, "sucla_hero_subtitle": {"type": "text", "name": "Subtítulo", "default": "Describe tu propuesta de valor o promoción."}, "sucla_hero_subtitle_color": {"type": "color", "name": "Color Subtítulo (Opcional)", "default": "#000"}, "sucla_hero_cta_text": {"type": "text", "name": "<PERSON><PERSON> (CTA)", "default": "<PERSON><PERSON><PERSON>"}, "sucla_hero_cta_link": {"type": "link", "name": "<PERSON><PERSON> (CTA)"}, "sucla_hero_cta_bg_color": {"type": "color", "name": "Color Fondo Botón", "default": "#007bff"}, "sucla_hero_cta_text_color": {"type": "color", "name": "Color Texto Botón", "default": "#FFFFFF"}, "sucla_hero_cta_bg_hover_color": {"type": "color", "name": "Color Fondo Botón (Hover)", "default": "#0056b3"}, "sucla_featured_cats_enabled": {"type": "checkbox", "name": "Mostrar Categorías Destacadas", "default": true}, "sucla_featured_cats_title": {"type": "text", "name": "T<PERSON><PERSON>lo Sección Categorías", "default": "Compra por Categoría"}, "sucla_featured_cats_layout": {"type": "select", "name": "Diseño Categorías", "options": [{"value": "grid", "label": "Grid"}, {"value": "carousel", "label": "<PERSON><PERSON><PERSON>"}], "default": "grid"}, "sucla_featured_cats_min_carousel": {"type": "slider", "name": "Mínimo items para Carrusel Cat.", "min": 2, "max": 6, "step": 1, "default": 3, "info": "Si hay menos items, se mostrará en grid."}, "sucla_featured_cats_loop": {"type": "checkbox", "name": "Loop Infinito Categorías (Carrusel)", "default": false}, "sucla_category_aspect_ratio": {"type": "text", "name": "Ratio Imagen Categoría (ej: 4 / 3)", "default": "4 / 3"}, "sucla_featured_cats_slides_large": {"type": "slider", "name": "# Slides Cat. Escritorio Grande", "min": 2, "max": 6, "step": 1, "default": 5}, "sucla_featured_cats_slides_desktop": {"type": "slider", "name": "# Slides Cat. Escritorio", "min": 2, "max": 6, "step": 1, "default": 4}, "sucla_featured_cats_slides_tablet": {"type": "slider", "name": "# Slides Cat. Tablet", "min": 2, "max": 5, "step": 1, "default": 3}, "sucla_featured_cats_slides_mobile": {"type": "slider", "name": "# Slides Cat. Móvil", "min": 1, "max": 3, "step": 1, "default": 2}, "sucla_featured_cats_spacing": {"type": "slider", "name": "Espacio Slides Cat. (px, <PERSON><PERSON><PERSON>)", "min": 5, "max": 40, "step": 1, "default": 15}, "sucla_category_1_image": {"type": "image", "name": "Imagen Categoría 1"}, "sucla_category_1_title": {"type": "text", "name": "Título Categoría 1"}, "sucla_category_1_link": {"type": "link", "name": "Enlace Categoría 1"}, "sucla_category_2_image": {"type": "image", "name": "Imagen Categoría 2"}, "sucla_category_2_title": {"type": "text", "name": "Título Categoría 2"}, "sucla_category_2_link": {"type": "link", "name": "Enlace Categoría 2"}, "sucla_category_3_image": {"type": "image", "name": "Imagen Categoría 3"}, "sucla_category_3_title": {"type": "text", "name": "Título Categoría 3"}, "sucla_category_3_link": {"type": "link", "name": "Enlace Categoría 3"}, "sucla_category_4_image": {"type": "image", "name": "Imagen Categoría 4"}, "sucla_category_4_title": {"type": "text", "name": "Título Categoría 4"}, "sucla_category_4_link": {"type": "link", "name": "Enlace Categoría 4"}, "sucla_category_5_image": {"type": "image", "name": "Imagen Categoría 5"}, "sucla_category_5_title": {"type": "text", "name": "Título Categoría 5"}, "sucla_category_5_link": {"type": "link", "name": "Enlace Categoría 5"}, "sucla_category_6_image": {"type": "image", "name": "Imagen Categoría 6"}, "sucla_category_6_title": {"type": "text", "name": "Título Categoría 6"}, "sucla_category_6_link": {"type": "link", "name": "Enlace Categoría 6"}, "sucla_product_carousel_enabled": {"type": "checkbox", "name": "Mostrar Carrusel Productos", "default": true}, "sucla_product_carousel_title": {"type": "text", "name": "<PERSON><PERSON><PERSON><PERSON>", "default": "Novedades"}, "sucla_product_carousel_collection": {"type": "category", "name": "Colección a Mostrar Productos"}, "sucla_product_carousel_limit": {"type": "slider", "name": "Máximo Productos", "min": 3, "max": 24, "step": 1, "default": 8}, "sucla_product_carousel_loop": {"type": "checkbox", "name": "Loop Infinito Productos", "default": false}, "sucla_product_card_aspect_ratio": {"type": "text", "name": "Ratio Imagen Producto (ej: 1 / 1)", "default": "1 / 1", "info": "Afecta las cards en este carrusel."}, "sucla_product_carousel_show_view_all_button": {"type": "checkbox", "name": "<PERSON><PERSON> 'Ver Todo' <PERSON>os", "default": true}, "sucla_product_carousel_view_all_link": {"type": "link", "name": "<PERSON>lace 'Ver Todo' <PERSON> (Opcional)", "info": "<PERSON> vacío, usa enlace de colección."}, "sucla_product_carousel_view_all_text": {"type": "text", "name": "Texto Botón 'Ver Todo' Productos", "default": "Ver toda la colección"}, "sucla_product_carousel_bg_color": {"type": "color", "name": "Color Fondo Sección Productos", "default": "#f9f9f9"}, "sucla_product_carousel_slides_large": {"type": "slider", "name": "# Slides Prod. Escritorio Grande", "min": 3, "max": 8, "step": 1, "default": 6}, "sucla_product_carousel_slides_desktop": {"type": "slider", "name": "# Slides Prod. Escritorio", "min": 3, "max": 7, "step": 1, "default": 5}, "sucla_product_carousel_slides_tablet": {"type": "slider", "name": "# Slides Prod. Tablet", "min": 2, "max": 5, "step": 1, "default": 4}, "sucla_product_carousel_slides_sm_tablet": {"type": "slider", "name": "# Slides Prod. Tablet Pequeña", "min": 2, "max": 4, "step": 1, "default": 3}, "sucla_product_carousel_slides_mobile": {"type": "slider", "name": "# Slides Prod. Móvil", "min": 1, "max": 3, "step": 1, "default": 2}, "sucla_product_carousel_spacing": {"type": "slider", "name": "Espacio lógico Slides Prod. (px)", "min": 5, "max": 40, "step": 1, "default": 15, "info": "El espacio visual se crea con padding."}, "sucla_promo_banner_enabled": {"type": "checkbox", "name": "Mostrar Banner Promocional", "default": false}, "sucla_promo_banner_image": {"type": "image", "name": "Imagen Banner Promo"}, "sucla_promo_banner_image_position": {"type": "select", "name": "Posición Imagen Banner Promo", "options": [{"label": "Centro", "value": "center"}, {"label": "Arriba", "value": "top"}, {"label": "Abajo", "value": "bottom"}, {"label": "Iz<PERSON>erda", "value": "left"}, {"label": "Derecha", "value": "right"}], "default": "center"}, "sucla_promo_banner_alt_text": {"type": "text", "name": "Texto Alternativo Imagen Banner Promo"}, "sucla_promo_banner_min_height": {"type": "text", "name": "<PERSON>ura Mínima Banner Promo (ej: 400px)", "default": "400px"}, "sucla_promo_banner_min_height_mobile": {"type": "text", "name": "Altura Mínima <PERSON> Promo (ej: 300px)", "default": "300px"}, "sucla_promo_banner_overlay_color": {"type": "color", "name": "Color Overlay Banner Promo", "default": "rgba(0, 0, 0, 0.45)"}, "sucla_promo_banner_text_color": {"type": "color", "name": "Color Texto Banner Promo", "default": "#FFFFFF"}, "sucla_promo_banner_title": {"type": "text", "name": "T<PERSON><PERSON>lo <PERSON> Promo", "default": "Título Promocional"}, "sucla_promo_banner_title_color": {"type": "color", "name": "Color Título Banner Promo (Opcional)", "default": "#000"}, "sucla_promo_banner_title_weight": {"type": "text", "name": "Grosor Fuente Título Banner Promo", "default": "600"}, "sucla_promo_banner_subtitle": {"type": "text", "name": "Subtítulo Banner Promo", "default": "Texto descriptivo de la promoción."}, "sucla_promo_banner_subtitle_color": {"type": "color", "name": "Color Subtítulo Banner Promo (Opcional)", "default": "#000"}, "sucla_promo_banner_cta_text": {"type": "text", "name": "Texto Botón Banner Promo", "default": "Descubrir"}, "sucla_promo_banner_cta_link": {"type": "link", "name": "<PERSON><PERSON> Banner Promo"}, "sucla_promo_banner_cta_bg_color": {"type": "color", "name": "Color Fondo Botón Banner Promo", "default": "transparent"}, "sucla_promo_banner_cta_text_color": {"type": "color", "name": "Color Texto Botón Banner Promo", "default": "#FFFFFF"}, "sucla_promo_banner_cta_border_color": {"type": "color", "name": "Color Borde Botón Banner Promo", "default": "#FFFFFF"}, "sucla_promo_banner_cta_bg_hover_color": {"type": "color", "name": "Color Fondo Botón Banner Promo (Hover)", "default": "#000"}, "sucla_promo_banner_cta_text_hover_color": {"type": "color", "name": "Color Texto Botón Banner Promo (Hover)", "default": "#fff"}, "sucla_promo_banner_cta_border_hover_color": {"type": "color", "name": "Color Borde Botón Banner Promo (Hover)", "default": "#fff"}, "sucla_content_blocks_enabled": {"type": "checkbox", "name": "Mostrar Bloques Contenido", "default": false}, "sucla_content_blocks_title": {"type": "text", "name": "T<PERSON><PERSON>lo <PERSON>cción Bloques (Opcional)"}, "sucla_content_blocks_bg_color": {"type": "color", "name": "Color Fondo Sección Bloques", "default": "#ffffff"}, "sucla_content_blocks_border_color": {"type": "color", "name": "Color Borde Bloques", "default": "#eeeeee"}, "sucla_block_aspect_ratio": {"type": "text", "name": "<PERSON><PERSON> Imagen Bloque (ej: 16 / 9)", "default": "16 / 9"}, "sucla_content_block_1_image": {"type": "image", "name": "Imagen Bloque 1"}, "sucla_content_block_1_title": {"type": "text", "name": "Título Bloque 1"}, "sucla_content_block_1_text": {"type": "text", "name": "Texto Bloque 1"}, "sucla_content_block_1_link": {"type": "link", "name": "Enlace Bloque 1 (Opcional)"}, "sucla_content_block_1_link_text": {"type": "text", "name": "Texto Enlace Bloque 1", "default": "<PERSON><PERSON>"}, "sucla_content_block_2_image": {"type": "image", "name": "Imagen Bloque 2"}, "sucla_content_block_2_title": {"type": "text", "name": "Título Bloque 2"}, "sucla_content_block_2_text": {"type": "text", "name": "Texto Bloque 2"}, "sucla_content_block_2_link": {"type": "link", "name": "Enlace Bloque 2 (Opcional)"}, "sucla_content_block_2_link_text": {"type": "text", "name": "Texto Enlace Bloque 2", "default": "<PERSON><PERSON>"}, "sucla_content_block_3_image": {"type": "image", "name": "Imagen Bloque 3"}, "sucla_content_block_3_title": {"type": "text", "name": "Título Bloque 3"}, "sucla_content_block_3_text": {"type": "text", "name": "Texto Bloque 3"}, "sucla_content_block_3_link": {"type": "link", "name": "Enlace Bloque 3 (Opcional)"}, "sucla_content_block_3_link_text": {"type": "text", "name": "Texto Enlace Bloque 3", "default": "<PERSON><PERSON>"}, "sucla_instagram_enabled": {"type": "checkbox", "name": "Mostrar Feed Instagram", "default": false}, "sucla_instagram_title": {"type": "text", "name": "<PERSON><PERSON><PERSON><PERSON> Instagram", "default": "Síguenos en Instagram"}, "sucla_instagram_username": {"type": "text", "name": "Usuario Instagram (sin @)"}, "sucla_instagram_bg_color": {"type": "color", "name": "Color Fondo Sección Instagram", "default": "#fafafa"}, "sucla_instagram_gap": {"type": "slider", "name": "Espacio entre Fotos Instagram (px)", "min": 0, "max": 20, "step": 1, "default": 8}, "sucla_instagram_image_1": {"type": "image", "name": "Instagram Imagen 1"}, "sucla_instagram_image_2": {"type": "image", "name": "Instagram Imagen 2"}, "sucla_instagram_image_3": {"type": "image", "name": "Instagram Imagen 3"}, "sucla_instagram_image_4": {"type": "image", "name": "Instagram Imagen 4"}, "sucla_instagram_image_5": {"type": "image", "name": "Instagram Imagen 5"}, "sucla_instagram_image_6": {"type": "image", "name": "Instagram Imagen 6"}, "sucla_swiper_nav_size": {"type": "text", "name": "<PERSON><PERSON><PERSON> (px)", "default": "44px"}, "sucla_swiper_nav_bg": {"type": "color", "name": "Fondo Flechas Nav", "default": "rgba(255, 255, 255, 0.9)"}, "sucla_swiper_nav_color": {"type": "color", "name": "Color Icono Flechas", "default": "#333333"}, "sucla_swiper_nav_bg_hover": {"type": "color", "name": "Fondo <PERSON> (Hover)", "default": "#000"}, "sucla_swiper_nav_icon_size": {"type": "text", "name": "<PERSON><PERSON><PERSON> (px)", "default": "18px"}, "sucla_swiper_pag_bullet_size": {"type": "text", "name": "<PERSON><PERSON><PERSON> (px)", "default": "10px"}, "sucla_swiper_pag_bullet_color": {"type": "color", "name": "Color Puntos Pag Inactivos", "default": "#333333"}, "sucla_swiper_pag_bullet_active_color": {"type": "color", "name": "Color Punto Activo", "info": "Si vacío, usa color primario", "default": "#000"}}}, "Luxury Showcase": {"icon": "star", "options": {"showcase_enabled": {"type": "checkbox", "name": "Mostrar Luxury Showcase", "default": true, "info": "Activa o desactiva la sección de marcas de lujo"}, "showcase_banner_left": {"type": "image", "name": "Imagen Banner Izquierdo", "info": "Dimensiones recomendadas: 800x1067px (ratio 3:4)"}, "showcase_banner_right": {"type": "image", "name": "Imagen Banner Derecho", "info": "Dimensiones recomendadas: 800x1067px (ratio 3:4)"}, "showcase_title": {"type": "text", "name": "<PERSON><PERSON><PERSON><PERSON>", "default": "MICHAEL KORS", "info": "Título que aparecerá en el centro del banner dual"}, "showcase_subtitle": {"type": "text", "name": "Subtítulo", "default": "Discover the Latest Collection", "info": "Texto descriptivo debajo del título principal"}, "showcase_button_text": {"type": "text", "name": "Texto del Botón", "default": "Shop Now", "info": "Texto que aparecerá en el botón de acción"}, "showcase_button_url": {"type": "link", "name": "Enlace del Botón", "info": "URL a la que dirigirá el botón"}, "showcase_collection": {"type": "category", "name": "Colección de Productos", "info": "Selecciona la colección de productos a mostrar en la cuadrícula"}}}, "Showcase de Marcas de Lujo V2": {"icon": "star", "options": {"showcase_v2_enabled": {"type": "checkbox", "name": "Activar Showcase V2", "default": false}, "showcase_v2_title": {"type": "text", "name": "<PERSON><PERSON><PERSON><PERSON>", "default": "Nuestras Marcas de Lujo"}, "showcase_v2_subtitle": {"type": "text", "name": "Subtítulo", "default": "Descubre nuestra exclusiva selección de marcas premium"}, "showcase_v2_primary_color": {"type": "color", "name": "Color Primario", "default": "#1a1a1a"}, "showcase_v2_secondary_color": {"type": "color", "name": "Color Secundario", "default": "#ffffff"}, "showcase_v2_accent_color": {"type": "color", "name": "Color de Acento", "default": "#d4af37"}, "showcase_v2_text_primary": {"type": "color", "name": "Color de Texto Principal", "default": "#1a1a1a"}, "showcase_v2_text_secondary": {"type": "color", "name": "Color de Texto Secundario", "default": "#ffffff"}, "showcase_v2_heading_1": {"type": "heading", "name": "Item 1"}, "showcase_v2_image_1": {"type": "image", "name": "Imagen Item 1"}, "showcase_v2_title_1": {"type": "text", "name": "Título Item 1", "default": "Marca de Lujo 1"}, "showcase_v2_price_1": {"type": "text", "name": "Precio/Subtexto Item 1 (Opcional)"}, "showcase_v2_link_1": {"type": "link", "name": "Enlace Item 1"}, "showcase_v2_heading_2": {"type": "heading", "name": "Item 2"}, "showcase_v2_image_2": {"type": "image", "name": "Imagen Item 2"}, "showcase_v2_title_2": {"type": "text", "name": "Título Item 2", "default": "Marca de Lujo 2"}, "showcase_v2_price_2": {"type": "text", "name": "Precio/Subtexto Item 2 (Opcional)"}, "showcase_v2_link_2": {"type": "link", "name": "Enlace Item 2"}, "showcase_v2_heading_3": {"type": "heading", "name": "Item 3"}, "showcase_v2_image_3": {"type": "image", "name": "Imagen Item 3"}, "showcase_v2_title_3": {"type": "text", "name": "Título Item 3", "default": "Marca de Lujo 3"}, "showcase_v2_price_3": {"type": "text", "name": "Precio/Subtexto Item 3 (Opcional)"}, "showcase_v2_link_3": {"type": "link", "name": "Enlace Item 3"}, "showcase_v2_heading_4": {"type": "heading", "name": "Item 4"}, "showcase_v2_image_4": {"type": "image", "name": "Imagen Item 4"}, "showcase_v2_title_4": {"type": "text", "name": "Título Item 4", "default": "Marca de Lujo 4"}, "showcase_v2_price_4": {"type": "text", "name": "Precio/Subtexto Item 4 (Opcional)"}, "showcase_v2_link_4": {"type": "link", "name": "Enlace Item 4"}, "showcase_v2_heading_5": {"type": "heading", "name": "Item 5"}, "showcase_v2_image_5": {"type": "image", "name": "Imagen Item 5"}, "showcase_v2_title_5": {"type": "text", "name": "T<PERSON>tulo <PERSON> 5", "default": "Marca de Lujo 5"}, "showcase_v2_price_5": {"type": "text", "name": "Precio/Subtexto Item 5 (Opcional)"}, "showcase_v2_link_5": {"type": "link", "name": "Enlace Item 5"}, "showcase_v2_heading_6": {"type": "heading", "name": "Item 6"}, "showcase_v2_image_6": {"type": "image", "name": "Imagen Item 6"}, "showcase_v2_title_6": {"type": "text", "name": "<PERSON><PERSON><PERSON><PERSON> 6", "default": "Marca de Lujo 6"}, "showcase_v2_price_6": {"type": "text", "name": "Precio/Subtexto Item 6 (Opcional)"}, "showcase_v2_link_6": {"type": "link", "name": "<PERSON><PERSON>em 6"}}}, "category_buttons": {"name": "Botones de Categoría", "icon": "list", "options": {"category_buttons_enabled": {"type": "checkbox", "name": "Mostrar Botones de Categoría", "default": false}, "category_buttons_title": {"type": "text", "name": "Título de la Sección", "default": "Nuestras Categorías"}, "category_buttons_title_font": {"type": "google_font", "name": "Fuente del Título", "default": "<PERSON><PERSON><PERSON>", "options": [{"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Montserrat", "value": "Montserrat"}, {"label": "Noto Sans", "value": "Noto Sans"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Open Sans", "value": "Open Sans"}, {"label": "Open Sans Condensed", "value": "Open Sans Condensed"}, {"label": "PT Sans", "value": "PT Sans"}, {"label": "PT Serif", "value": "PT Serif"}, {"label": "<PERSON><PERSON><PERSON> (by <PERSON><PERSON><PERSON>)", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Roboto", "value": "Roboto"}]}, "category_buttons_show_divider": {"type": "checkbox", "name": "Mostrar Línea Divisora", "default": true}, "category_buttons_new_tab": {"type": "checkbox", "name": "<PERSON><PERSON><PERSON> en Nueva Pestaña", "default": false}, "category_buttons_max_width": {"type": "text", "name": "<PERSON><PERSON> Contenedor", "default": "1660px"}, "category_buttons_margin": {"type": "text", "name": "Margen del Contenedor", "default": "2rem auto"}, "category_buttons_spacing": {"type": "text", "name": "Espac<PERSON> en<PERSON> Bo<PERSON>", "default": "1rem"}, "category_buttons_padding": {"type": "text", "name": "Padding de los Botones", "default": "0.75rem 1.5rem"}, "category_buttons_min_width": {"type": "text", "name": "<PERSON><PERSON>", "default": "120px"}, "category_buttons_divider_height": {"type": "text", "name": "Altura de la Línea Divisora", "default": "1px"}, "category_buttons_primary_color": {"type": "color", "name": "Color Principal", "default": "#1a1a1a"}, "category_buttons_secondary_color": {"type": "color", "name": "Color Secundario", "default": "#ffffff"}, "category_buttons_accent_color": {"type": "color", "name": "Color de Acento", "default": "#d4af37"}, "category_buttons_border_color": {"type": "color", "name": "Color de Bordes", "default": "#e5e5e5"}, "category_button_1_title": {"type": "text", "name": "Botón 1 - Título"}, "category_button_1_link": {"type": "link", "name": "Botón 1 - <PERSON>lace"}, "category_button_1_active": {"type": "checkbox", "name": "Botón 1 - Activo", "default": false}, "category_button_2_title": {"type": "text", "name": "Botón 2 - <PERSON><PERSON><PERSON><PERSON>"}, "category_button_2_link": {"type": "link", "name": "Botón 2 - <PERSON><PERSON>"}, "category_button_2_active": {"type": "checkbox", "name": "Botón 2 - Activo", "default": false}, "category_button_3_title": {"type": "text", "name": "Botón 3 - <PERSON><PERSON><PERSON><PERSON>"}, "category_button_3_link": {"type": "link", "name": "Botón 3 - <PERSON><PERSON>"}, "category_button_3_active": {"type": "checkbox", "name": "Botón 3 - Activo", "default": false}, "category_button_4_title": {"type": "text", "name": "Botón 4 - <PERSON><PERSON><PERSON><PERSON>"}, "category_button_4_link": {"type": "link", "name": "Botón 4 - <PERSON><PERSON>"}, "category_button_4_active": {"type": "checkbox", "name": "Botón 4 - Activo", "default": false}, "category_button_5_title": {"type": "text", "name": "Botón 5 - <PERSON><PERSON><PERSON><PERSON>"}, "category_button_5_link": {"type": "link", "name": "Botón 5 - <PERSON><PERSON>"}, "category_button_5_active": {"type": "checkbox", "name": "Botón 5 - Activo", "default": false}, "category_button_6_title": {"type": "text", "name": "<PERSON><PERSON><PERSON> 6 - <PERSON><PERSON><PERSON><PERSON>"}, "category_button_6_link": {"type": "link", "name": "<PERSON><PERSON><PERSON> 6 - <PERSON><PERSON>"}, "category_button_6_active": {"type": "checkbox", "name": "Botón 6 - <PERSON><PERSON>", "default": false}}}}