{% if options.banners_display %}
  {% if options.banner_1_image == empty and options.banner_2_image == empty and options.banner_3_image == empty and options.banner_4_image == empty and options.banner_5_image == empty and options.banner_6_image == empty %}
  <div id="banner-section" class="row">
    <div class="banner-home col-12 col-md-6">
      <img src="{{ 'banner-demo-1.jpg' | asset }}" />
      <div class="banner-info">
          <h4>{% t 'Personalize this banner'%}</h4>
          <a class="btn btn-link" href="{{store.url}}/admin/themes/options/{{theme.id}}">{% t 'Go to link'%}</a>
      </div>
    </div>
    <div class="banner-home col-12 col-md-6">
      <img src="{{ 'banner-demo-2.jpg' | asset }}" />
      <div class="banner-info">
          <h4>{% t 'Personalize this banner'%}</h4>
          <a class="btn btn-link" href="{{store.url}}/admin/themes/options/{{theme.id}}">{% t 'Go to link'%}</a>
      </div>
    </div>
    <div class="banner-home col-12 col-md-6">
      <img src="{{ 'banner-demo-3.jpg' | asset }}" />
      <div class="banner-info">
          <h4>{% t 'Personalize this banner'%}</h4>
          <a class="btn btn-link" href="{{store.url}}/admin/themes/options/{{theme.id}}">{% t 'Go to link'%}</a>
      </div>
    </div>
    <div class="banner-home col-12 col-md-6">
      <img src="{{ 'banner-demo-4.jpg' | asset }}" />
      <div class="banner-info">
          <h4>{% t 'Personalize this banner'%}</h4>
          <a class="btn btn-link" href="{{store.url}}/admin/themes/options/{{theme.id}}">{% t 'Go to link'%}</a>
      </div>
    </div>
    <div class="banner-home col-12 col-md-6">
      <img src="{{ 'banner-demo-5.jpg' | asset }}" />
      <div class="banner-info">
          <h4>{% t 'Personalize this banner'%}</h4>
          <a class="btn btn-link" href="{{store.url}}/admin/themes/options/{{theme.id}}">{% t 'Go to link'%}</a>
      </div>
    </div>
    <div class="banner-home col-12 col-md-6">
      <img src="{{ 'banner-demo-6.jpg' | asset }}" />
      <div class="banner-info">
          <h4>{% t 'Personalize this banner'%}</h4>
          <a class="btn btn-link" href="{{store.url}}/admin/themes/options/{{theme.id}}">{% t 'Go to link'%}</a>
      </div>
    </div>
  </div>
  {% else %}
  <div class="row">
    {% if options.banner_1_image != empty %}
    <div class="banner-home col-12 col-md-6">
      <a href="{{ options.banner_1_link}}"><img src="{{ options.banner_1_image | resize: '600x350'}}" /></a>
      <div class="banner-info">
        {% if options.banner_1_title != empty %}
        <h4>{{ options.banner_1_title }}</h4>
        {% endif %}
        {% if options.banner_1_link_text != empty %}
        <a class="btn btn-link" href="{{ options.banner_1_link}}">{{ options.banner_1_link_text }}</a>
        {% endif %}
      </div>
    </div>
    {% endif %}
    {% if options.banner_2_image != empty %}
    <div class="banner-home col-12 col-md-6">
      <a href="{{ options.banner_2_link}}"><img src="{{ options.banner_2_image | resize: '600x350'}}" /></a>
      <div class="banner-info">
        {% if options.banner_2_title != empty %}
        <h4>{{ options.banner_2_title }}</h4>
        {% endif %}
        {% if options.banner_2_link_text != empty %}
        <a class="btn btn-link" href="{{ options.banner_2_link}}">{{ options.banner_2_link_text }}</a>
        {% endif %}
      </div>
    </div>
    {% endif %}
    {% if options.banner_3_image != empty %}
    <div class="banner-home col-12 col-md-6">
      <a href="{{ options.banner_3_link}}"><img src="{{ options.banner_3_image | resize: '600x350'}}" /></a>
      <div class="banner-info">
        {% if options.banner_3_title != empty %}
        <h4>{{ options.banner_3_title }}</h4>
        {% endif %}
        {% if options.banner_3_link_text != empty %}
        <a class="btn btn-link" href="{{ options.banner_3_link}}">{{ options.banner_3_link_text }}</a>
        {% endif %}
      </div>
    </div>
    {% endif %}
    {% if options.banner_4_image != empty %}
    <div class="banner-home col-12 col-md-6">
      <a href="{{ options.banner_4_link}}"><img src="{{ options.banner_4_image | resize: '600x350'}}" /></a>
      <div class="banner-info">
        {% if options.banner_4_title != empty %}
        <h4>{{ options.banner_4_title }}</h4>
        {% endif %}
        {% if options.banner_4_link_text != empty %}
        <a class="btn btn-link" href="{{ options.banner_4_link}}">{{ options.banner_4_link_text }}</a>
        {% endif %}
      </div>
    </div>
    {% endif %}
    {% if options.banner_5_image != empty %}
    <div class="banner-home col-12 col-md-6">
      <a href="{{ options.banner_5_link}}"><img src="{{ options.banner_5_image | resize: '600x350'}}" /></a>
      <div class="banner-info">
        {% if options.banner_5_title != empty %}
        <h4>{{ options.banner_5_title }}</h4>
        {% endif %}
        {% if options.banner_5_link_text != empty %}
        <a class="btn btn-link" href="{{ options.banner_5_link}}">{{ options.banner_5_link_text }}</a>
        {% endif %}
      </div>
    </div>
    {% endif %}
    {% if options.banner_6_image != empty %}
    <div class="banner-home col-12 col-md-6">
      <a href="{{ options.banner_6_link}}"><img src="{{ options.banner_6_image | resize: '600x350'}}" /></a>
      <div class="banner-info">
        {% if options.banner_6_title != empty %}
        <h4>{{ options.banner_6_title }}</h4>
        {% endif %}
        {% if options.banner_6_link_text != empty %}
        <a class="btn btn-link" href="{{ options.banner_6_link}}">{{ options.banner_6_link_text }}</a>
        {% endif %}
      </div>
    </div>
    {% endif %}
  </div>
  {% endif %}
{% endif %}
