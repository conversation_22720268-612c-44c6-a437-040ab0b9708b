<!DOCTYPE html>
<!--[if IE 9]><html class="lt-ie10" lang="en" > <![endif]-->
<html class="no-js" lang="{{ languages.first.locale }}" xmlns="http://www.w3.org/1999/xhtml">
<!--<![endif]-->

<head>
  <title>{{page_title}}</title>
  <meta name="description" content="{{meta_description}}" />
  <!-- ESTE ES UN COMENTARIO PARA VER SI ESTO SE CARGA EN LA PAGINA PRINCIPAL -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">

  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

  <meta name="robots" content="follow, all" />

  <!-- Set the viewport width to device width for mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  {% include 'og_meta_tags' %}

  {% if languages.size > 1 %}
  {% for language in languages %}
  <link rel="alternate" hreflang="{{language.locale | replace: '-', '_'}}" href="{{language.url}}" />
  {% endfor %}
  {% endif %}

  {%if template == "category" %}
  <link rel="canonical" href="{{category.url}}">
  {%else%}
  <link rel="canonical" href="{{current_url}}">
  {%endif%}

  <link rel="icon" type="image/x-icon"  href="{% if options.favicon != empty %}{{ options.favicon }}{% else %}{{"favicon.png" | asset }}{% endif %}">

  {{ 'jquery/3.3.1/jquery.min.js' | public_asset_tag }}

  <link rel="stylesheet" href="//stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css">
  <link rel="stylesheet" type="text/css" href="{{ 'app.css' | asset }}" />
  <link rel="stylesheet" type="text/css" href="{{ 'style.css' | asset }}" />
  <link rel="stylesheet" type="text/css" href="{{ 'color_pickers.css' | asset }}" />
  <link rel="stylesheet" type="text/css" href="{{ 'linear-icon.css' | asset }}" />
  {% if template == 'home' or template == 'product' %}
  <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
  <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css">
  <script src="//cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
  <script src="//cdnjs.cloudflare.com/ajax/libs/Swiper/11.0.5/swiper-bundle.min.js"></script>
  <link
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
  rel="stylesheet"
/>
<link
  href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css"
  rel="stylesheet"
/>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  {% endif %}

  {% include 'schema' %}

  {% if options.head_code != empty %}
  <!-- Custom head code -->
  {{options.head_code}}
  {% endif %}
</head>

<body {% if template =='home' %}class="home" {% endif %}>

  <!--[if lt IE 8]>
<p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
<![endif]-->
  {% if template == 'cart' or template == 'checkout' or template == 'revieworder' or template == 'success' %}
  <header class="checkout-header container">
    <div class="row">
      <div class="col-9">
        <div class="logo text-center mt-3">
          <a href="{{store.url}}" title="{{store.name}}" class="navbar-brand">
            {% if store.logo != empty %}
            <img src="{{store.logo}}" class="store-image" alt="{{store.name}}" />
            {% else %}
            {% if template == 'home' %}
            <h1><span class="text-logo">{{store.name}}</span></h1>
            {% else %}
            <span class="text-logo">{{store.name}}</span>
            {% endif %}
            {% endif %}
          </a>
        </div>
      </div>
    </div>
  </header>
  {% else %}
  <!-- Navigation -->
  <header class="site-header">
  <!-- Sección de mensajes y acciones superiores -->
  <div class="top-message">
    <div class="wrapper">
      <!-- Mensaje superior configurable -->
      <div class="message">
        {% if options.display_top_message %}
          <p><i class="linear-icon icon-{{options.top_message_icon}}"></i> {{ options.top_message}}</p>
        {% endif %}
      </div>
      
      <!-- Acciones superiores: moneda, idioma, cuenta -->
      <ul class="top-actions">
        <!-- Selector de moneda -->
        {% if options.currencies == blank %}
          <li class="dropdown no-currency-selected"></li>
        {% elsif options.currencies != blank and options.open_exchange_rates_token != blank %}
          <li class="dropdown">
            <a href="#" class="dropdown-toggle nav-link trsn" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
              <span id="current_currency">{{store.currency_code}}</span>
              <span class="caret"></span>
            </a>
            <ul class="dropdown-menu">
              <li><a href="#" onclick="Jumpseller.setCurrency('{{store.currency_code}}');" class="trsn nav-link" title="{{store.currency_code}}">{{store.currency_code}}</a></li>
              {% assign store_currencies = options.currencies | split: ',' %}
              {% for currency in store_currencies %}
                <li><a href="#" onclick="Jumpseller.setCurrency('{{currency}}');" class="trsn nav-link" title="{{currency}}">{{currency}}</a></li>
              {% endfor %}
            </ul>
          </li>
        {% endif %}

        <!-- Selector de idioma -->
        {% if languages.size > 1 %}
          <li class="dropdown">
            <a href="#" class="dropdown-toggle trsn nav-link" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
              <span>{{languages.first.name}}</span>
              <span class="caret"></span>
            </a>
            <ul class="dropdown-menu">
              {% for language in languages %}
                <li><a href="{{language.url}}" class="trsn nav-link" title="{{language.name}}">{{language.name}}</a></li>
              {% endfor %}
            </ul>
          </li>
        {% endif %}

        <!-- Enlaces de cuenta de cliente -->
        {% if store.customers_enabled %}
          <li {% if template=="customer_account" %}class="active"{% endif %}>
            <a href="{% if customer %}{{customer_account_url}}{% else %}{{customer_login_url}}{% endif %}" id="login-link-desktop" class="trsn nav-link" title="{% if customer %}{% t "See my Details" %}{% else %}{% t "Login to" %}{{store.name}}{% endif %}">
              <span class="customer-name">
                {% if customer %}{{ customer.name }}{% else %}{% t "Login" %}{% endif %}
              </span>
            </a>
          </li>
        {% endif %}
        
        {% if customer %}
          <li>
            <a title="{% t 'Logout' %}" href="{{customer.logout_url}}" class="nav-link">
              <span>{% t 'Logout' %}</span>
            </a>
          </li>
        {% endif %}
      </ul>
    </div>
  </div>

  <!-- Cabecera principal -->
  <div class="container main-header">
    <div class="row">
      <!-- Sección izquierda: logo y botón de navegación móvil -->
      <div class="col-3 text-left left-side">
        <!-- Logo versión desktop -->
        <div class="logo d-none d-lg-block">
          <a href="{{store.url}}" title="{{store.name}}" class="navbar-brand">
            {% if store.logo != empty %}
              <img src="{{store.logo}}" class="store-image" alt="{{store.name}}" />
            {% else %}
              {% if template == 'home' %}
                <h1><span class="text-logo">{{store.name}}</span></h1>
              {% else %}
                <span class="text-logo">{{store.name}}</span>
              {% endif %}
            {% endif %}
          </a>
        </div>
        
        <!-- Botón navegación móvil -->
        <button id="mobile-nav-btn" class="btn btn-link d-block d-lg-none">
          <i class="linear-icon icon-0812-menu"></i>
        </button>
        
        <!-- Navegación móvil -->
        <div id="mobile-nav" class="absolute-bg" style="display: none">
          <nav id="navbarMobile" class="lateral-box left">
            <button id="close-mobile-nav-btn" class="btn btn-link btn-block text-left lateral-header-btn">
              <i class="linear-icon icon-0811-cross pr-2 pb-2"></i> <span>{% t 'Close' %}</span>
            </button>
            
            <!-- Menú principal móvil -->
            <ul class="navbar-nav">
              {% for item in menu.main.items %}
                {% include 'navigation_mobile_menu' with item %}
              {% endfor %}
            </ul>
            
            <!-- Utilidades en móvil -->
            <ul class="navbar-nav utility-nav">
              <!-- Selector de moneda en móvil -->
              {% if options.currencies != blank and options.open_exchange_rates_token != blank %}
                <li class="nav-item">
                  <a href="#" class="button" data-id="currency-action">
                    <span id="current_currency">{{store.currency_code}}</span>
                    <i class="linear-icon icon-0823-plus"></i>
                  </a>
                  <ul id="currency-action" class="sub-menu">
                    <li><a href="#" onclick="Jumpseller.setCurrency('{{store.currency_code}}');" class="trsn" title="{{store.currency_code}}">{{store.currency_code}}</a></li>
                    {% assign store_currencies = options.currencies | split: ',' %}
                    {% for currency in store_currencies %}
                      <li><a href="#" onclick="Jumpseller.setCurrency('{{currency}}');" class="trsn" title="{{currency}}">{{currency}}</a></li>
                    {% endfor %}
                  </ul>
                </li>
              {% endif %}

              <!-- Selector de idioma en móvil -->
              {% if languages.size > 1 %}
                <li class="nav-item">
                  <a href="#" class="button" data-id="languages-action">
                    <span>{{languages.first.name}}</span>
                    <i class="linear-icon icon-0823-plus"></i>
                  </a>
                  <ul id="languages-action" class="sub-menu">
                    {% for language in languages %}
                      <li><a href="{{language.url}}" class="trsn" title="{{language.name}}">{{language.name}}</a></li>
                    {% endfor %}
                  </ul>
                </li>
              {% endif %}

              <!-- Cuenta de cliente en móvil -->
              {% if store.customers_enabled %}
                <li class="nav-item{% if template == "customer_account" %}active{% endif %}">
                  <a href="{% if customer %}{{customer_account_url}}{% else %}{{customer_login_url}}{% endif %}" id="login-link" class="trsn" title="{% if customer %}{% t "See my Details" %}{% else %}{% t "Login to" %}{{store.name}}{% endif %}">
                    <span class="customer-name">
                      {% if customer %}{{ customer.name }}{% else %}{% t "Login" %}{% endif %}
                    </span>
                  </a>
                </li>
              {% endif %}
              
              {% if customer %}
                <li class="nav-item">
                  <a title="{% t 'Logout' %}" href="{{customer.logout_url}}">
                    <span>{% t 'Logout' %}</span>
                  </a>
                </li>
              {% endif %}
            </ul>
            
            <!-- Redes sociales en móvil -->
            <ul class="social list-inline social-networks">
              {% if social.facebook_url != blank %}
                <li class="list-inline-item">
                  <a href="{{ social.facebook_url }}" class="trsn" title="{% t 'Go to' %} Facebook" target="_blank">
                    <i class="fab fa-facebook-f"></i>
                  </a>
                </li>
              {% endif %}

              {% if social.twitter_url != blank %}
                <li class="list-inline-item">
                  <a href="{{ social.twitter_url }}" class="trsn" title="{% t 'Go to' %} Twitter" target="_blank">
                    <i class="fab fa-twitter"></i>
                  </a>
                </li>
              {% endif %}

              {% if social.pinterest_url != blank %}
                <li class="list-inline-item">
                  <a href="{{ social.pinterest_url }}" class="trsn" title="{% t 'Go to' %} Pinterest" target="_blank">
                    <i class="fab fa-pinterest"></i>
                  </a>
                </li>
              {% endif %}
              
              {% if social.instagram_url != blank %}
                <li class="list-inline-item">
                  <a href="{{ social.instagram_url }}" class="trsn" title="{% t 'Go to' %} Instagram" target="_blank">
                    <i class="fab fa-instagram"></i>
                  </a>
                </li>
              {% endif %}
              
              {% if social.whatsapp_url != blank %}
                <li class="list-inline-item">
                  <a href="{{ social.whatsapp_url }}&text={% t 'Hello'%}!" class="trsn" title="{% t 'Go to' %} Whatsapp" target="_blank">
                    <i class="fab fa-whatsapp"></i>
                  </a>
                </li>
              {% endif %}
              
              {% if social.youtube_url != blank %}
                <li class="list-inline-item">
                  <a href="{{ social.youtube_url }}" class="trsn" title="{% t 'Go to' %} Youtube" target="_blank">
                    <i class="fab fa-youtube"></i>
                  </a>
                </li>
              {% endif %}
            </ul>
          </nav>
        </div>
      </div>
      
      <!-- Sección derecha: búsqueda y carrito -->
      <div class="col-7 text-right">
        <!-- Formulario de búsqueda -->
        <form id="search_mini_form" method="get" action="{{search.url_send}}" style="display:none">
          <div class="input-group">
            <input type="text" value="{{search.query}}" name="q" class="form-control form-control-sm" onFocus="javascript:this.value=''" placeholder="{% t 'Search for products' %}" />
            <button type="submit" class="btn btn-link"><i class="linear-icon icon-0803-magnifier"></i></button>
          </div>
        </form>
        
        <!-- Botones de búsqueda y carrito -->
        <button id="search-btn" class="btn btn-link"><i class="linear-icon icon-0803-magnifier"></i></button>
        
        {% if options.disable_shopping_cart != true %}
          <button id="cart-btn" class="btn btn-link">
            <i class="linear-icon icon-0334-cart"></i>
            <span class="">{{ order.products_count }}</span>
          </button>
        {% endif %}

        <!-- Panel lateral de carrito -->
        <div id="cart" class="absolute-bg" style="display: none">
          <div class="lateral-box right">
            {% include 'cart_lateral' %}
          </div>
        </div>
      </div>
    </div>
    
    <!-- Logo móvil y menú principal -->
    <div class="row">
      <div class="col-12">
        <!-- Logo versión móvil -->
        <div class="logo d-block d-lg-none">
          <a href="{{store.url}}" title="{{store.name}}" class="navbar-brand">
            {% if store.logo != empty %}
              <img src="{{store.logo}}" class="store-image" alt="{{store.name}}" />
            {% else %}
              {% if template == 'home' %}
                <h1><span class="text-logo">{{store.name}}</span></h1>
              {% else %}
                <span class="text-logo">{{store.name}}</span>
              {% endif %}
            {% endif %}
          </a>
        </div>
        
        <!-- Navegación principal desktop -->
        <nav class="navbar navbar-expand-lg navbar-light d-none d-lg-block {% if options.megamenu %}{% else %}vertical_menu{% endif %}">
          <div class="collapse navbar-collapse {{options.position_nav}}" id="navbarsContainer-2">
            <ul class="navbar-nav">
              {% for item in menu.main.items %}
                {% include 'navigation_menu' with item %}
              {% endfor %}
            </ul>
          </div>
        </nav>
      </div>
    </div>
  </div>
</header>
  {% endif %}
  <!-- Page Content -->
  {{content}}
  <!-- Footer -->
  {% if template == 'cart' or template == 'checkout' or template == 'revieworder' or template == 'success' %}
  {% else %}
  {% comment %}
  {% include 'features_footer' %}
  {% endcomment %}
  {% endif %}
  {% include 'footer' %}
  <!-- /.container -->

  <!-- Css -->
  <link rel="stylesheet" href="//use.fontawesome.com/releases/v5.7.2/css/all.css" integrity="sha384-fnmOCqbTlWIlj8LyTjo7mOUStjsKC4pOpQbqyi7RrhN7udi9RwhKkMHpvLbHG9Sr" crossorigin="anonymous">
  {% if options.display_cart_notification %}
  <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css">
  {% endif %}

  <!-- Bootstrap Core JavaScript -->
  <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js"></script>
  <script src="//stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>

  <!-- Script to Activate Tooltips -->
  <script>
    $(function() {
      $('[data-toggle="tooltip"]').tooltip()
      $('.carousel').carousel()
    })
  </script>

  <script src="//cdn.jsdelivr.net/bootstrap.filestyle/1.1.0/js/bootstrap-filestyle.min.js"></script>
  <script src="{{ 'main.js' | asset }}"></script>

  {% if options.currencies != blank and options.open_exchange_rates_token != blank %}
  <script src="//cdnjs.cloudflare.com/ajax/libs/money.js/0.2.0/money.min.js"></script>
  <script src="//cdnjs.cloudflare.com/ajax/libs/accounting.js/0.4.1/accounting.min.js"></script>
  <script>
    var open_exchange_rates_token = '{{options.open_exchange_rates_token}}';
    var i18n_decimal_mark = '{{store.i18n_decimal_mark}}';

    if (typeof(Storage) !== "undefined") {
      if (sessionStorage.getItem('global_currency') == null) {
        sessionStorage.setItem('global_currency', '{{ store.currency_code }}');
        sessionStorage.setItem('store_currency', '{{ store.currency_code }}');
      }
    } else {
      // Sorry! No Web Storage support..
      console.log("Unable to use multi-currency on this store. Please update your browser.");
      $('#current_currency').parents('li').hide();
    }
  </script>
  {% endif %}

  {% if options.display_cart_notification %}
  <script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
  <script>
    var shoppingCartMessage = '{% t "Go to Shopping Cart." %}';
    var singleProductMessage = '{% t "has been added to the shopping cart." %}'
    var multiProductMessage = '{% t "have been added to the shopping cart." %}'
  </script>
  <script src="{{ 'addtocart.js' | asset }}"></script>
  {% endif %}

  {% include 'fonts' %}

  <script src="{{ 'theme.js' | asset }}"></script>

  {% if options.body_code != empty %}
  <!-- Custom body code -->
  {{options.body_code}}
  {% endif %}
</body>

</html>