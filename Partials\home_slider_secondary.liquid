{% if options.secondary_slider_display %}
    {% if options.secondary-slider-1-image == empty and options.secondary-slider-2-image == empty and options.secondary-slider-3-image == empty and options.secondary-slider-4-image == empty and options.secondary-slider-5-image == empty and options.secondary-slider-6-image == empty %}
    <div id="secondary-slider" class="owl-carousel home-slider secondary">
        <div class="item">
            <div class="layer"></div>
            <img src="{{ 'secondary-slide-demo-1.jpg' | asset }}" alt="{{store.name}}">
            <a href="{{store.url}}/admin/themes/options/{{theme.id}}">
              <div class="carousel-info-inner">
                    <div class="btn btn-primary"
                        title="{% t 'Add Slider 1' %}">{% t 'Add Slider 1' %}
                    </div>
               </div>
            </a>
        </div>
        <div class="item">
            <div class="layer"></div>
            <img src="{{ 'secondary-slide-demo-2.jpg' | asset }}" alt="{{store.name}}">
            <a href="{{store.url}}/admin/themes/options/{{theme.id}}" >
              <div class="carousel-info-inner">
                    <div class="btn btn-primary"
                        title="{% t 'Add Slider 2' %}">{% t 'Add Slider 2' %}
                    </div>
                </div>
            </a>
        </div>
        <div class="item">
            <div class="layer"></div>
            <img src="{{ 'secondary-slide-demo-3.jpg' | asset }}" alt="{{store.name}}">
            <a href="{{store.url}}/admin/themes/options/{{theme.id}}" >
              <div class="carousel-info-inner">
                    <div class="btn btn-primary"
                        title="{% t 'Add Slider 3' %}">{% t 'Add Slider 3' %}
                    </div>
              </div>
            </a>
        </div>
        <div class="item">
            <div class="layer"></div>
            <img src="{{ 'secondary-slide-demo-4.png' | asset }}" alt="{{store.name}}">
            <a href="{{store.url}}/admin/themes/options/{{theme.id}}" >
                <div class="carousel-info-inner">
                  <div class="btn btn-primary"
                      title="{% t 'Add Slider 4' %}">{% t 'Add Slider 4' %}
                  </div>
              </div>
            </a>
        </div>
        <div class="item">
            <div class="layer"></div>
            <img src="{{ 'secondary-slide-demo-4.png' | asset }}" alt="{{store.name}}">
            <a href="{{store.url}}/admin/themes/options/{{theme.id}}">
                   <div class="carousel-info-inner">
                  <div class="btn btn-primary"
                      title="{% t 'Add Slider 5' %}">{% t 'Add Slider 5' %}
                  </div>
              </div>
            </a>
        </div>
        <div class="item">
            <div class="layer"></div>
            <img src="{{ 'secondary-slide-demo-4.png' | asset }}" alt="{{store.name}}">
            <a href="{{store.url}}/admin/themes/options/{{theme.id}}" >
                <div class="carousel-info-inner">
                  <div class="btn btn-primary"
                      title="{% t 'Add Slider 6' %}">{% t 'Add Slider 6' %}
                  </div>
              </div>
            </a>
        </div>
    </div>
    {% else %}
    <div id="secondary-slider" class="owl-carousel home-slider secondary">
        {% if options.secondary-slider-1-image != empty %}
        <div class="item">
            <div class="layer"></div>
            <img src="{{ options.secondary-slider-1-image | resize: '700x800'}}"  alt="{{store.name}}">
            {% if options.secondary-slider-1-link != empty %}
              <a href="{{ options.secondary-slider-1-link }}">
              <div class="carousel-info-inner">
                {% if options.secondary-slider-1-text-button != empty %}
                <button class="btn btn-primary">{{options.secondary-slider-1-text-button }}</button>
                {% endif %}
              </div>
              </a>
            {% endif %}
        </div>
        {% endif %}
        {% if options.secondary-slider-2-image != empty %}
        <div class="item">
            <div class="layer"></div>
            <img src="{{ options.secondary-slider-2-image | resize: '700x800' }}" alt="{{store.name}}">
            {% if options.secondary-slider-2-link != empty %}
              <a href="{{ options.secondary-slider-2-link }}">
              <div class="carousel-info-inner">
                {% if options.secondary-slider-2-text-button != empty %}
                <button class="btn btn-primary">{{options.secondary-slider-2-text-button }}</button>
                {% endif %}
              </div>
              </a>
            {% endif %}
        </div>
        {% endif %}
        {% if options.secondary-slider-3-image != empty %}
        <div class="item">
            <div class="layer"></div>
            <img src="{{ options.secondary-slider-3-image | resize: '700x800' }}" alt="{{store.name}}">
            {% if options.secondary-slider-3-link != empty %}
              <a href="{{ options.secondary-slider-3-link }}">
              <div class="carousel-info-inner">
                {% if options.secondary-slider-3-text-button != empty %}
                <button class="btn btn-primary">{{options.secondary-slider-3-text-button }}</button>
                {% endif %}
              </div>
              </a>
            {% endif %}
        </div>
        {% endif %}
        {% if options.secondary-slider-4-image != empty %}
        <div class="item">
            <div class="layer"></div>
            <img src="{{ options.secondary-slider-4-image | resize: '700x800' }}" alt="{{store.name}}">
            {% if options.secondary-slider-4-link != empty %}
              <a href="{{ options.secondary-slider-4-link }}">
              <div class="carousel-info-inner">
                {% if options.secondary-slider-4-text-button != empty %}
                <button class="btn btn-primary">{{options.secondary-slider-4-text-button }}</button>
                {% endif %}
              </div>
              </a>
            {% endif %}
        </div>
        {% endif %}
        {% if options.secondary-slider-5-image != empty %}
        <div class="item">
            <div class="layer"></div>
            <img src="{{ options.secondary-slider-5-image | resize: '700x800' }}" alt="{{store.name}}">
            {% if options.secondary-slider-5-link != empty %}
              <a href="{{ options.secondary-slider-5-link }}">
              <div class="carousel-info-inner">
                {% if options.secondary-slider-5-text-button != empty %}
                <button class="btn btn-primary">{{options.secondary-slider-5-text-button }}</button>
                {% endif %}
              </div>
              </a>
            {% endif %}
        </div>
        {% endif %}
        {% if options.secondary-slider-6-image != empty %}
        <div class="item">
            <div class="layer"></div>
            <img src="{{ options.secondary-slider-6-image | resize: '700x800'}}" alt="{{store.name}}">
            {% if options.secondary-slider-6-link != empty %}
              <a href="{{ options.secondary-slider-6-link }}">
              <div class="carousel-info-inner">
                {% if options.secondary-slider-6-text-button != empty %}
                <button class="btn btn-primary">{{options.secondary-slider-6-text-button }}</button>
                {% endif %}
              </div>
              </a>
            {% endif %}
        </div>
        {% endif %}
    
    </div>
    {% endif %}
    {% endif %}
    <script>
      $('#secondary-slider').owlCarousel({
        loop: {% if options.secondary_slider_loop %}true{% else %}false{% endif %},
        autoplay: {% if options.secondary_slider_autoplay %}true{% else %} false{% endif %},
        autoplayHoverPause: {% if options.secondary_slider_pause %} true{% else %} false{% endif %},
        autoplayTimeout: {{ options.secondary_slider_autoplay_speed }},
        nav: true,
        dots: false,
        margin: 7,
        responsive:{
          0:{
              items:2
          },
          768:{
              items:3
          }
        }
      })
    </script>
    