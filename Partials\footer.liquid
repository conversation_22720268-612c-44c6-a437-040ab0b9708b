<footer class="site-footer {% if template == 'cart' or template == 'checkout' or template == 'revieworder' or template == 'success' %} checkout-footer{% endif %}">
    <div class="container">
      {% if template != 'cart' and template != 'checkout' and template != 'revieworder' and template != 'success' %}
      <div class="top-footer row">
        <div class="section col-12 col-md-3">
          <div class="logo">
            <a href="{{store.url}}" title="{{store.name}}">
              {% if store.logo != empty %}
              <img src="{{store.logo}}" class="store-image" alt="{{store.name}}" />
              {% else %}
              <h1><span class="text-logo">{{store.name}}</span></h1>
              {% endif %}
            </a>
          </div>
        </div>
        <div class="section col-12 col-md-3">
          <h3>{%t 'Menu' %}</h3>
          <ul class="menu">
            {% for item in menu.footer.items %}
            {% include 'footer_navigation_menu' with item %}
            {% endfor %}
          </ul>
        </div>
        {% if options.email or options.phone or options.address %}
        <div class="section col-12 col-md-3">
          <h3>{%t 'Contact Us' %}</h3>
  
          {% if options.display_contact_email %}
          <p><a href="mailto:{{ store.email }}" target="_blank" title="{{store.email}}">{{ store.email }}</a></p>
          {% endif %}
          {% if options.second_email != blank %}
          <p><a href="mailto:{{ options.second_email }}" target="_blank" title="{{options.second_email}}">{{ options.second_email }}</a></p>
          {% endif %}
          {% if options.phone != blank %}
          <p><a href="tel:{{ options.phone | remove:'(' | remove:')' | remove:' ' }}" title="{% t "Call us" %}">{{ options.phone }}</a></p>
          {% endif %}
          {% if store.address != empty %}
          {%comment%}
          <p>{{ store.address }}</p>
          {%endcomment%}
          {% endif %}
        </div>
        {% endif %}
        <div class="section col-12 col-md-3">
          {% if options.enable_subscribe %}
          {% include 'newsletter' %}
          {% endif %}
          <ul class="social list-inline social-networks mt-3">
            {% if social.facebook_url != blank %}
            <li class="list-inline-item">
              <a href="{{ social.facebook_url }}" class="trsn" title="{% t 'Go to' %} Facebook" target="_blank">
                <i class="fab fa-facebook-f"></i>
              </a>
            </li>
            {% endif %}
            {% if social.twitter_url != blank %}
            <li class="list-inline-item">
              <a href="{{ social.twitter_url }}" class="trsn" title="{% t 'Go to' %} Twitter" target="_blank">
                <i class="fab fa-twitter"></i>
              </a>
            </li>
            {% endif %}
            {% if social.pinterest_url != blank %}
            <li class="list-inline-item">
              <a href="{{ social.pinterest_url }}" class="trsn" title="{% t 'Go to' %} Pinterest" target="_blank">
                <i class="fab fa-pinterest"></i>
              </a>
            </li>
            {% endif %}
            {% if social.instagram_url != blank %}
            <li class="list-inline-item">
              <a href="{{ social.instagram_url }}" class="trsn" title="{% t 'Go to' %} Instagram" target="_blank">
                <i class="fab fa-instagram"></i>
              </a>
            </li>
            {% endif %}
            {% if social.whatsapp_url != blank %}
            <li class="list-inline-item">
              <a href="{{ social.whatsapp_url }}&text={% t 'Hello' %}%20{{store.name}}" class="trsn" title="WhatsApp" target="_blank">
                <i class="fab fa-whatsapp fa-fw"></i>
              </a>
            </li>
            {% endif %}
            {% if social.youtube_url != blank %}
            <li class="list-inline-item">
              <a href="{{ social.youtube_url }}" class="trsn" title="{% t 'Go to' %} Youtube" target="_blank">
                <i class="fab fa-youtube"></i>
              </a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
      {% endif %}
      <div class="bottom-footer row">
        <div class="col-12 col-md-6 order-2 order-md-1">
          <p class="powerd-by">&copy; {{ 'now' | date: "%Y" }} {{store.name}}. {% t "All Rights Reserved." %} </p>
        </div>
        <div class="col-12 col-md-6 order-1 order-md-2">
          {% include 'payment_options' %}
        </div>
      </div>
    </div>
  </footer>
  