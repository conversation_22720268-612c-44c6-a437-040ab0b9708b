/* General Styles */
.home h2.page-header {
    text-align: center;
  }
   h3 a {
   color:#2C3E50  !important; 
  }
  h6 {
   color: #2C3E50 !important; 
  }
  
  /*css para el boton de agotado*/
  .btn-dark  {
    background-color: #7623e2 !important;
    border-color: #7623e2 !important;  
    width: 233px !important;
    height: 38px !important;
    padding-top: 8px !important;
  }
  h6.subtitle {
    font-size: 18px;
    font-weight: 300;
    line-height: 30px;
    margin-bottom: 30px;
  }
  
  .line-through {
    text-decoration: line-through;
  }
  
  .social-networks li {
    width: 30px;
    height: 30px;
    border-radius: 5px;
    border: solid thin;
    text-align: center;
    vertical-align: bottom;
    margin-top: 10px;
  }
  
  .social-networks li i {
    font-size: 18px !important;
    line-height: 28px;
  }
  
  .social-networks li a {
    width: 100%;
    display: block;
  }
  
  .btn-primary.disabled,
  .btn-primary:disabled {
    background-color: #ccc;
    border-color: #ccc;
  }
  
  iframe {
    max-width: 100%;
  }
  
  /* Header */
  .top-message {
    padding: 10px;
  }
  
  .top-message .wrapper {
    display: flex;
    justify-content: space-between;
    max-width: 1140px;
    margin: 0 auto;
  }
  
  .top-message p {
    margin-bottom: 0;
    font-size: 14px;
  }
  
  .top-message i {
    padding-right: 10px;
    margin-left: 20px;
  }
  
  .top-message ul.top-actions {
    margin-bottom: 0;
    display: flex;
    display: none;
  }
  
  .top-message ul.top-actions li {
    border-right: solid thin;
  }
  
  .top-message ul.top-actions li:last-child {
    border-right: none;
  }
  
  .top-message .nav-link {
    padding: 0 10px !important;
    font-size: 11px;
  }
  
  .top-message ul.dropdown-menu.show {
    padding: 0;
    border-radius: 0;
    margin-top: 11px;
  }
  
  .top-message ul.dropdown-menu.show li a {
    padding-bottom: 5px !important;
  }
  
  .main-header {
    margin-top: 15px;
  }
  
  .main-header .btn-link {
    height: 40px;
    padding: 0 6px;
  }
  
  .logo {
    text-align: center;
    margin-bottom: 10px;
    margin-top: 20px;
  }
  
  .logo .navbar-brand {
    margin-right: 0;
    
  }
  
  .main-header .left-side {
    position: inherit;
  }
  
  .main-header i {
    font-size: 25px;
  }
  
  .main-header i.icon-0803-magnifier {
    font-size: 22px;
    vertical-align: bottom;
  }
  
  /* Ajuste para el logo en móviles */
  @media (max-width: 991.98px) {
    .logo .store-image {
      max-height: 40px; /* Ajusta esta altura según necesites */
      width: auto;
    }
    .logo .navbar-brand {
      padding-top: 0;
      padding-bottom: 0;
    }
  }
  
  .absolute-bg {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.2);
    bottom: 0;
    width: 100%;
    z-index: 20;
  }
  
  .lateral-box {
    position: fixed;
    top: 0;
    background: #fff;
    bottom: 0;
    width: 70%;
    max-width: 270px;
    z-index: 20;
    box-shadow: 1px 1px 27px;
    padding: 10px;
    overflow-x: scroll;
    min-width: 250px;
  }
  
  .lateral-box.left {
    left: 0;
  }
  
  .lateral-box.right {
    right: 0;
  }
  
  /* Buscador desktop */
  #search_mini_form {
    display: inline-block;
    min-width: 220px;
    max-width: 320px;
    width: auto;
    margin-right: 10px;
    vertical-align: middle;
  }
  
  #search_mini_form .input-group {
    width: 100%;
  }
  
  #search_mini_form input.form-control {
    width: 100%;
    min-width: 160px;
    padding: 6px 12px;
    font-size: 1rem;
    border: none;
    border-bottom: 1.5px solid #222;
    border-radius: 0;
    background: transparent;
    box-shadow: none;
  }
  
  #search_mini_form button.btn-link {
    padding: 0 8px;
    font-size: 1.2rem;
  }
  
  #search_mini_form input.form-control:focus {
    outline: none;
    border-bottom: 2px solid #000;
    background: #f8f8f8;
  }
  
  /* Buscador móvil */
  #search_mini_form_mobile {
    width: 100%;
    margin-top: 10px;
  }
  
  #search_mini_form_mobile .input-group {
    width: 100%;
  }
  
  #search_mini_form_mobile input.form-control {
    padding: 8px 12px;
    font-size: 1rem;
    border: 1px solid #ccc;
    border-radius: 4px 0 0 4px;
  }
  
  #search_mini_form_mobile button.btn-link {
    border: 1px solid #ccc;
    border-left: none;
    padding: 8px 12px;
    border-radius: 0 4px 4px 0;
    background-color: #f8f8f8;
  }
  
  .dropdown-toggle::after {
    display: none;
  }
  
  .dropdown-toggle i {
    font-size: 11px;
    padding-left: 5px;
  }
  
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
    border-radius: 0;
    padding: 0;
    border: none;
    top: 48px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
  }
  
  button#cart-btn span {
    width: 20px;
    height: 20px;
    display: flex;
    border-radius: 50%;
    position: absolute;
    top: 20px;
    right: 11px;
    color: #fff;
    align-items: center;
    justify-content: center;
    font-size: 11px;
  }
  
  ul.megamenu {
    position: absolute;
    margin-top: -8px;
    z-index: 2;
    width: 100%;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 1px 5px 8px rgba(0, 0, 0, 0.3);
    display: none;
    padding: 20px 10px;
  }
  
  ul.megamenu .megamenu {
    position: relative;
    top: 0;
    padding: 0;
    box-shadow: none;
  }
  
  ul.megamenu .megamenu ul {
    padding-right: 10px;
  }
  
  ul.megamenu i {
    display: none;
  }
  
  ul.megamenu .nav-item.level-0 {
    font-weight: bold;
    padding: 0 10px 20px;
  }
  
  ul.megamenu .nav-item.level-0 li {
    font-weight: 300;
    padding: 0;
  }
  
  ul.megamenu .nav-item.level-0 li a {
    font-size: 95% !important;
  }
  
  ul.megamenu .nav-item.level-0 .nav-link {
    padding-bottom: 5px !important;
    padding-top: 5px !important;
  }
  
  .nav-item.dropdown.sfhover {
    border-bottom: solid 3px;
  }
  
  .megamenu .nav-item.dropdown.sfhover {
    border-bottom: 0;
  }
  
  .nav-item.dropdown.sfhover .megamenu,
  .nav-item.dropdown:hover .megamenu {
    display: flex;
    flex-wrap: wrap;
  }
  
  .navbar-light .navbar-nav .nav-link:hover {
    color: rgba(0, 0, 0, 0.7);
    border-bottom: solid 3px;
  }
  
  .nav-item.dropdown.sfhover .megamenu .megamenu,
  .nav-item.dropdown:hover .megamenu .megamenu {
    display: block;
  }
  
  .nav-item.dropdown.sfhover .megamenu .megamenu .megamenu,
  .nav-item.dropdown:hover .megamenu .megamenu .megamenu {
    padding-left: 15px;
  }
  
  .navbar-light .navbar-nav .megamenu .nav-link:hover {
    border-bottom: none;
  }
  
  /* Vertical Menu */
  .vertical_menu ul.dropdown-menu.multi-level {
    margin: -3px 0 0 0;
    padding: 0;
    border: 0;
    top: 55px;
    border-radius: 0;
    position: absolute;
    left: auto;
    min-width: 160%;
    min-width: -webkit-max-content;
    min-width: -moz-max-content;
    line-height: 2.8rem;
    border-top: 3px solid;
    width: 100%;
  }
  
  .vertical_menu ul.dropdown-menu.multi-level .multi-level {
    top: 1px;
    left: 100%;
    min-width: 100%;
  }
  
  .vertical_menu #navbarsContainer-2 > ul.navbar-nav > li {
    display: inline-block;
    position: relative;
    height: 55px;
  }
  
  .vertical_menu ul.dropdown-menu.multi-level li.nav-item {
    float: left;
  }
  
  .vertical_menu ul.dropdown-menu.multi-level li.nav-item {
    float: none !important;
    min-width: 100%;
  }
  
  .vertical_menu ul.dropdown-menu.multi-level li.nav-item a {
    padding: 5px 15px !important;
  }
  
  .vertical_menu ul.dropdown-menu.multi-level li.nav-item a:hover {
    background: #eee;
  }
  
  .vertical_menu .nav-item.dropdown.sfhover {
    border-bottom: none;
  }
  
  .vertical_menu .multi-level .nav-link:hover {
    border-bottom: 0;
  }
  
  .vertical_menu .dropdown-menu.multi-level i.linear-icon.icon-0827-chevron-down {
    transform: rotate(-90deg);
    display: inline-block;
  }
  
  @media all and (min-width: 769px) {
    .logo {
      text-align: left;
      margin-top: 0;
    }
  
    .main-header .text-right {
      margin-top: 15px;
    }
  }
  
  @media all and (min-width: 991px) {
    .top-message ul.top-actions {
      display: flex;
    }
  }
  
  /* Mobile Menu */
  button#close-mobile-nav-btn {
    display: flex;
    align-items: center;
    height: 48px;
  }
  
  .mobile-nav .lateral-header-btn i {
    margin-right: 10px;
  }
  
  .lateral-header-btn {
    border-bottom: solid thin;
    border-radius: 0;
    padding: 5px 0;
    margin-bottom: 10px;
  }
  
  .lateral-header-btn span {
    text-transform: uppercase;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
  }
  
  .lateral-header-btn.btn-link:hover {
    border-bottom: solid thin;
    opacity: 0.6;
    text-decoration: none;
  }
  
  #mobile-nav .navbar-nav {
    padding-left: 10px;
  }
  
  #mobile-nav .navbar-nav > li > a {
    padding-top: 8px;
    padding-bottom: 8px;
    display: inline-block;
    width: 100%;
    font-weight: 600;
  }
  
  #mobile-nav .navbar-nav li.dropdown a {
    width: calc(100% - 50px);
    padding-top: 5px;
    padding-bottom: 5px;
    display: inline-block;
  }
  
  #mobile-nav .navbar-nav ul {
    padding-left: 0;
    margin-bottom: 10px;
    font-size: 14px;
  }
  
  #mobile-nav .navbar-nav ul i {
    font-size: 17px;
    float: right;
    margin-right: 7px;
  }
  
  #mobile-nav ul.navbar-nav li.dropdown ul.multi-level li.dropdown ul.multi-level {
    margin-left: 10px;
  }
  
  #mobile-nav .dropdown-btn {
    float: right;
    width: 50px;
    text-align: right;
    height: 30px;
    display: contents;
  }
  
  #mobile-nav .dropdown-btn i {
    font-size: 17px;
    float: right;
    margin-right: 7px;
  }
  
  .utility-nav {
    border-top: solid thin;
    margin-top: 20px;
    padding-top: 10px;
    margin-bottom: 20px;
  }
  
  .utility-nav a {
    padding-top: 8px;
    padding-bottom: 8px;
  }
  
  .utility-nav ul.sub-menu {
    display: none;
    padding-left: 15px !important;
  }
  
  .utility-nav i {
    font-size: 17px;
    line-height: 15px;
    float: right;
    margin-right: 7px;
  }
  
  #mobile-nav .list-inline-item:not(:last-child) {
    margin-right: 4px;
  }
  
  /* Cart Lateral */
  .cart-list {
    border-bottom: solid thin;
    padding-bottom: 15px;
    margin-bottom: 15px;
    margin-top: 30px;
    max-height: calc(100vh - 340px);
    overflow-y: scroll;
    overflow-x: hidden;
  }
  
  .cart-list h3.product-name {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 0;
  }
  
  .cart-list h6.product-brand {
    font-weight: 300;
    margin-bottom: 6px;
    font-size: 14px !important;
  }
  
  .cart-list .prod-mob {
    font-size: 20px;
  }
  
  .cart-list .row {
    margin-bottom: 8px;
    margin-left: 0;
    margin-right: 0;
  }
  
  .cart-wrapper .total h4 {
    text-transform: uppercase;
    font-size: 15px;
    line-height: 20px;
    font-weight: 300;
  }
  
  .cart-wrapper .total .price-mob {
    font-size: 20px;
    font-weight: bold;
  }
  
  button#close-cart-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  /* Footer */
  #footer-features {
    margin-top: 50px;
    position: relative;
    padding: 30px 0 20px;
  }
  
  #footer-features .item {
    display: flex;
    justify-content: flex-start;
    padding: 15px;
  }
  
  #footer-features .icon {
    margin-right: 20px;
  }
  
  #footer-features .icon i {
    font-size: 50px;
    vertical-align: top;
    line-height: 50px;
  }
  
  #footer-features h4 {
    font-size: 22px;
    font-weight: bold;
  }
  
  .top-footer {
    padding: 30px 5px 0;
    text-align: center;
  }
  
  .top-footer .logo {
    text-align: center;
  }
  
  .top-footer h3 {
    font-weight: bold;
    font-size: 22px;
  }
  
  .top-footer .section {
    margin-bottom: 40px;
  }
  
  .top-footer .section h3 {
    margin-bottom: 13px;
  }
  
  .top-footer .section p {
    margin-bottom: 6px;
  }
  
  .top-footer .section ul.menu {
    padding-left: 0;
  }
  
  .top-footer .section ul.menu li i {
    display: none;
  }
  
  /*
  .top-footer .section ul.sub-menu {
    padding-left: 10px;
  }
  */
  .top-footer .section ul.sub-menu li i {
    display: inline-block;
    font-size: 13px;
    padding-right: 7px;
  }
  
  #newsletter .form-control {
    border-radius: 0;
    font-size: 14px;
  }
  
  #newsletter .btn {
    border-radius: 0;
    color: #fff;
  }
  
  hr.footer-line {
    width: 100%;
    margin-top: 80px;
    border-top: solid thin #ccc;
  }
  
  @media all and (min-width: 400px) {
    .top-footer {
      padding: 30px 15px 0;
    }
  }
  
  @media all and (min-width: 768px) {
    .top-footer {
      text-align: left;
    }
  
    .top-footer .logo {
      text-align: left;
    }
  
    .top-footer .section ul.sub-menu {
      padding-left: 10px;
    }
  }
  
  @media all and (min-width: 769px) {
    .top-footer .section h3 {
      margin-top: 20px;
    }
  
    #footer-features {
      margin-top: 100px;
    }
  
    .top-footer {
      padding: 30px 0 0;
    }
  
    hr.footer-line {
      width: 100%;
      margin-top: 30px;
    }
  }
  
  /* Home Carousel */
  .carousel-info-inner,
  .layer {
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }
  
  .carousel-info-inner a {
    display: block;
    margin: 0 auto;
    opacity: 1;
  }
  
  .owl-carousel .owl-dots {
    text-align: center;
  }
  
  .owl-carousel button.owl-dot {
    width: 30px;
    height: 3px;
    background: #333 !important;
    display: inline-block;
    color: red;
    z-index: 100;
    margin-right: 5px;
  }
  
  .owl-carousel .owl-nav {
    position: absolute;
    top: 45%;
    left: -17px;
    right: -17px;
    z-index: -10;
  }
  
  .owl-carousel button.owl-next {
    float: right;
  }
  
  #principal-slider.owl-carousel .owl-dots {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
  }
  
  #principal-slider {
    margin-bottom: 7px;
  }
  /* Vistas al slider Principal Modificado por luis */
  /*Para pantallas mas grandes de 600px */
  
  @media only screen and (min-width: 600px)  {
  #principal-slider .btn-primary,
  #secondary-slider .btn-primary {
    background: none !important;
    border: none;
    color: #ffffff !important;
    /*text-decoration: underline;*/
    text-transform: uppercase;
    border-bottom: 1px solid #fff; 
    float: left; 
    display: block; 
    width: auto; 
    margin-left: 10px;
    font-weight: 700 !important;
    font-size: 1.2rem !important;
    font-family: Lato !important;
    line-height: 12px !important;
    
     
   }
    /* Tamaño de nombre Productos en pantallas notebook */
  h3 {
      font-size: 1.2em !important;
    
  }  
  }
  /* fin pantallas 600 px */
  /*Inicio Pantallas pequeñas */
  
  @media only screen and (max-width: 600px)  {
  
  #principal-slider .btn-primary,
  #secondary-slider .btn-primary {
    background: none !important;
    border: none;
    color: #fff !important;
    /*text-decoration: underline;*/
    text-transform: uppercase;
    text-decoration: underline;
    font-weight: 700 !important;
    font-size: 1.2rem !important;
    font-family: Lato !important;
    line-height: 25px !important;
  }
    /* Tamaño de nombre Productos en celulares  */
    h3 {
      font-size: 0.9em !important;
  }
  }
  /* Fin pantallas Pequeñas */
  /* Fin Vistas slider modificado por luis */ 
    /* Cambio de color link H3 home productos */
    
  /* Home */
  .brand-section {
    margin-top: 40px;
  }
  
  #features-products-slider {
    padding: 0 15px;
    margin-bottom: 30px;
  }
  
  .products-home {
    margin-top: 30px;
  }
  
  @media all and (min-width: 769px) {
    .brand-section {
      margin-top: 80px;
    }
  
    .products-home {
      margin-top: 50px;
    }
  
    #features-products-slider {
      padding: 0 0;
    }
  
    .owl-carousel .owl-nav {
      left: -22px;
      right: -22px;
    }
  }
  
  @media all and (min-width: 991px) {
    .owl-carousel .owl-nav i {
      font-size: 20px;
    }
  }
  
  /* Banner Home */
  #banner-section {
    margin-top: 40px;
  }
  
  .banner-home {
    margin-bottom: 25px;
  }
  
  .banner-info {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
  }
  
  .banner-info h4 {
    font-weight: bold;
    font-size: 22px;
  }
  
  /* Category */
  .product-block {
    margin-bottom: 20px;
  }
  
  .product-block h3 {
    font-weight: bold;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: normal;
    
  }
  
  .product-block h6 {
    font-size: 15px;
  }
  
  .category-header h6 {
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .category-header img {
    margin-bottom: 20px;
  }
  
  .category-page .well {
    margin-top: 20px;
  }
  
  .product-image-block {
    position: relative;
  }
  
  span.flag {
    position: absolute;
    right: 0;
    padding: 0 10px;
    color: #fff;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: bold;
  }
  
  span.flag::before {
    clip-path:
      polygon(
        0% 0%,
        100% 0%,
        100% 100%,
        100% 100%,
        100% 100%,
        100% 100%,
        45% 100%
      );
    display: inline-block;
    content: '';
    width: 12px;
    height: 18px;
    position: absolute;
    left: -6px;
  }
  
  span.flag.sale {
    top: 15px;
  }
  
  span.flag.out-stock {
    bottom: 30px;
  }
  
  .category-actions {
    border-top: solid thin #ccc;
    border-bottom: solid thin #ccc;
    margin: 0 0 20px;
  }
  
  .category-actions .form-group {
    margin-bottom: 0;
  }
  
  .category-actions .form-control {
    border-color: #fff;
  }
  
  #categories-menu {
    background: transparent;
    border: 0;
    text-transform: uppercase;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  #categories-menu i {
    padding-right: 10px;
  }
  
  ul#categories {
    position: absolute;
    z-index: 100;
    background: #fff;
    padding: 20px;
    box-shadow: 2px 7px 6px 0 rgba(0, 0, 0, 0.2);
  }
  
  #categories li {
    margin-bottom: 8px;
  }
  
  #categories ul {
    padding-left: 15px;
  }
  
  #categories a {
    padding-right: 20px;
  }
  
  #categories .panel-heading.dropdown::after,
  #categories .panel-heading-1.dropdown::after {
    font-family: 'Linear Icon';
    font-style: normal;
    font-weight: 400;
    speak: none;
    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    line-height: 1em;
    -webkit-font-smoothing: antialiased;
    float: right;
  }
  
  #categories .panel-heading.dropdown.collapsed::after,
  #categories .panel-heading-1.dropdown.collapsed::after {
    content: '\eb36'; /* Plus */
  }
  
  #categories .panel-heading.dropdown::after,
  #categories .panel-heading-1.dropdown::after {
    content: '\eb37'; /* Minus */
  }
  
  @media all and (min-width: 769px) {
    span.flag {
      font-size: 13px;
      padding: 5px 15px;
    }
  
    span.flag::before {
      height: 29px;
      top: 0;
    }
  
    span.flag.sale {
      top: 30px;
    }
  
    span.flag.out-stock {
      bottom: 45px;
    }
  
    .product-block h3 {
      font-size: 18px;
      line-height: 25px;
    }
  }
  
  /* Pagination */
  ul.pagination {
    border-top: solid thin #ccc;
    border-bottom: solid thin #ccc;
    border-radius: 0;
  }
  
  .page-item.active .page-link {
    z-index: 1;
    color: #000;
    font-weight: bold;
    background-color: transparent;
    border-color: transparent;
    text-decoration: undeline;
  }
  
  .page-item.active .page-link::after {
    display: block;
    content: '';
    height: 3px;
    background: red;
  }
  
  ul.pagination .page-link {
    border: 0;
    background: transparent;
  }
  
  ul.pagination .page-link:hover {
    background-color: transparent;
  }
  
  /* Blog */
  #home-blog {
    margin-top: 40px;
  }
  
  .blog-post {
    text-align: center;
    margin-bottom: 40px;
  }
  
  .blog-post:last-child {
    margin-bottom: 0;
  }
  
  .blog-post h3 {
    font-weight: bold;
    font-size: 22px;
    margin-bottom: 15px;
  }
  
  .blog-post img,
  .post img {
    margin-bottom: 25px;
  }
  
  @media all and (min-width: 769px) {
    #home-blog {
      margin-top: 80px;
      margin-bottom: 40px;
    }
  }
  
  /* Sidebar */
  .card {
    border: solid thin;
    margin: 30px 0;
    border-radius: 0;
    background: transparent;
  }
  
  .card .card-header {
    background: transparent;
  }
  
  .card h4 {
    font-weight: bold;
    font-size: 22px !important;
    margin-bottom: 0;
  }
  
  .card ul li {
    margin-bottom: 13px;
  }
  
  .card .list-group-item {
    background: transparent;
    border: none;
    margin-bottom: 0;
  }
  
  .card .list-group-item:last-child {
    margin-bottom: 15px;
  }
  
  @media all and (min-width: 769px) {
    .sidebar .card {
      margin: 0 0 30px;
    }
  }
  
  /* Contact */
  .contact input[type='submit'].btn-block {
    width: auto;
    padding-left: 30px;
    padding-right: 30px;
  }
  
  .form-control {
    border-radius: 0;
  }
  
  #contact-list i {
    padding-right: 10px;
  }
  
  .map {
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: grayscale(100%);
  }
  
  @media all and (min-width: 769px) {
    .contact {
      margin-top: 20px;
    }
  
    .contact .sidebar .card {
      margin-top: 30px;
    }
  }
  
  /* Product Page */
  hr.divider {
    border-top: 2px solid;
    margin-top: 30px;
    margin-bottom: 30px;
  }
  
  .product-page .page-header {
    margin: 0;
  }
  
  #product-sharing {
    margin-top: 40px;
  }
  
  #product-sharing ul {
    display: inline-block;
    padding-left: 10px;
  }
  
  .product-form-price {
    font-size: 20px;
    font-weight: 600;
  }
  
  .form-group.price_elem {
    margin-bottom: 30px;
  }
  
  .product-brand {
    margin-bottom: 0;
    font-size: 17px !important;
  }
  
  .product-info label {
    text-transform: uppercase;
    font-weight: bold;
    display: inline-block;
    padding-right: 10px;
  }
  
  .product-info select {
    display: inline-block;
    width: 100%;
    min-width: 100px;
  }
  
  .product-info select option {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .product-info .btn {
    border-radius: 0;
    padding-left: 20px;
    padding-right: 20px;
  }
  
  #stock .form-control-label,
  #stock .col-sm-8.col-md-9 {
    width: auto;
    display: inline-block;
    padding-left: 0;
  }
  
  #related-carousel {
    margin-top: 20px;
    padding: 0 20px;
  }
  
  .product-page-thumbs {
    order: 2;
    display: flex;
    overflow-y: hidden;
    overflow-x: scroll;
    margin: 0 15px;
    padding: 0;
  }
  
  .thumbs {
    display: block;
    flex-basis: 25%;
    min-width: 25%;
    margin-right: 10px;
  }
  
  .thumbs img {
    margin-bottom: 5px;
  }
  
  .product-page-thumbs::-webkit-scrollbar,
  .cart-list::-webkit-scrollbar {
    width: auto;
    height: 7px;
    background: #fff;
  }
  
  .product-page-thumbs::-webkit-scrollbar-track,
  .cart-list::-webkit-scrollbar-track {
    background: #fff;
  }
  
  /* Handle */
  .product-page-thumbs::-webkit-scrollbar-thumb,
  .cart-list::-webkit-scrollbar-thumb {
    background: #888;
  }
  
  /* Handle on hover */
  .product-page-thumbs::-webkit-scrollbar-thumb:hover,
  .cart-list::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
  
  @media all and (min-width: 768px) {
    .product-page-thumbs {
      order: 1;
      max-height: 650px;
      overflow-y: scroll;
      overflow-x: hidden;
      flex-direction: column;
      margin: 0;
      padding: 0 15px;
    }
  
    .product-page-thumbs::-webkit-scrollbar,
    .cart-list::-webkit-scrollbar {
      width: 7px;
      height: auto;
      background: #fff;
    }
  
    .thumbs {
      margin-right: 0;
    }
  
    .product-page-thumbs img {
      margin-bottom: 10px;
    }
  
    .main-product-image {
      order: 2;
    }
  
    .thumbs img {
      max-width: 100%;
    }
  
    .main-product-image {
      padding-left: 0;
    }
  }
  
  @media all and (min-width: 769px) {
    .product-page {
      margin-top: 30px;
    }
  }
  
  @media all and (min-width: 991px) {
    .product-page-thumbs {
      order: 1;
      max-height: 450px;
    }
  }
  
  @media all and (min-width: 1200px) {
    .product-page-thumbs {
      order: 1;
      max-height: 515px;
    }
  }
  
  /* Checkout */
  .checkout-content {
    flex: 1 0 auto;
  }
  
  .breadcrumb-cart {
    justify-content: space-around;
    margin-top: 10px;
    margin-bottom: 15px;
  }
  
  .breadcrumb-cart .item {
    text-align: center;
    flex: 1;
    line-height: 15px;
  }
  
  .breadcrumb-cart .item.active a {
    font-weight: bold;
  }
  
  .breadcrumb-cart span {
    display: block;
    width: 50px;
    height: 50px;
    border: solid thin;
    margin: 10px auto;
    font-size: 33px;
    line-height: 36px;
    background: #fff;
  }
  
  .breadcrumb-cart::before {
    content: '';
    display: block;
    border-top: solid thin 2px;
    height: 2px;
    width: 73%;
    position: absolute;
    top: 36px;
    z-index: -1;
  }
  
  .breadcrumb-cart a {
    text-transform: uppercase;
    font-size: 12px;
  }
  
  .checkout-footer {
    margin-top: 35px;
    flex-shrink: 0;
    padding-top: 10px;
  }
  
  .cart-table .table td {
    vertical-align: middle;
  }
  
  .cart-table .table th {
    text-transform: uppercase;
  }
  
  .cart-table .table tbody {
    border-bottom: solid thin;
  }
  
  .cart-table .table td span.remove {
    display: block;
    width: 100%;
    text-align: center;
  }
  
  .cart-table .table p {
    margin-bottom: 0;
  }
  
  .cart-table .table strong {
    text-transform: uppercase;
  }
  
  td.left-border {
    border-left: solid thin #dee2e6;
  }
  
  td.both-border {
    border-left: solid thin #dee2e6;
    border-right: solid thin #dee2e6;
  }
  
  .table.summary td {
    border-top: none;
  }
  
  .card.secondary-card,
  .card.secondary-card .card-header {
    border: none;
  }
  
  .card.secondary-card .btn {
    width: auto;
    color: #fff;
  }
  
  .checkout-container h2 {
    font-size: 24px !important;
    border-bottom: solid thin;
    padding: 0 0 15px;
    margin: 0 15px;
    font-weight: bold;
  }
  
  .checkout-container .review-order-info h2 {
    margin-left: 0;
    margin-bottom: 15px;
  }
  
  .checkout-container .review-order-info .info {
    margin-bottom: 30px;
  }
  
  .checkout-container .col-lg-8,
  .checkout-container .col-lg-4 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 25px;
  }
  
  #payments,
  #shipping {
    border: none;
  }
  
  #payments .card-header,
  #shipping .card-header {
    border-bottom: none;
    padding: 0;
    font-weight: bold;
  }
  
  #payments .card-header h2 {
    margin-left: 0;
    margin-right: 30px;
  }
  
  .checkout-content .card {
    margin-top: 0;
  }
  
  .card-header h2 {
    font-weight: bold;
  }
  
  .card.summary .table tbody {
    border-bottom: none;
  }
  
  .card.summary .table tbody tr:first-child td {
    border-top: none;
  }
  
  .checkout-success .checkout-container h2 {
    margin: 20px 0 15px;
  }
  
  .checkout-success h1.page-header {
    margin-bottom: 5px;
  }
  
  .checkout-success h5.subtitle {
    font-weight: 300;
  }
  
  .checkout-success h5.subtitle strong {
    font-weight: 800;
  }
  
  .checkout-success #cart-update-form tr:first-child td {
    border-top: none;
  }
  
  #estimate_shipping_form {
    margin-bottom: 25px;
  }
  
  .btn.btn-outline-secondary.btn-block {
    border-radius: 0;
  }
  
  form#review_form {
    margin-bottom: 20px;
  }
  
  .payment_information {
    font-style: italic;
    font-size: 14px;
    margin-top: 6px;
  }
  
  .cart-actions a.btn-link,
  .cart-actions .list-inline a {
    font-size: 0.875rem;
  }
  
  @media all and (min-width: 768px) {
    .desktop-hide {
      display: none;
    }
  
    #payments,
    #shipping {
      width: 50%;
      display: inline-block;
      vertical-align: top;
    }
  }
  
  @media all and (min-width: 769px) {
    .breadcrumb-cart {
      margin-bottom: 30px;
    }
  
    .checkout-footer {
      margin-top: 60px;
    }
  
    h2.legend.col-md-12.col-sm-12.col-xs-12 {
      max-width: 96%;
    }
  
    #order-summary.fixed {
      position: -webkit-sticky;
      position: sticky;
      top: 0;
    }
  
    .checkout-container .review-order-info .info {
      margin-bottom: 50px;
    }
  
    .checkout-success h5.subtitle {
      margin-bottom: 50px;
    }
  }
  
  /* Login */
  #login .actions {
    display: flex;
    justify-content: space-between;
  }
  
  #login .actions p {
    margin-top: 15px;
    margin-bottom: 0;
  }
  
  #customer_details h4.title,
  #customer_address h2,
  #customer-summary h2 {
    font-size: 24px !important;
    border-bottom: solid thin;
    font-weight: bold;
    padding: 0 0 15px;
    margin: 0 0 20px;
  }
  
  input#register_customer,
  input#edit_customer_address {
    padding-left: 50px;
    padding-right: 50px;
    margin-top: 15px;
    width: 100%;
  }
  
  #customer-summary {
    margin-top: 20px;
  }
  
  #customer-order-table .table thead th {
    border-top: none;
  }
  
  .orders_header .collapsed .show {
    display: block;
  }
  
  .orders_header .collapsed .hide {
    display: none;
  }
  
  .orders_header .show {
    display: none;
  }
  
  #customer-summary .orders_header .reorder-btn {
    display: block;
    position: absolute;
    right: 0;
    top: 0;
  }
  
  #customer-summary .orders_header {
    padding: 0;
    margin: 15px 0 10px 0;
    position: relative;
  }
  
  #customer-summary .orders_body {
    padding: 0 0 20px 0;
    margin: 0;
    position: relative;
  }
  
  #customer-summary .orders {
    margin-bottom: 15px;
    border-bottom: 1px solid #ddd;
  }
  
  #customer-summary .orders:last-child {
    margin: 0;
    border: 0;
  }
  
  .card .orders_item h3 {
    font-size: 20px !important;
  }
  
  .card .orders_item h4 {
    font-weight: bold;
    font-size: 18px !important;
    margin-bottom: 20px;
  }
  
  .card .orders_item h5 {
    font-weight: normal;
    font-size: 16px !important;
    margin: 0;
  }
  
  #customer-summary .orders_body .ordered_product {
    border-top: 1px solid #ddd;
    margin-top: 10px;
    padding-top: 10px;
  }
  
  #customer-summary .order_amounts {
    padding: 15px 0;
    border-top: 3px solid #ddd;
    margin-top: 15px;
    position: relative;
  }
  
  @media all and (min-width: 769px) {
    input#register_customer,
    input#edit_customer_address {
      width: auto;
    }
  
    #customer-summary {
      margin-top: 30px;
    }
  }
  
  /* Carousel */
  .carousel-control-next,
  .carousel-control-prev {
    z-index: 2;
  }
  
  .carousel-control-next-icon,
  .carousel-control-prev-icon {
    width: 14px;
    height: 30px;
  }
  
  .carousel-control-next {
    right: 15px;
  }
  
  span.dark-bg {
    background: black;
    width: 30px;
    height: 30px;
    border-radius: 50%;
  }
  
  /* Instagram Feed */
  .ig_account {
    font-weight: 300;
  }
  
  #instagram-section {
    margin-top: 40px;
  }
  
  #instagram a > div {
    display: inline-block;
    width: 33.3%;
    height: 0;
    padding-bottom: 33.3%;
    background: #eee 50% 50% no-repeat;
    background-size: cover;
    margin-bottom: -7px;
  }
  
  @media all and (min-width: 769px) {
    #instagram a > div {
      display: inline-block;
      width: 16.666%;
      padding-bottom: 16.666%;
      margin-bottom: -7px;
    }
  
    #instagram-section {
      margin-top: 40px;
    }
  }
  
  /* Text description + Post */
  .text-formatted blockquote {
    display: block;
    margin: 1.5em 10px;
    padding: 0.5em 15px;
    border-left: 1px solid #bbb;
    font-style: italic;
  }
  
  .text-formatted h3,
  .text-formatted h4,
  .text-formatted h5,
  .text-formatted h6 {
    font-weight: bold;
  }
  
  .text-formatted ul {
    list-style: disc;
  }
  
  .text-formatted ul li,
  .text-formatted ol li {
    padding-bottom: 8px;
  }
  
  .text-formatted ol {
    list-style: decimal;
  }
  
  .text-formatted ol ol {
    list-style: lower-alpha;
  }
  
  .text-formatted ol ol ol {
    list-style: lower-roman;
  }
  
  .text-formatted table {
    width: 100%;
    border: 1px solid #dee2e6;
  }
  
  .text-formatted table td,
  .text-formatted table th {
    padding: 0.75rem;
    vertical-align: top;
    border: 1px solid #dee2e6;
  }
  
  .zoom-img:hover {
    cursor: -webkit-zoom-in;
    cursor: zoom-in;
  }
  
  .zoom-img.zoom-active:hover {
    cursor: -webkit-zoom-out;
    cursor: zoom-out;
  }
  
  .form-group select,
  select.select-qty.form-control {
    -moz-appearance: none;
    -webkit-appearance: none;
    background-position: 98% 50%;
    background-repeat: no-repeat;
    background-image: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%20%3C%21DOCTYPE%20svg%20PUBLIC%20%22-//W3C//DTD%20SVG%201.1//EN%22%20%22http%3A//www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd%22%3E%20%3Csvg%20version%3D%221.1%22%20id%3D%22Layer_1%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20xmlns%3Axlink%3D%22http%3A//www.w3.org/1999/xlink%22%20x%3D%220px%22%20y%3D%220px%22%20width%3D%2214px%22%20height%3D%2212px%22%20viewBox%3D%220%200%2014%2012%22%20enable-background%3D%22new%200%200%2014%2012%22%20xml%3Aspace%3D%22preserve%22%3E%20%3Cpolygon%20points%3D%223.862%2C7.931%200%2C4.069%207.725%2C4.069%20%22/%3E%3C/svg%3E);
  }
  
  .btn-link.btn-filter:hover {
    text-decoration: none;
  }
  
  #checkout input#submit_review_order {
    display: none;
  }
  
  /* ==== HEADER DESKTOP MEJORADO ==== */
  @media (min-width: 992px) {
    .main-header {
      margin-top: 10px;
      margin-bottom: 10px;
    }
    .logo {
      text-align: left;
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      display: flex;
      align-items: center;
      min-height: 60px;
    }
    .logo .navbar-brand {
      margin-right: 0;
      padding: 0;
      display: flex;
      align-items: center;
    }
    .main-header nav.navbar {
      padding: 0;
    }
    .main-header .navbar-nav {
      width: 100%;
      justify-content: center;
      align-items: center;
      gap: 10px;
    }
    .header-icons {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 18px;
      min-height: 60px;
    }
    .header-icons .btn-link {
      padding: 0 6px;
      font-size: 22px;
    }
    .header-icons .nav-link {
      padding: 0 6px;
      font-size: 15px;
      color: #2C3E50;
      font-weight: 500;
      background: none;
      border: none;
    }
    .header-icons .nav-link:hover {
      color: #7623e2;
      text-decoration: underline;
    }
    .cart-count {
      background: #7623e2;
      color: #fff;
      border-radius: 50%;
      font-size: 12px;
      width: 18px;
      height: 18px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: -6px;
      right: -10px;
      font-weight: bold;
    }
  }
  /* ==== FIN HEADER DESKTOP MEJORADO ==== */
  
  /* ==== TOP BAR MENSAJES DINÁMICOS ==== */
  .top-bar-mensajes {
    width: 100%;
    text-align: center;
    color: #fff; /* Puedes reemplazar por {{ options.top_message_font_color }} si usas CSS.liquid */
    background: #222; /* Puedes reemplazar por {{ options.top_message_background_color }} si usas CSS.liquid */
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-size: 15px;
    font-weight: 500;
    border-bottom: 1px solid #181818;
    letter-spacing: 0.02em;
  }
  .mensaje-slider {
    width: 100%;
    position: relative;
  }
  .mensaje-item {
    display: none;
    width: 100%;
    transition: opacity 0.5s;
    justify-content: center;
    align-items: center;
    min-height: 32px;
  }
  .mensaje-item[style*="display:block"] {
    display: flex;
  }
  .top-bar-mensajes i {
    margin-right: 8px;
    font-size: 16px;
    vertical-align: middle;
  }
  /* ==== FIN TOP BAR MENSAJES DINÁMICOS ==== */
  
  /* Iconos del header móvil */
  @media (max-width: 991.98px) {
    .main-header .d-block.d-lg-none .d-flex.align-items-center .btn-link {
      font-size: 1.6rem; /* Ligeramente más grande para mejor toque */
      padding: 0 10px; /* Espacio horizontal */
      color: #2c3e50; /* Color consistente con el tema */
    }
    .main-header .d-block.d-lg-none .d-flex.align-items-center .btn-link i {
      line-height: 1; /* Para mejor alineación vertical del icono */
    }
    .main-header .d-block.d-lg-none .d-flex.align-items-center .cart-count {
      top: -6px;
      right: -2px;
      font-size: 10px;
      width: 15px;
      height: 15px;
      line-height: 15px; /* Centrar el número */
    }
  }
  
  /* Mejoras para el buscador móvil */
  @media (max-width: 991.98px) {
    #search_mini_form_mobile.open { /* Añadiremos esta clase con JS */
      display: block !important;
      position: absolute;
      top: 100%; /* Justo debajo de la barra de header original */
      left: 0;
      right: 0;
      background-color: #fff;
      padding: 15px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      z-index: 1000; /* Asegurar que esté por encima de otros elementos del header */
      border-top: 1px solid #eee;
    }
  }
  