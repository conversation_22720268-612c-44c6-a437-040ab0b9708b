<div class="container">
  <!-- Page Heading -->
  <div class="row">
    <div class="col-12 text-center">
      <h1 class="page-header">{{page.title}}</h1>
      {{page.body}}
    </div>
  </div>
  <!-- /.row -->

  <!-- Blog Post -->
  {% unless pages.categories.category["Post"] == blank %}
  {% paginate pages.categories.category["Post"].pages by options.page_post_per_page %}
  <div class="row contact">
    {% for page in paged.pages reversed %}
    <div class="col-12 col-md-4 blog-post">
      {% if page.images == empty %}
      <a href="{{ page.url }}" title="{{ page.title }}">
        <img class="img-fluid" src="//placehold.it/800x500" alt="{{ page.title }}">
      </a>
      {% else %}
      <a href="{{ page.url }}" title="{{ page.title }}"><img class="img-fluid" src="{{ page.images.first | resize: '800x500' }}" alt="{{ page.title }}" /></a>
      {% endif %}
      <h3>
        <a href="{{ page.url }}" title="{{ page.title }}">{{ page.title }}</a>
      </h3>
      <p>{{page.body | truncate:400 | strip_html }}</p>
      <a class="btn btn-link" href="{{ page.url }}">{% t 'Read More' %} <i class="fas fa-chevron-right"></i></a>
    </div>
    {% endfor %}
    <!-- End Blog Post -->
  </div>
  <div class="row">
    <div class="col-sm-12 col-md-12">
      {{pager}}
    </div>
  </div>

  {% endpaginate %}
  {% endunless %}
</div>
