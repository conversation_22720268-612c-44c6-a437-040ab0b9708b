/* =============================================================
Here you will find the "Color Pickers" CSS attributes. You can change them by going to your Admin Panel at Themes > Options.
If you want to add more pickers to your store's set, or wanna know more about what you can on the code visit the following link https://jumpseller.com/support/creating-a-custom-html-theme#special-variables

© Copyright New Premium Theme. Powered by Jumpseller
============================================================= */

/* =============================================================
COLOR PICKERS
============================================================= */

/* Store Background | Default: #FFF */
body, ul#categories, .dropdown-menu.multi-level, .lateral-box {
    background-color: {{ options.store_background }} ;
    color: {{ options.primary_color }};
  }
  
  /* Navbar Background | Default: #222 */
  .navbar-inverse {
    background-color: {{ options.primary_color }} ;
    border-color: {{ options.primary_color }};
  }
  h1, h2, h3, h4, h5, h6, p,
  .main-header .btn-link,
  .nav-item a,
  #principal-slider .btn-primary, #secondary-slider .btn-primary,
  .product-block h3 a,
  .banner-info .btn.btn-link:hover,
  .blog-post h3 a,
  .blog-post .btn.btn-link:hover,
  .top-footer a,
  .logo .navbar-brand,
  ul.pagination .page-link,
  .category-submenu a,
  .social-networks li i,
  .btn-link:hover,
  .breadcrumb-cart a,
  .checkout-content .table a.cart-product-remove,
  .cart-list h3.product-name a,
  .navbar-light .navbar-nav .nav-link,
  #categories-menu,
  .page-item.active .page-link,
  .ig_account a,
  .btn-link.btn-filter
  {
    color: {{ options.primary_color }};
  }
  a,
  .product-block h3 a:hover,
  .banner-info .btn.btn-link,
  .blog-post .btn.btn-link,
  ul.pagination .page-link:hover,
  #categories-menu:hover,
  .category-submenu a:hover,
  .btn-link,
  .social-networks li a:hover i,
  .checkout-content .table a.cart-product-remove:hover,
  .cart-list h3.product-name a:hover,
  .cart-wrapper .subtitle,
  .ig_account a:hover,
  .btn-link.btn-filter:hover
  {
    color: {{ options.accent_color }};
  }
  
  #principal-slider .btn-primary:hover, #secondary-slider .btn-primary:hover,
  span.flag.sale, span.flag.sale:before  {
    background-color: {{ options.accent_color }} !important ;
  }
  .toast, .toast-success {background-color: {{ options.cart_notification_color }};}
  
  .cart-table thead tr, .btn.btn-adc{
    border: solid 1px {{ options.accent_color }};
  }
  #principal-slider .layer{
    background-color: {{options.slider_background_color}};
    opacity: {{options.slider_background_opacity}};
  }
  #secondary-slider .layer{
    background-color: {{options.secondary_slider_background_color}};
    opacity: {{options.secondary_slider_background_opacity}};
  }
  #footer-features .layer{
    background-color: {{options.footer_features_background_color}};
    opacity: {{options.footer_features_background_opacity}};
  }
  #footer-features, #footer-features .item h4, #footer-features .item p {
    color: {{options.footer_features_color}};
  }
  .top-message, .top-message .nav-link, .top-message p, .top-message i{
    background-color: {{ options.top_message_background_color }};
    color: {{ options.top_message_font_color }}
  }
  .lateral-header-btn{
    border-bottom-color: {{ options.primary_color }};
  }
  .owl-carousel button.owl-dot.active, .breadcrumb-cart::before, .accent-bg {
    background-color: {{ options.accent_color }} !important ;
  }
  .btn-primary, .btn-primary.accent-bg:hover, #newsletter .btn.primary, .site-footer i.submit-newsletter-icon, .card.secondary-card .btn, button#cart-btn span{
    background-color: {{ options.primary_color }} ;
    border-color: {{ options.primary_color }};
    color: {{ options.store_background }}
  }
  #side-filters .badge {
    background-color: {{ options.primary_color }} ;
    border-color: {{ options.primary_color }};
  }
  .card, .card-header, .social-networks li:hover,
  hr.divider, .breadcrumb-cart span, .checkout-content .table tbody, .checkout-container h2,
  #customer_details h4.title, #customer_address h2, #customer-summary h2{
    border-color: {{ options.accent_color }};
  }
  #side-filters .card {
    border: solid 1px {{ options.accent_color }};
  }
  
  span.flag.out-stock, span.flag.out-stock:before,
  .page-item.active .page-link:after{
    background-color: {{ options.primary_color }};
  }
  
  .btn-primary:hover, .breadcrumb-cart .item.active span, .breadcrumb-cart .item.complete span,
  .btn-primary.accent-bg, .btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show>.btn-primary.dropdown-toggle{
    background-color: {{ options.accent_color }} ;
    border-color: {{ options.accent_color }};
    color: #fff;
  }
  .card.summary,.card.summary .card-header {
    border-color: {{ options.primary_color }};
  }
  
  .site-footer{
    background-color: {{ options.footer_background }};
    color: {{ options.footer_color }};
  }
  .site-footer a, .site-footer p, .site-footer h1, .site-footer h3, .site-footer i, .site-footer .social-networks li i{
    color: {{ options.footer_color }} ;
  }
  
  .toast.toast-success, .toast, .toast-success {
    background-color: {{ options.cart_notification_color }};
  }
  