<div class="container product-page">
  <!-- /.row -->
  <div class="row">
    <div class="col-lg-6 mb-4">
      {% if product.images == empty %}
      <!-- There's no image available -->
      <div class="text-center">
        <div class="product-page-no-image">
          <img class="img-hover" src="{{ 'no-image-home.jpg' | asset }}" alt="{{product.name | escape}}">
          <br>{% t "There's no product image available" %}
        </div>
      </div>
      {% elsif product.images.size == 1 %}
      <!-- There's only One image -->
      <div class="">
        <div class="main-product-image">
          {% if options.zoom_product_image %}
          <div class="zoom-img">
            <img src="{{product.images.first | resize:'756x945'}}" srcset="{{product.images.first | resize:'756x945'}} 1x, {{product.images.first | resize:'1080x1350'}} 2x" alt="{{product.name | escape}}" class="img-fluid">
          </div>
          {% else %}
          <img src="{{product.images.first | resize:'540x675'}}" srcset="{{product.images.first | resize:'540x675'}} 1x, {{product.images.first | resize:'1080x1350'}} 2x" alt="{{product.name | escape}}" class="img-fluid">
          {% endif %}
        </div>
      </div>
      {% elsif product.images.size > 1 %}
      <div class="row">
        <!-- Thumb Images -->
        <div class="col-md-3 product-page-thumbs space mt-3">
          {% for image in product.images %}
          <a class="thumbs" data-image="{{forloop.index}}" href="#"><img src="{{image | thumb:'120x150' }}" alt="{{product.name | escape}}" /></a>
          {% endfor %}
        </div>
        <div class="col-md-9 main-product-image space">
          <div id="product-carousel" class="carousel slide">
            <div class="carousel-inner" role="listbox">
              <div class="carousel-item active">
                {% if options.zoom_product_image %}
                <div class="zoom-img">
                  <img id="first-image" src="{{product.images.first | resize:'756x945'}}" srcset="{{product.images.first | resize:'756x945'}} 1x, {{product.images.first | resize:'1080x1350'}} 2x" alt="{{product.name | escape}}" class="img-fluid">
                </div>
                {% else %}
                <img id="first-image" src="{{product.images.first | resize:'540x675'}}" srcset="{{product.images.first | resize:'540x675'}} 1x, {{product.images.first | resize:'1080x1350'}} 2x" alt="{{product.name | escape}}" class="img-fluid">
                {% endif%}
              </div>
              {% for image in product.images offset:1 %}
              <div class="carousel-item">
                {% if options.zoom_product_image %}
                <div class="zoom-img">
                  <img src="{{image | resize:'756x945'}}" srcset="{{image | resize:'756x945'}} 1x, {{image | resize:'1080x1350'}} 2x" alt="{{product.name | escape}}" class="img-fluid">
                </div>
                {% else %}
                <img src="{{image | resize:'540x675'}}" srcset="{{image | resize:'540x675'}} 1x, {{image | resize:'1080x1350'}} 2x" alt="{{product.name | escape}}" class="img-fluid">
                {% endif%}
              </div>
              {% endfor %}
            </div>
          </div>
          <a class="carousel-control-prev" href="#product-carousel" role="button" data-slide="prev">
            <span class="dark-bg"><span class="carousel-control-prev-icon" aria-hidden="true"></span></span>
            <span class="sr-only">Previous</span>
          </a>
          <a class="carousel-control-next" href="#product-carousel" role="button" data-slide="next">
            <span class="dark-bg"><span class="carousel-control-next-icon" aria-hidden="true"></span></span>
            <span class="sr-only">Next</span>
          </a>
        </div>
      </div>
      {% endif %}
    </div>
    <div class="col-lg-6 product-info">
     {% if product.brand != blank %}
      <p class="product-brand">{{product.brand}}</p>
      {% endif %}
      <h1 class="page-header">{{product.name}}</h1>
      <form class="form-horizontal" action="{{product.add_to_cart_url}}" method="post" enctype="multipart/form-data" name="buy">
        <!-- Product Price  -->
        {% if options.hide_price != true %}
        <div class="form-group price_elem">
          <span class="product-form-price" id="product-form-price">{{product.price | minus:product.discount | price}}</span>
          {% if product.discount > 0 %}
          <span class="product-form-discount" id="product-form-discount">( {{product.price | price}} )</span>
          {% endif %}
          {% if product.discount_begins and product.discount_expires %}
          <div class="mt-3">
            {% capture begins_date %}<strong>{{product.discount_begins | date: '%d-%m-%Y' }}</strong>{% endcapture %}
            {% capture expires_date %}<strong>{{product.discount_expires | date: "%d-%m-%Y" }}</strong>{% endcapture %}
            <p class="discount-date font-weight-lighter">{% t "Promotion valid from %{begins_date} to %{expires_date}" | begins_date: '{{begins_date}}' | expires_date: '{{expires_date}}' %}</p>
          </div>
          {% endif %}
          {% if product.status == 'not-available' %}
          <span class="not-available">
            / {% t "Not Available" %}
          </span>
          {% else %}
          <span class=" product-stock product-out-stock {% if product.stock == 0 and product.stock_unlimited == false and product.status != 'not-available' %}visible{% else %}hidden{% endif %}">
            / {% t "Out of stock" %}
          </span>
          {% endif %}
        </div>
        {% endif %}
        {% if product.sku != blank and options.product_sku_visibility %}
        <div id="product-sku" class="sku" style="visibility:visible;">
          <label>{% t 'SKU' %}: </label>
          <span class="sku_elem">{{product.sku}}</span>
        </div>
        {% endif %}

        {% if options.product_stock_visibility and product.stock_unlimited == false and product.stock > 0 %}
        <div id="stock">
          <label>{% t "Stock" %}:</label>
          <span class="product-form-stock">{{product.stock}}</span>
        </div>
        {% endif %}
        {% for option in product.options %}
        <div class="form-group qty-select">
          <label>{{option.name}}:</label>
          {% if option.type == 'option' %}
          <select id="{{option.id}}" name="{{option.id}}" class="form-control prod-options">
            {% for value in option.values %}
            {% assign variant_id = nil %}
            {% assign variant_stock = nil %}
            {% for variant in product.variants %}
            {% if variant.stock_unlimited %}
            {% assign variant_id = variant.id %}
            {% assign variant_stock = 999 %}
            {% else %}
            {% for variant_value in variant.values %}
              {% if variant_value.id == value.id %}
                {% unless variant_stock != 0 and variant_stock %}
                  {% assign variant_id = variant.id %}
                  {% assign variant_stock = variant.stock %}
                {% endunless %}
              {% endif %}
            {% endfor %}
            {% endif %}
            {% endfor %}
            <option data-variant-stock="{{variant_stock}}" data-variant-id="{{variant_id}}" value="{{value.id}}">{{value.name}}</option>
            {% endfor %}
          </select>
          {% elsif option.type == 'input' %}
          {% for value in option.values %}
          <input id="{{option.id}}" class="text form-control prod-options" type="text" name='{{option.id}}' placeholder='{{value.name}}' />
          {% endfor %}
          {% elsif option.type == 'text' %}
          {% for value in option.values %}
          <textarea id="{{option.id}}" class="textarea form-control prod-options" name='{{option.id}}' placeholder='{{value.name}}'></textarea>
          {% endfor %}
          {% elsif option.type == 'file' %}
          {% assign file_option = true %}
          <input id="{{option.id}}" class="filestyle product_option_value_file_upload prod-options" type="file" name="{{option.id}}" />
          {% endif %}
        </div>
        {% endfor %}

        {% if product.status == 'not-available' %}
        <div class="form-group mt-5 mb-4{% if product.status == 'not-available' %}visible{% else %}hidden{% endif %}">
          <p>{% t "This product is currently unavailable. You may send us an inquiry about it" %}.</p>
          <a href="{{contact.url}}" class="btn btn-dark btn-sm" title="{% t 'Contact Us' %}">{% t "Contact Us" %}</a>
          <a href="javascript:history.back()" class="btn btn-link btn-sm" title="&larr; {% t 'or Continue Shopping' %}">&larr; {% t "or Continue Shopping" %}</a>
        </div>
        {% else %}
        <!-- Out of Stock -->
        <div class="form-group product-stock product-out-stock mt-5 mb-4 {% if product.stock == 0 and product.stock_unlimited == false and product.status != 'not-available' %}visible{% else %}hidden{% endif %}">
          <p>{% t "This product has run out of stock. You may send us an inquiry about it" %}.</p>
          <a href="{{contact.url}}" class="btn btn-dark btn-sm" title="{% t 'Contact Us' %}">{% t "Contact Us" %}</a>
          <a href="javascript:history.back()" class="btn btn-link btn-sm" title="&larr; {% t 'or Continue Shopping' %}">&larr; {% t "or Continue Shopping" %}</a>
        </div>

        {% if options.disable_shopping_cart %}
        <div class="form-group product-stock product-out-stock mt-5 mb-4">
          <a href="{{contact.url}}" class="btn btn-dark btn-sm" title="{% t 'Contact Us' %}">{% t "Contact Us" %}</a>
          <a href="javascript:history.back()" class="btn btn-link btn-sm" title="&larr; {% t 'or Continue Shopping' %}">&larr; {% t "or Continue Shopping" %}</a>
        </div>
        {% else %}
        <div class="form-group product-stock product-available  mt-5 row {% if product.stock > 0 or product.stock_unlimited == true and product.status == 'available' %}visible{% else %}hidden{% endif %}">
          <div class="col-4">
            {% capture maxStock %} max="{{ product.stock }}" {% endcapture %}
            <input type="number" class="qty form-control" id="input-qty" name="qty" maxlength="5" min="1" value="1" {% if product.stock_unlimited !=true %}{{maxStock}}{% endif %}>
          </div>

          <div class="col-8 pl-0">
            {% if options.display_cart_notification and file_option != true %}
            <input type="button" onclick="addToCart2('{{product.id}}', '{{product.name | replace: "'", "" | escape }}', $('#input-qty').val(), getProductOptions(), '{{product.images.first}}', '{{product.price | minus:product.discount | price}}', '{{ product.url }}');"
              class="adc btn btn-primary btn-block" value="{% t 'Add to Shopping Cart' %}" style="background-color: rgb(34, 34, 34); color: white; border-radius: 8px; font-weight: 700;" />

            {% else %}
            <input type="submit" class="adc btn btn-primary btn-block" value="{% t 'Add to Shopping Cart' %}" />
            {% endif %}

            <a href="javascript:history.back()" class="btn btn-link btn-sm btn-block text-right" title="{% t 'Continue Shopping' %}">&larr; {% t 'Continue Shopping' %}</a>
          </div>
        </div>
        <div class="payment-info-container mt-4 col-12">
          <!-- Información de cuotas -->
          <div class="installments-info mb-3">
            <h5 class="payment-info-title">
              <i class="fas fa-credit-card mr-2"></i>Paga en cuotas
            </h5>
            <div class="installments-grid">
              {% assign price_without_discount = product.price | minus:product.discount %}
              {% assign price_3_installments = price_without_discount | divided_by: 3 %}
              {% assign price_6_installments = price_without_discount | divided_by: 6 %}
              {% assign price_12_installments = price_without_discount | divided_by: 12 %}
              
              <div class="installment-option">
                <span class="installment-number">3x</span>
                <span class="installment-price">{{price_3_installments | price}}</span>
                <span class="installment-text"></span>
               
              </div>
              <div class="installment-option">
                <span class="installment-number">6x</span>
                <span class="installment-price">{{price_6_installments | price}}</span>
                <span class="installment-text"></span>
               
              </div>
              <div class="installment-option">
                <span class="installment-number">12x</span>
                <span class="installment-price">{{price_12_installments | price}}</span>
                <span class="installment-text"></span>
                
              </div>
              <small class="installment-text"> **valores referenciales, consultar con su proveedor de tarjeta</small>
            </div>
          </div>

          <!-- Medios de pago -->
          <div class="payment-methods mb-3">
            <h5 class="payment-info-title">
              <i class="fas fa-lock mr-2"></i>Medios de pago seguros
            </h5>
            <div class="payment-methods-grid">
              <img src="{{ 'pay-webpay.png' | asset }}" alt="Webpay" class="payment-icon">
              <img src="{{ 'pay-visa.png' | asset }}" alt="Visa" class="payment-icon">
              <img src="{{ 'pay-master.png' | asset }}" alt="Master Card" class="payment-icon">
              <img src="{{ 'pay-mach.png' | asset }}" alt="Mach" class="payment-icon">
              <img src="{{ 'pay-mercadopago.png' | asset }}" alt="Mercado Pago" class="payment-icon">
            </div>
          </div>

          <!-- Información de envío -->
          <div class="shipping-info">
            <h5 class="payment-info-title">
              <i class="fas fa-truck mr-2"></i>Información de envío
            </h5>
            <div class="shipping-options">
              {% if product.stock > 0 or product.stock_unlimited == true %}
                <div class="shipping-option">
                  <i class="fas fa-check-circle text-success mr-2"></i>
                  <span>Despacho a domicilio</span>
                </div>
                <div class="shipping-option">
                  <i class="fas fa-store text-primary mr-2"></i>
                  <span>Retiro en tienda</span>
                </div>
              {% endif %}
            </div>
          </div>
        </div>

        {% endif %}
        {% endif %}
        <hr class="divider" />

        {% if product.description %}
        <div class="form-group">
          <label>{% t "Description" %}:</label>
          <div class="text-formatted">
            {{product.description}}
          </div>
        </div>
        {% endif %}

        {% if product.fields != empty %}
        <div class="form-group">
          <label class="form-control-label">{% t "Details" %}:</label>
          {% for field in product.fields %}
          <p><strong>{{field.label}}:</strong> {{field.value}}</p>
          {% endfor %}
        </div>
        {% endif %}

        {% unless product.attachments == empty %}
        <div class="form-group">
          <label class="form-control-label">{% t "Attachments" %}:</label>
          {% for attachment in product.attachments %}
          <p><a target="_blank" href="{{attachment.url}}">{{attachment.name}}</a></p>
          {% endfor %}
        </div>
        {% endunless %}

        <div id="product-sharing">
          <label>{% t 'Share' %}:</label>
          <ul class="list-inline social-networks">
            {% if options.facebook_button_on_products %}
            <li class="list-inline-item">
              <a href="https://www.facebook.com/sharer/sharer.php?u={{store.base_url}}{{product.url}}" class="has-tip tip-top radius button tiny button-facebook trsn" title="{% t 'Share on Facebook' %}" target="_blank" data-tooltip>
                <i class="fab fa-facebook-f"></i>
              </a>
            </li>
            {% endif %}

            {% if options.twitter_button_on_products %}
            <li class="list-inline-item">
              <a href="https://twitter.com/share?url={{store.base_url}}{{product.url}}&text={% t 'Check this product' %} {{product.name}}" class="has-tip tip-top radius button tiny button-twitter trsn" title="{% t 'Share on Twitter' %}" target="_blank"
                data-tooltip>
                <i class="fab fa-twitter"></i>
              </a>
            </li>
            {% endif %}

            {% if options.pinterest_button_on_products %}
            <li class="list-inline-item">
              <a href="https://pinterest.com/pin/create/bookmarklet/?media={{product.images.first}}&url={{store.base_url}}{{product.url}}&is_video=false&description={{product.name}}: {{ product.description | strip_html }}" class="has-tip tip-top radius button tiny button-pinterest trsn"
                title="{% t 'Share on Pinterest' %}" target="_blank" data-tooltip>
                <i class="fab fa-pinterest"></i>
              </a>
            </li>
            {% endif %}

            {% if options.tumblr_button_on_products %}
            <li class="list-inline-item">
              <a class="has-tip tip-top radius button tiny button-tumblr trsn" title="{% t 'Share on Tumblr' %}" href="//tumblr.com/widgets/share/tool?canonicalUrl={{store.base_url}}{{product.url}}">
                <i class="fab fa-tumblr"></i>
              </a>
            </li>
            <script id="tumblr-js" async src="https://assets.tumblr.com/share-button.js"></script>
            {% endif %}

            {% if options.whatsapp_button_on_products %}
            <li class="list-inline-item">
              <a id="whatsapp" class="has-tip tip-top radius button tiny button-whats trsn" href="https://api.whatsapp.com/send?text={% t 'Check this product' %} {{product.name}} | {{store.base_url}}{{product.url}}">
                <i class="fab fa-whatsapp"></i>
              </a>
            </li>
            {% endif %}

          </ul>
        </div>


      </form>
      <script>
        $('#product-sharing a').click(function() {
          return !window.open(this.href, 'Share', 'width=640,height=300');
        });
      </script>

    </div>
  </div>

  {% if options.related_products_visibility %}
  {% include 'related_products'%}
  {% endif %}

</div>


<script>
  $(document).ready(function() {
    $('#product-carousel').carousel({
      interval: false
    });
    $('.thumbs').click(function(e) {
      e.preventDefault();
      $("#product-carousel").carousel(parseInt($(this).attr('data-image')) - 1);
    });
    $("#product-link").click(function() {
      $(this).select();
    });
    $(".carousel").on("touchstart", function(event) {
      var xClick = event.originalEvent.touches[0].pageX;
      $(this).one("touchmove", function(event) {
        var xMove = event.originalEvent.touches[0].pageX;
        if (Math.floor(xClick - xMove) > 5) {
          $(this).carousel('next');
        } else if (Math.floor(xClick - xMove) < -5) {
          $(this).carousel('prev');
        }
      });
      $(".carousel").on("touchend", function() {
        $(this).off("touchmove");
      });
    });
  });
</script>

{% if product.options != empty %}
<script>
  var callbackFunction = function(event, productInfo) {
    // if productInfo is not emtpy or null
    if (!$.isEmptyObject(productInfo)) {

      if (productInfo.image != '') {
        var imageID = $('.thumbs img').filter(function(image) {
          return this.src.includes(productInfo.image_id)
        }).parent().data('image')
        $("#product-carousel").carousel(parseInt(imageID) - 1);
      }

      {% if options.product_sku_visibility %}
      if (productInfo.sku != '') {
        $(".sku_elem").html(productInfo.sku);
        $('#product-sku').css('visibility', 'visible');
      }
      {% endif %}

      //update stock
      if ((productInfo.stock == 0 && productInfo.stock_unlimited == false) || productInfo.status == 'not-available') {

        $('.product-out-stock').removeClass('hidden');
        $('.product-available').addClass('hidden');
        $('.product-unavailable').addClass('hidden');
        $('#stock').html('');
        $('#stock').hide();
      } else {
        $('.product-available').removeClass('hidden');
        $('.product-out-stock').addClass('hidden');
        $('.product-unavailable').addClass('hidden');

        if (productInfo.stock_unlimited == false && productInfo.stock > 0) {
          $('#stock').html(' <label class="col-sm-3 col-md-3 form-control-label">{% t "Stock" %}:</label><div class="col-sm-8 col-md-9"><span class="product-form-stock">' + productInfo.stock + '</span></div>');
          $('#stock').show();
          // if there is no discount
        } else {
          $('#stock').html('');
          $('#stock').hide();
        }
      }

      if (productInfo.discount == 0) {
        // update price
        $('#product-form-price').text(productInfo.price_formatted);
      } else {
        // update price with discount
        $('#product-form-price').text(productInfo.price_discount_formatted);

        // update price
        $('#product-form-discount').text('(' + productInfo.price_formatted + ')');
      }

      {% if options.currencies != blank and options.open_exchange_rates_token != blank %}
      changeCurrency(true);
      {% endif %}
    }
  };

  $(document).ready(function() {
    $(".product_option_value_file_upload").filestyle({
      icon: false
    });

    Jumpseller.productVariantListener(".qty-select select", {
      product: '{{ product | json }}',
      callback: callbackFunction
    });

    $.each($("select.prod-options"), function(select_index, select) {
      $.each($(this).children('option'), function(option_index, option) {
        var stock = $(option).data("variant-stock");

        if (stock != '0') {
          $(option).prop('selected', true).trigger('change'); // selecting the first non-stock zero option.
          return false;
        }
      });
    });
  });
</script>
{% endif %}

{% if options.display_cart_notification %}
<!-- Add to cart plugin -->
<script>
  function getProductOptions() {
    var options = {};
    $(".prod-options").each(function() {
      var option = $(this);
      options[option.attr('id')] = option.val();
    })
    return options;
  }
</script>
{% endif %}

{% if options.zoom_product_image %}
<script src="{{ 'jquery.zoom.js' | asset }}"></script>
<script>
  $(".zoom-img").zoom({
    on: "click"
  });
  $(".zoom-img").click(function() {
    if ($(this).is(".zoom-active")) {
      $(this).removeClass("zoom-active");
    } else {
      $(this).addClass("zoom-active");
    }
  });
</script>
{% endif %}
