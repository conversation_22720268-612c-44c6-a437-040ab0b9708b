<!-- End Instagram Section -->
{% if options.instafeed and social.instagram_url != blank %}
    <section id="instagram-section">
        <div class="row title-ig">
            <div class="col-12">
            <h2 class="page-header text-left">Instagram <span class="ig_account"><a href="{{ social.instagram_url }}" target="_blank" title="{% t "Follow Us" %} @{{ social.instagram_url | split: '/' | last }}" class="butn trsn mt-5 follows-int">/@{{ social.instagram_url | split: '/' | last }}</a></span></h2>          
          </div>
        </div>
        <div class="row ">
          <div id="instagram" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 limit-{{ options.sucla_instagram-limit }}"></div>
         <!-- <a href="{{ social.instagram_url }}" target="_blank" title="{% t "Follow Us" %} @{{ social.instagram_url | split: '/' | last }}" class="btn trsn mt-5"><i class="fa fa-instagram" aria-hidden="true"></i> {% t "Siguenos!" %}</a> -->
          <h3><a href="https://www.instagram.com/sucla_imports" target="_blank" title="Síguenos! @sucla_imports" class="btn secondary trsn"><i class="fab fa-instagram" aria-hidden="true"></i> Síguenos!</a></h3>
          <!--btn trsn mt-5 -->
        </div>
  
      <!-- Instagram code -->
      <script>
        $.ajax({
          url: "/instagram-app/media",
          data: {
            count: {{ options.sucla_instagram-limit }}
          },
                  success: function(json) {
            for (var i in json.posts) {
              if(i >= {{options.sucla_instagram-limit}}){continue};
              url = json.posts[i].thumbnail_url;
              shortcode = json.posts[i].shortcode;
  
              newElement = document.createElement('div');
              newElement.className = 'insta_img';
              newElement.style = 'background-image: url(' + url + ')';
  
              newElementLink = document.createElement('a');
              newElementLink.href = json.posts[i].permalink;
              newElementLink.target = "_blank";
              newElementLink.appendChild(newElement);
  
              var clientHeight = document.getElementById('instagram').clientHeight;
              document.getElementById("instagram").appendChild(newElementLink);
            }
          }
        });
      </script>
    </section>
  {% endif %}
  