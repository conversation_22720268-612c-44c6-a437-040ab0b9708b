<!DOCTYPE html>
<!--[if IE 9]><html class="lt-ie10" lang="en" > <![endif]-->
<html class="no-js" lang="{{ languages.first.locale }}" xmlns="http://www.w3.org/1999/xhtml">
<!--<![endif]-->

<head>
  <title>{{page_title}}</title>
  <meta name="description" content="{{meta_description}}" />
  <!-- ESTE ES UN COMENTARIO PARA VER SI ESTO SE CARGA EN LA PAGINA PRINCIPAL -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">

  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

  <meta name="robots" content="follow, all" />

  <!-- Set the viewport width to device width for mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  {% include 'og_meta_tags' %}

  {% if languages.size > 1 %}
  {% for language in languages %}
  <link rel="alternate" hreflang="{{language.locale | replace: '-', '_'}}" href="{{language.url}}" />
  {% endfor %}
  {% endif %}

  {%if template == "category" %}
  <link rel="canonical" href="{{category.url}}">
  {%else%}
  <link rel="canonical" href="{{current_url}}">
  {%endif%}

  <link rel="icon" type="image/x-icon"  href="{% if options.favicon != empty %}{{ options.favicon }}{% else %}{{"favicon.png" | asset }}{% endif %}">

  {{ 'jquery/3.3.1/jquery.min.js' | public_asset_tag }}

  <link rel="stylesheet" href="//stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css">
  <link rel="stylesheet" type="text/css" href="{{ 'app.css' | asset }}" />
  <link rel="stylesheet" type="text/css" href="{{ 'style.css' | asset }}" />
  <link rel="stylesheet" type="text/css" href="{{ 'color_pickers.css' | asset }}" />
  <link rel="stylesheet" type="text/css" href="{{ 'linear-icon.css' | asset }}" />
  {% if template == 'home' or template == 'product' %}
  <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
  <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css">
  <script src="//cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
  <script src="//cdnjs.cloudflare.com/ajax/libs/Swiper/11.0.5/swiper-bundle.min.js"></script>
  <link
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
  rel="stylesheet"
/>
<link
  href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css"
  rel="stylesheet"
/>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  {% endif %}

  {% include 'schema' %}

  {% if options.head_code != empty %}
  <!-- Custom head code -->
  {{options.head_code}}
  {% endif %}
</head>

<body {% if template =='home' %}class="home" {% endif %}>

  <!--[if lt IE 8]>
<p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve your experience.</p>
<![endif]-->
  {% if template == 'cart' or template == 'checkout' or template == 'revieworder' or template == 'success' %}
  <header class="checkout-header container">
    <div class="row">
      <div class="col-9">
        <div class="logo text-center mt-3">
          <a href="{{store.url}}" title="{{store.name}}" class="navbar-brand">
            {% if store.logo != empty %}
            <img src="{{store.logo}}" class="store-image" alt="{{store.name}}" />
            {% else %}
            {% if template == 'home' %}
            <h1><span class="text-logo">{{store.name}}</span></h1>
            {% else %}
            <span class="text-logo">{{store.name}}</span>
            {% endif %}
            {% endif %}
          </a>
        </div>
      </div>
    </div>
  </header>
  {% else %}
  <!-- Navigation -->
  <header class="site-header">
  <!-- Sección de mensajes y acciones superiores -->
  <div class="top-message">
    <div class="wrapper">
      <!-- Mensaje superior configurable -->
      <div class="message">
        {% if options.display_top_message %}
          <p><i class="linear-icon icon-{{options.top_message_icon}}"></i> {{ options.top_message}}</p>
        {% endif %}
      </div>
      
      <!-- Acciones superiores: moneda, idioma, cuenta -->
      <ul class="top-actions">
        <!-- Selector de moneda -->
        {% if options.currencies == blank %}
          <li class="dropdown no-currency-selected"></li>
        {% elsif options.currencies != blank and options.open_exchange_rates_token != blank %}
          <li class="dropdown">
            <a href="#" class="dropdown-toggle nav-link trsn" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
              <span id="current_currency">{{store.currency_code}}</span>
              <span class="caret"></span>
            </a>
            <ul class="dropdown-menu">
              <li><a href="#" onclick="Jumpseller.setCurrency('{{store.currency_code}}');" class="trsn nav-link" title="{{store.currency_code}}">{{store.currency_code}}</a></li>
              {% assign store_currencies = options.currencies | split: ',' %}
              {% for currency in store_currencies %}
                <li><a href="#" onclick="Jumpseller.setCurrency('{{currency}}');" class="trsn nav-link" title="{{currency}}">{{currency}}</a></li>
              {% endfor %}
            </ul>
          </li>
        {% endif %}

        <!-- Selector de idioma -->
        {% if languages.size > 1 %}
          <li class="dropdown">
            <a href="#" class="dropdown-toggle trsn nav-link" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
              <span>{{languages.first.name}}</span>
              <span class="caret"></span>
            </a>
            <ul class="dropdown-menu">
              {% for language in languages %}
                <li><a href="{{language.url}}" class="trsn nav-link" title="{{language.name}}">{{language.name}}</a></li>
              {% endfor %}
            </ul>
          </li>
        {% endif %}

        <!-- Enlaces de cuenta de cliente -->
        {% if store.customers_enabled %}
          <li {% if template=="customer_account" %}class="active"{% endif %}>
            <a href="{% if customer %}{{customer_account_url}}{% else %}{{customer_login_url}}{% endif %}" id="login-link-desktop" class="trsn nav-link" title="{% if customer %}{% t "See my Details" %}{% else %}{% t "Login to" %}{{store.name}}{% endif %}">
              <span class="customer-name">
                {% if customer %}{{ customer.name }}{% else %}{% t "Login" %}{% endif %}
              </span>
            </a>
          </li>
        {% endif %}
        
        {% if customer %}
          <li>
            <a title="{% t 'Logout' %}" href="{{customer.logout_url}}" class="nav-link">
              <span>{% t 'Logout' %}</span>
            </a>
          </li>
        {% endif %}
      </ul>
    </div>
  </div>

  <!-- Cabecera principal SOLO DESKTOP -->
  <div class="container main-header d-none d-lg-block">
    <div class="row align-items-center">
      <!-- Logo -->
      <div class="col-lg-2">
        <div class="logo">
          <a href="{{store.url}}" title="{{store.name}}" class="navbar-brand">
            {% if store.logo != empty %}
              <img src="{{store.logo}}" class="store-image" alt="{{store.name}}" />
            {% else %}
              {% if template == 'home' %}
                <h1><span class="text-logo">{{store.name}}</span></h1>
              {% else %}
                <span class="text-logo">{{store.name}}</span>
              {% endif %}
            {% endif %}
          </a>
        </div>
      </div>
      <!-- Menú de navegación principal -->
      <div class="col-lg-8">
        <nav class="navbar navbar-expand-lg navbar-light vertical_menu">
          <div class="collapse navbar-collapse {{options.position_nav}}" id="navbarsContainer-2">
            <ul class="navbar-nav w-100 justify-content-center">
              {% for item in menu.main.items %}
                {% include 'navigation_menu' with item %}
              {% endfor %}
            </ul>
          </div>
        </nav>
      </div>
      <!-- Iconos búsqueda, carrito y login -->
      <div class="col-lg-2">
        <div class="header-icons d-flex justify-content-end align-items-center gap-3">
          <!-- Buscador Desktop -->
          <form id="search_mini_form" method="get" action="/search" style="display:none; margin-right:10px;">
            <div class="input-group">
              <input type="text" name="q" class="form-control form-control-sm" placeholder="Buscar productos..." autocomplete="off" />
              <button type="submit" class="btn btn-link"><i class="linear-icon icon-0803-magnifier"></i></button>
            </div>
          </form>
          <button id="search-btn" class="btn btn-link"><i class="linear-icon icon-0803-magnifier"></i></button>
          {% if options.disable_shopping_cart != true %}
            <button id="cart-btn" class="btn btn-link position-relative">
              <i class="linear-icon icon-0334-cart"></i>
              <span class="cart-count">{{ order.products_count }}</span>
            </button>
          {% endif %}
          <!-- <a href="{% if customer %}{{customer_account_url}}{% else %}{{customer_login_url}}{% endif %}" id="login-link-desktop" class="trsn nav-link ms-2" title="{% if customer %}{% t 'See my Details' %}{% else %}{% t 'Login to' %}{{store.name}}{% endif %}">
            <span class="customer-name">
              {% if customer %}{{ customer.name }}{% else %}{% t 'Login' %}{% endif %}
            </span>
          </a> -->
        </div>
        <!-- Panel lateral de carrito -->
        <div id="cart" class="absolute-bg" style="display: none">
          <div class="lateral-box right">
            {% include 'cart_lateral' %}
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- FIN SOLO DESKTOP -->
  
  <!-- Header Móvil Reestructurado -->
  <div class="row d-block d-lg-none">
    <div class="col-12">
      <!-- Barra superior móvil -->
      <div class="d-flex justify-content-between align-items-center py-2 px-3">
        <!-- Botón menú hamburguesa -->
        <button id="mobile-nav-btn" class="btn btn-link p-0" style="font-size: 1.5rem;">
          <i class="linear-icon icon-0812-menu"></i>
        </button>
        <!-- Logo móvil -->
        <div class="logo mx-auto" style="margin-top:0 !important; margin-bottom:0 !important;">
          <a href="{{store.url}}" title="{{store.name}}" class="navbar-brand">
            {% if store.logo != empty %}
              <img src="{{store.logo}}" class="store-image" alt="{{store.name}}" /> <!-- El CSS controlará el tamaño -->
            {% else %}
              <span class="text-logo">{{store.name}}</span>
            {% endif %}
          </a>
        </div>
        <!-- Iconos búsqueda y carrito -->
        <div class="d-flex align-items-center">
          <button id="search-btn-mobile" class="btn btn-link p-0 me-2" style="font-size: 1.3rem;"><i class="linear-icon icon-0803-magnifier"></i></button>
          {% if options.disable_shopping_cart != true %}
            <button id="cart-btn-mobile" class="btn btn-link position-relative p-0" style="font-size: 1.3rem;">
              <i class="linear-icon icon-0334-cart"></i>
              <span class="cart-count">{{ order.products_count }}</span>
            </button>
          {% endif %}
        </div>
      </div>
      <!-- Formulario de búsqueda móvil -->
      <div class="px-3 pb-2">
           <form id="search_mini_form_mobile" method="get" action="/search" style="display:none; width: 100%;">
              <div class="input-group">
                <input type="text" name="q" class="form-control form-control-sm" placeholder="{% t 'Search for products' %}" autocomplete="off" />
                <button type="submit" class="btn btn-link"><i class="linear-icon icon-0803-magnifier"></i></button>
              </div>
            </form>
      </div>

      <!-- Menú móvil lateral (estructura del backup) -->
      <div id="mobile-nav" class="absolute-bg" style="display: none;">
        <nav id="navbarMobile" class="lateral-box left">
          <button id="close-mobile-nav-btn" class="btn btn-link btn-block text-left lateral-header-btn">
            <i class="linear-icon icon-0811-cross pr-2 pb-2"></i> <span>{% t 'Close' %}</span>
          </button>
          {% include 'mobile_menu' %}
          {% comment %}
            El parcial 'mobile_menu' debería contener idealmente:
            <ul class="navbar-nav">
              {% for item in menu.main.items %}
                {% include 'navigation_mobile_menu' with item %}
              {% endfor %}
            </ul>
            <ul class="navbar-nav utility-nav"> (para moneda, idioma, cuenta) </ul>
            <ul class="social list-inline social-networks"> (redes sociales) </ul>
            Si no, estos elementos deberían añadirse aquí directamente o dentro del parcial.
          {% endcomment %}
        </nav>
      </div>

      <!-- Carrito lateral móvil -->
      <div id="cart-mobile" class="absolute-bg" style="display: none">
        <div class="lateral-box right">
          {% include 'cart_lateral' %}
          <button id="close-cart-mobile-btn" class="btn btn-link btn-block text-left lateral-header-btn" style="margin-top:15px;">
            <i class="linear-icon icon-0811-cross pr-2 pb-2"></i> <span>{% t 'Close' %}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</header>
  {% endif %}
  <!-- Page Content -->
  {{content}}
  <!-- Footer -->
  {% if template == 'cart' or template == 'checkout' or template == 'revieworder' or template == 'success' %}
  {% else %}
  {% comment %}
  {% include 'features_footer' %}
  {% endcomment %}
  {% endif %}
  {% include 'footer' %}
  <!-- /.container -->

  <!-- Css -->
  <link rel="stylesheet" href="//use.fontawesome.com/releases/v5.7.2/css/all.css" integrity="sha384-fnmOCqbTlWIlj8LyTjo7mOUStjsKC4pOpQbqyi7RrhN7udi9RwhKkMHpvLbHG9Sr" crossorigin="anonymous">
  {% if options.display_cart_notification %}
  <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css">
  {% endif %}

  <!-- Bootstrap Core JavaScript -->
  <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js"></script>
  <script src="//stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>

  <!-- Script to Activate Tooltips -->
  <script>
    $(function() {
      $('[data-toggle="tooltip"]').tooltip()
      $('.carousel').carousel()
    })
  </script>

  <script src="//cdn.jsdelivr.net/bootstrap.filestyle/1.1.0/js/bootstrap-filestyle.min.js"></script>
  <script src="{{ 'main.js' | asset }}"></script>

  {% if options.currencies != blank and options.open_exchange_rates_token != blank %}
  <script src="//cdnjs.cloudflare.com/ajax/libs/money.js/0.2.0/money.min.js"></script>
  <script src="//cdnjs.cloudflare.com/ajax/libs/accounting.js/0.4.1/accounting.min.js"></script>
  <script>
    var open_exchange_rates_token = '{{options.open_exchange_rates_token}}';
    var i18n_decimal_mark = '{{store.i18n_decimal_mark}}';

    if (typeof(Storage) !== "undefined") {
      if (sessionStorage.getItem('global_currency') == null) {
        sessionStorage.setItem('global_currency', '{{ store.currency_code }}');
        sessionStorage.setItem('store_currency', '{{ store.currency_code }}');
      }
    } else {
      // Sorry! No Web Storage support..
      console.log("Unable to use multi-currency on this store. Please update your browser.");
      $('#current_currency').parents('li').hide();
    }
  </script>
  {% endif %}

  {% if options.display_cart_notification %}
  <script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
  <script>
    var shoppingCartMessage = '{% t "Go to Shopping Cart." %}';
    var singleProductMessage = '{% t "has been added to the shopping cart." %}'
    var multiProductMessage = '{% t "have been added to the shopping cart." %}'
  </script>
  <script src="{{ 'addtocart.js' | asset }}"></script>
  {% endif %}

  {% include 'fonts' %}

  <script src="{{ 'theme.js' | asset }}"></script>

  {% if options.body_code != empty %}
  <!-- Custom body code -->
  {{options.body_code}}
  {% endif %}
</body>

</html>
