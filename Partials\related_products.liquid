{% unless recommended.products_count == 0 %}
    <div class="row related-products mt-3">
      <div class="col-lg-12">
        <!-- Page Heading -->
        <div class="row">
          <div class="col-12">
            <h3 class="page-header">{% t 'You might also be interested in:' %}</h3>
          </div>
          <div id="related-carousel" class="owl-carousel">
            {% for recommend_product in recommended.products limit: options.related_products_limit %}
            <div class="item product-block">
              <div class="product-image-block">
                {% if recommend_product.discount > 0 %}
                <span class="flag sale">{% t "Sale" %}</span>
                {% endif %}
                {% if recommend_product.stock == 0 and recommend_product.stock_unlimited == false %}
                <span class="flag out-stock">{% t "Out of Stock" %}</span>
                {% elsif recommend_product.status == 'not-available' %}
                <span class="flag out-stock">{% t "Not Available" %}</span>
                {% endif %}
                {% if recommend_product.images != empty %}
                <a href="{{recommend_product.url}}"><img class="img-fluid img-portfolio img-hover mb-3" src="{{recommend_product.images.first | resize:'600x700'}}" srcset="{{recommend_product.images.first | resize:'600x700'}} 1x, {{recommend_product.images.first | resize:'1200x1400'}} 2x" alt="{{recommend_product.name | escape}}" /></a>
                {% else %}
                <a href="{{recommend_product.url}}"><img class="img-fluid img-portfolio img-hover mb-3" src="{{ 'no-image-home.jpg' | asset }}" alt="{{recommend_product.name | escape}}"></a>
                {% endif %}
              </div>
              <div class="caption">
                <h3><a href="{{recommend_product.url}}">{{ recommend_product.name | truncate:50 }}</a></h3>
                {% if recommend_product.brand != empty %}<h6>{{recommend_product.brand}}</h6>{% endif %}
                {% if options.hide_price != true %}
                <div class="price-mob">
                  {% if recommend_product.stock == 0 and recommend_product.stock_unlimited == false %}
                  <span class="line-through price"> {{ recommend_product.price | price }} </span>
                  {% elsif recommend_product.status == 'not-available' %}
                  <span class="product-block-not-available">{% t "Not Available" %}</span>
                  {% else %}
                  {% if recommend_product.discount > 0 %}
                  <span class="price">{{ recommend_product.price | minus:recommend_product.discount | price }}</span> <span class="product-block-discount price"> {{ recommend_product.price | price }} </span>
                  {% else %}
                  <span class="price">{{ recommend_product.price | price }}</span>
                  {% endif %}
                  {% endif %}
                </div>
                {% endif %}
              </div>
            </div>
            {% endfor %}
            
          </div>
        </div>
      </div>
    </div>
    
    <script>
      $('#related-carousel').owlCarousel({
        loop: false,
        autoplay: true,
        autoplayHoverPause: false,
        autoplayTimeout: 5000,
        nav: true,
        dots: true,
        margin: 30,
        responsive:{
          0:{
            items:2
          },
          768:{
            items:3
          }
        }
      })
    </script>
    {% endunless %}
    