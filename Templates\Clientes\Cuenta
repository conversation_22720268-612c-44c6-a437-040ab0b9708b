<div class="container">
  <div class="row">
    <div class="col-sm-4">
      <div class="row">
        <div class="col-12">
          <div class="card mb-3">
            <div class="card-header"><h2 class="legend card-title">{% t 'Contact' %}</h2></div>
            <div class="card-body">
              {% if customer.name != empty %}
              <p><strong>{{ customer.name }}</strong></p>
              {% endif %}
              <p>{{customer.email}}</p>
              <p>{{ customer.phone }}</p>
              <a href="{{customer.edit_url}}" class="btn btn-secondary btn-sm" title="{% t 'Edit' %}"><i class="fas fa-pencil-alt"></i></a>
            </div>
          </div>
        </div>


        <!-- CUSTOMER SHIPPING ADDRESS -->
        <div class="col-12">
          <div class="card mb-3">
            <div class="card-header"><h2 class="legend card-title">{% t "Shipping Address" %}</h2></div>
            <div class="card-body">
            <a class="add-link" href="{{customer.add_shipping_address_url}}"><i class="fas fa-plus"></i> {% t 'Add' %}</a>
              {% for shipping_address in customer.shipping_addresses %}
              <div class="shipping_address">
              <p class="address">{{ shipping_address.formatted}}</p>
              <a class="btn btn-secondary btn-sm" href="{{ shipping_address.edit_url}}" title="{% t 'Edit' %}" alt="{% t 'Edit' %}"><i class="fas fa-pencil-alt"></i></a>
              {% if shipping_address.default == false %}
              <a class="btn btn-secondary btn-sm" href="{{ shipping_address.set_primary_url}}" title="{% t 'Make Primary' %}" alt="{% t 'Make Primary' %}"><i class="fas fa-star"></i></a>
              {% endif %}
              <a class="btn btn-secondary btn-sm" href="{{ shipping_address.delete_url}}" title="{% t 'Delete' %}" alt="{% t 'Delete' %}"><i class="fas fa-times"></i></a>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>

        <!-- CUSTOMER BILLING ADDRESS -->
        <div class="col-12">
          <div class="card mb-3">
            <div class="card-header"><h2 class="legend card-title">{% t "Billing Address" %}</h2></div>
            <div class="card-body">
              <a class="add-link" href="{{customer.add_billing_address_url}}"><i class="fas fa-plus"></i> {% t 'Add' %}</a>
              {% for billing_address in customer.billing_addresses %}
              <div class="billing_address">
                <p class="address">{{ billing_address.formatted }}</p>
                <a class="btn btn-secondary btn-sm" href="{{ billing_address.edit_url}}" title="{% t 'Edit' %}" alt="{% t 'Edit' %}"><i class="fas fa-pencil-alt"></i></a>
                {% if billing_address.default == false %}
                <a class="btn btn-secondary btn-sm" href="{{ billing_address.set_primary_url}}" title="{% t 'Make Primary' %}" alt="{% t 'Make Primary' %}"><i class="fas fa-star"></i></a>
                {% endif %}
                <a class="btn btn-secondary btn-sm" href="{{ billing_address.delete_url}}" title="{% t 'Delete' %}" alt="{% t 'Delete' %}"><i class="fas fa-times"></i></a>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>

      </div>
    </div>

    <div class="col-sm-8 col-12">
      <div id="customer-summary">
        <div class="card">
          <div class="card-body">
            <h2 class="block-header">{% t "Previous Sales" %}</h2>
            {% if customer.orders == empty %}
            {% t "You have not made any purchases." %}
            {% else %}
            {% for order in customer.orders %}
            <div class="orders">
              <div class="orders_header">
                <h5>{% t 'Order' %}: #{{order.id}}</h5>
                <a class="collapsed" data-toggle="collapse" href="#collapse{{order.id}}" role="button" aria-expanded="false" aria-controls="collapseExample">
                  <span class="show">{% t "View Order Details" %}</span>
                  <span class="hide">{% t "Hide Order Details" %}</span>
                </a>
                <a href="{{order.duplicate_url}}" target="_blank" class="reorder-btn btn btn-sm btn-adc">{% t "Reorder" %}</a>
              </div>
              <div class="collapse orders_body" id="collapse{{order.id}}">
                <div class="orders_item">
                  <label>{% t "Status" %}: </label>
                  <span class="label radius {{ order.status_id | downcase | replace:' ','-' }}">
                    {{order.status}}
                  </span>
                </div>
                <div class="orders_item">
                  <label>{% t "Order Date" %}: </label> {{order.date}}
                </div>
                <div class="orders_item">
                  <label>{% t 'Order Total' %}: </label>
                  <span class="product-block-price">{{order.total | price }}</span>
                </div>
                <hr>
                <div class="orders_item">
                  <h4>{% t "The order" %} #{{order.id}} {% t "contains the following items" %}:</h4>
                  <div class="row">
                    <div class="col-9">
                      {% t 'Product Details' %}
                    </div>
                    <div class="col-3 text-right">
                      {% t 'Price' %}
                    </div>
                  </div>
                  {% for ordered_product in order.products %}
                  {% if ordered_product.exists? %}
                  <div class="ordered_product">
                    <div class="row">
                      <div class="col-9">
                        {% if ordered_product.options != empty %}
                        <h5>{{ordered_product.qty}} x {{ordered_product.name}}</h5>
                        <div class="clearfix"></div>
                        {% for option in ordered_product.options %}
                        <small><strong>{{option.name}}:</strong> {{option.value}}   </small>
                        {% endfor %}
                        {% else %}
                        <h5>{{ordered_product.qty}} x {{ordered_product.name}}</h5>
                        {% endif %}
                      </div>
                      <div class="col-3 text-right">
                        <h5><span class="product-block-price">{{ordered_product.price | price }}</span></h5>
                      </div>
                    </div>
                  </div>
                  {% else %}
                  <div class="ordered_product">
                    <div class="row">
                      <div class="col-9">
                        {% if ordered_product.options != empty %}
                        <small style="color:#ff0000">{% t 'This product is not available' %}</small>
                        <h5>{{ordered_product.qty}} x {{ordered_product.name}}</h5>
                        <div class="clearfix"></div>
                        {% for option in ordered_product.options %}
                        <small><strong>{{option.name}}:</strong> {{option.value}}   </small>
                        {% endfor %}
                        {% else %}
                        <small style="color:#ff0000">{% t 'This product is not available' %}</small>
                        <h5>{{ordered_product.qty}} x {{ordered_product.name}}</h5>
                        {% endif %}
                      </div>
                      <div class="col-3 text-right">
                        <h5><span class="product-block-price">{{ordered_product.price | price }}</span></h5>
                      </div>
                    </div>
                  </div>
                  {% endif %}
                  {% endfor %}
                </div>
                <div class="order_amounts">
                  {% if order.shipping > 0 %}
                  <div class="orders_item text-right">
                    <label>{% t 'Shipping' %}: </label>
                    <span class="product-block-price">{{order.shipping | price }}</span>
                  </div>
                  {% endif %}
                  <div class="orders_item text-right">
                    <label>{% t 'Subtotal' %}: </label>
                    <span class="product-block-price">{{order.subtotal | price }}</span>
                  </div>
                  {% if order.discount > 0 %}
                  <div class="orders_item text-right">
                    <label>{% t 'Discounts' %}: </label>
                    <span class="product-block-price">{{order.discount | price }}</span>
                  </div>
                  {% endif %}
                  <div class="orders_item text-right">
                    <h3>
                      <strong>{% t 'Total' %}:
                        <span class="product-block-price"> {{order.total | price }}</span></strong>
                    </h3>
                  </div>
                </div>
              </div>
            </div>
            {% endfor %}
            {% endif %}
          </div>
        </div>
      </div>

    </div>
  </div>

</div>
