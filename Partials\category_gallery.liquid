{% comment %}
  Component: prada-gallery.liquid
  Descripción: Galería con layout alternado (1-2-1-2) y efecto hover en textos
{% endcomment %}

<div class="prada-gallery-container">
  <!-- Primera fila (1 imagen) -->
  <div class="prada-row prada-row-single">
    <div class="prada-item prada-item-large">
      <a href="{{ category_1_url }}" class="prada-item-link">
        <div class="prada-item-image">
          <img src="{{ '1.png' | asset }}" alt="{{ category_1_title }}">
        </div>
        <div class="prada-item-text">
          <h3 class="prada-title">{{ category_1_title }}</h3>
          <p class="prada-subtitle">{{ category_1_subtitle }}</p>
          <span class="prada-button">{{ category_1_button_text | default: 'EXPLORAR' }}</span>
        </div>
      </a>
    </div>
  </div>
  
  <!-- Segunda fila (2 imágenes) -->
  <div class="prada-row prada-row-double">
    <div class="prada-item prada-item-medium">
      <a href="{{ category_2_url }}" class="prada-item-link">
        <div class="prada-item-image">
          <img src="{{ '1-a.png' | asset }}" alt="{{ category_2_title }}">
        </div>
        <div class="prada-item-text">
          <h3 class="prada-title">{{ category_2_title }}</h3>
          <p class="prada-subtitle">{{ category_2_subtitle }}</p>
          <span class="prada-button">{{ category_2_button_text | default: 'COMPRAR' }}</span>
        </div>
      </a>
    </div>
    <div class="prada-item prada-item-medium">
      <a href="{{ category_3_url }}" class="prada-item-link">
        <div class="prada-item-image">
          <img src="{{ '1-b.png' | asset }}" alt="{{ category_3_title }}">
        </div>
        <div class="prada-item-text">
          <h3 class="prada-title">{{ category_3_title }}</h3>
          <p class="prada-subtitle">{{ category_3_subtitle }}</p>
          <span class="prada-button">{{ category_3_button_text | default: 'COMPRAR' }}</span>
        </div>
      </a>
    </div>
  </div>
  
  <!-- Tercera fila (1 imagen) -->
  <div class="prada-row prada-row-single">
    <div class="prada-item prada-item-large">
      <a href="{{ category_4_url }}" class="prada-item-link">
        <div class="prada-item-image">
          <img src="{{ 'banner-instragram.png' | asset }}" alt="{{ category_4_title }}">
        </div>
        <div class="prada-item-text">
          <h3 class="prada-title">{{ category_4_title }}</h3>
          <p class="prada-subtitle">{{ category_4_subtitle }}</p>
          <span class="prada-button">{{ category_4_button_text | default: 'EXPLORAR' }}</span>
        </div>
      </a>
    </div>
  </div>
  
  <!-- Cuarta fila (2 imágenes) -->
  <div class="prada-row prada-row-double">
    <div class="prada-item prada-item-medium">
      <a href="{{ category_5_url }}" class="prada-item-link">
        <div class="prada-item-image">
          <img src="{{ '2-a.png' | asset }}" alt="{{ category_5_title }}">
        </div>
        <div class="prada-item-text">
          <h3 class="prada-title">{{ category_5_title }}</h3>
          <p class="prada-subtitle">{{ category_5_subtitle }}</p>
          <span class="prada-button">{{ category_5_button_text | default: 'COMPRAR' }}</span>
        </div>
      </a>
    </div>
    <div class="prada-item prada-item-medium">
      <a href="{{ category_6_url }}" class="prada-item-link">
        <div class="prada-item-image">
          <img src="{{ '2-b.png' | asset }}" alt="{{ category_6_title }}">
        </div>
        <div class="prada-item-text">
          <h3 class="prada-title">{{ category_6_title }}</h3>
          <p class="prada-subtitle">{{ category_6_subtitle }}</p>
          <span class="prada-button">{{ category_6_button_text | default: 'COMPRAR' }}</span>
        </div>
      </a>
    </div>
  </div>
</div>

<style>
 /* Estilos para la galería estilo Prada */
.prada-gallery-container {
  width: 100%;
  max-width: 1660px;
  margin: 0 auto;
  padding: 30px 15px;
  box-sizing: border-box;
}

.prada-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  justify-content: center;
}

.prada-row-double {
  flex-wrap: wrap;
}

.prada-item {
  position: relative;
  overflow: hidden;
}

.prada-item-large {
  width: 100%;
  height: 774px; /* Dimensión específica solicitada */
  max-width: 1660px;
}

.prada-item-medium {
  width: calc(50% - 10px);
  height: 935px; /* Dimensión específica solicitada */
  max-width: 810px;
}

.prada-item-image {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.prada-item-image img {
  width: 100%;
  height: 100%;
  object-fit: center;
  object-position: center;
  transition: transform 0.4s ease;
}

.prada-item:hover .prada-item-image img {
  transform: scale(1.03); /* Zoom más sutil para que no se pierdan elementos importantes */
}

.prada-item-text {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 2;
}

.prada-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #000;
  display: inline-block;
  position: relative;
}

.prada-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 100%;
  height: 1px;
  background-color: #000;
  transition: all 0.3s ease;
}

.prada-subtitle {
  margin: 5px 0 0;
  font-size: 14px;
  color: #666;
}

.prada-button {
  display: inline-block;
  padding: 8px 15px;
  background-color: transparent;
  color: #000;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: 0;
}

.prada-item:hover .prada-title::after {
  height: 0;
}

.prada-item:hover .prada-button {
  background-color: #000;
  color: #fff;
  opacity: 1;
}

/* Responsive para tablets */
  @media (max-width: 1200px) {
    .prada-item-large {
      height: 0;
      padding-bottom: 50%; /* Relación de aspecto aproximada para mantener proporción */
      position: relative;
    }
    
    .prada-item-medium {
      height: 0;
      padding-bottom: 60%; /* Relación de aspecto aproximada para mantener proporción */
      position: relative;
    }
    
    .prada-item-image {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }

/* Responsive para móviles */
  @media (max-width: 768px) {
    .prada-item-large {
      height: 0;
      padding-bottom: 70%; /* Ajustado para mejor visualización en móviles */
      position: relative;
    }
    
    .prada-item-medium {
      width: 100%;
      height: 0;
      padding-bottom: 70%; /* Misma proporción que large para consistencia */
      position: relative;
      margin-bottom: 20px;
    }
    
    .prada-row-double {
      flex-direction: column;
      gap: 20px;
    }
    
    .prada-item-image {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
    
    .prada-item-image img {
      object-position: center;
    }
  }

/* Para pantallas muy pequeñas */
  @media (max-width: 480px) {
    .prada-item-large, 
    .prada-item-medium {
      height: 0;
      padding-bottom: 100%; /* Relación cuadrada para móviles pequeños */
      position: relative;
    }
    
    .prada-gallery-container {
      padding: 10px 5px;
    }
    
    .prada-row {
      margin-bottom: 10px;
      gap: 10px;
    }
    
    .prada-item-text {
      top: 10px;
      left: 10px;
    }
    
    .prada-title {
      font-size: 16px;
    }
    
    .prada-subtitle {
      font-size: 12px;
    }
}
</style>