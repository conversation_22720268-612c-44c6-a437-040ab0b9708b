{% comment %}
  Component: category_buttons_divider.liquid
  Descripción: Botones de categoría con estilo lujoso que pueden funcionar como divisores
  Versión: 1.1
{% endcomment %}

<style>
  .category-buttons-divider {
    --primary-color: {{ options.category_buttons_primary_color | default: '#1a1a1a' }};
    --secondary-color: {{ options.category_buttons_secondary_color | default: '#ffffff' }};
    --accent-color: {{ options.category_buttons_accent_color | default: '#d4af37' }};
    --border-color: {{ options.category_buttons_border_color | default: '#e5e5e5' }};
    --button-spacing: {{ options.category_buttons_spacing | default: '1rem' }};
    --button-padding: {{ options.category_buttons_padding | default: '0.75rem 1.5rem' }};
    --divider-height: {{ options.category_buttons_divider_height | default: '1px' }};
    --transition-duration: 0.3s;
    --button-radius: {{ options.category_buttons_radius | default: '4px' }};
  }

  .category-buttons-divider {
    max-width: {{ options.category_buttons_max_width | default: '1660px' }} !important;
    margin: {{ options.category_buttons_margin | default: '2rem auto' }} !important;
    width: 100% !important;
  }

  .category-buttons-container {
    display: flex !important;
    flex-wrap: nowrap !important;
    gap: var(--button-spacing) !important;
    position: relative !important;
    width: 100% !important;
    justify-content: space-between !important;
  }

  {% if options.category_buttons_show_divider %}
  .category-buttons-container::before {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 0 !important;
    right: 0 !important;
    height: var(--divider-height) !important;
    background-color: var(--border-color) !important;
    z-index: 1 !important;
  }
  {% endif %}

  .category-button {
    position: relative !important;
    z-index: 2 !important;
    padding: var(--button-padding) !important;
    background-color: #000000 !important;
    color: #ffffff !important;
    border: none !important;
    text-transform: uppercase !important;
    letter-spacing: 0.1em !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    transition: all var(--transition-duration) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 50px !important;
    flex: 1 1 0 !important;
    text-align: center !important;
    cursor: pointer !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .category-button:hover {
    background-color: #333333 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1) !important;
  }

  .category-button.active {
    background-color: var(--primary-color) !important;
    color: var(--secondary-color) !important;
  }

  @media (max-width: 1200px) {
    .category-button {
      font-size: 0.8rem !important;
      padding: 0.5rem 1rem !important;
    }
  }

  @media (max-width: 768px) {
    .category-buttons-container {
      /* flex-wrap: wrap !important; */ /* Eliminado para evitar salto de línea */
      gap: 0.5rem !important;
    }

    .category-button {
      /* flex: 1 1 calc(33.333% - 0.5rem) !important; */ /* Eliminado para usar flex base y evitar salto */
      font-size: 0.75rem !important;
      padding: 0.5rem 0.75rem !important;
      min-height: 44px !important;
    }
  }

  @media (max-width: 480px) {
    .category-buttons-container {
      gap: 0.5rem !important;
    }

    .category-button {
      /* flex: 1 1 calc(50% - 0.25rem) !important; */ /* Eliminado para usar flex base y evitar salto */
      font-size: 0.7rem !important;
      letter-spacing: 0.05em !important;
    }
  }
</style>

{% if options.category_buttons_enabled %}
<div class="category-buttons-divider">
  {% if options.category_buttons_title != blank %}
    <h2 class="text-center" style="margin-bottom: 2rem !important; color: var(--primary-color) !important; font-family: {{ options.category_buttons_title_font | default: 'Raleway' }}, sans-serif !important; font-weight: 600 !important;">
      {{ options.category_buttons_title }}
    </h2>
  {% endif %}

  <div class="category-buttons-container">
    {% for i in (1..6) %}
      {% assign button_title = 'category_button_' | append: i | append: '_title' %}
      {% assign button_link = 'category_button_' | append: i | append: '_link' %}
      {% assign button_active = 'category_button_' | append: i | append: '_active' %}
      
      {% if options[button_title] != blank and options[button_link] != blank %}
        <a href="{{ options[button_link] }}" 
           class="category-button {% if options[button_active] %}active{% endif %}"
           {% if options.category_buttons_new_tab %}target="_blank"{% endif %}>
          {{ options[button_title] }}
        </a>
      {% endif %}
    {% endfor %}
  </div>
</div>
{% endif %} 