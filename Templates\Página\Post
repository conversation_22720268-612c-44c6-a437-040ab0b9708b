<!-- Page Content -->
<div class="container">

  <div class="row">
    <div class="col-12">
      <h1 class="page-header">{{page.title}}</h1>
    </div>
  </div>
  <div class="row">
    <!-- Blog Entries Column -->
    <div class="col-md-8 page post">
       {% if page.images == empty %}
          <img class="img-fluid" src="http://placehold.it/800x500" alt="{{ page.title }}">
       {% else %}
      <img class="img-fluid" src="{{ page.images.first }}" alt="{{ page.title }}" />
      {% endif %}
      <div class="text-formatted">
        {{page.body}}
      </div>
    </div>
 
    <!-- Blog Sidebar Widgets Column -->
    <div class="col-md-4 sidebar">
      <!-- Blog menu -->
      {% if menu.blog.items != empty %}
      <div class="card">
        <div class="card-header"><h4 card="title">{% t 'Featured Articles' %}</h4></div>
            <ul class="list-group list-group-flush">
              {% for item in menu.blog.items %}
              <li class="list-group-item">
                <a href="{{ item.url }}" title="{{item.name}}">{{ item.name }}</a>
              </li>
              {% endfor %}
            </ul>
        <!-- /.row -->
      </div>
      {% endif %}
 
      <!-- Side Widget blog -->
      {% if options.widget_blog_text != empty %}
      <div class="card">
        <div class="card-header"><h4 class="card-title">{{ options.widget_blog_title }}</h4></div>
        <div class="card-body">
        <p>{{ options.widget_blog_text }}</p>
        </div>
      </div>
      {% endif %}
 
    </div>
 
  </div>
  <!-- /.row -->
 </div>
 <!-- /.container -->
 