<!-- Facebook Meta tags for Product -->
<meta property="fb:app_id" content="283643215104248" />
{% if template == 'product' %}
  <meta property="og:title" content="{{product.name | escape}}" />
  <meta property="og:type" content="product" />
	{% if product.images == empty %}
  	<meta property="og:image" content="{{ store.logo }}" />
	{% else %}
    {% for image in product.images %}
     <meta property="og:image" content="{{image}}" />
    {% endfor %}
	{% endif %}

  {% if product.brand != blank %}
  	<meta property="og:brand" content="{{product.brand}}" />
  {% endif %}

  <meta property="product:is_product_shareable" content="1" />
  <meta property="product:original_price:amount" content="{{product.price}}"/>
  <meta property="product:original_price:currency" content="{{store.currency_code}}"/>
  <meta property="product:price:amount" content="{{product.price | minus:product.discount}}"/>
  <meta property="product:price:currency" content="{{store.currency_code}}"/>

  {% if product.stock == 0 and product.stock_unlimited == false %}
 	 <meta property="product:availability" content="oos"/>
  {% elsif product.status == 'not-available' %}
	  <meta property="product:availability" content="pending"/>
  {% else %}
	  <meta property="product:availability" content="instock"/>
  {% endif %}

{% elsif template == 'page' %}
  <meta property="og:title" content="{{page.title}}" />
  <meta property="og:type" content="article" />
  {% for image in page.images %}
  	<meta property="og:image" content="{{image}}" />
  {% endfor %}

{% elsif template == 'category' %}
  <meta property="og:title" content="{{category.name}}" />
  <meta property="og:type" content="website" />
  {% if category.images == empty %}
    {% assign image = category.products.first.images.first %}
    {% if image != blank %}
      <meta property="og:image" content="{{image | resize: '1200x1200' }}" />
    {% endif %}
	{% else %}
    {% for image in category.images %}
  	  <meta property="og:image" content="{{image | resize: '1200x1200' }}" />
    {% endfor %}
	{% endif %}

{% else %}
  <meta property="og:title" content="{{store.name}}" />
  <meta property="og:type" content="website" />
  <meta property="og:image" content="{{ store.logo }}" />
{% endif %}

<meta property="og:description" content="{{ meta_description }}" />
<meta property="og:url" content="{{store.base_url}}{{current_url}}" />
<meta property="og:site_name" content="{{store.name}}" />
<meta name="twitter:card" content="summary" />

{% for language in languages %}
	<meta property="og:locale{% if forloop.first != true %}:alternate{% endif %}" content="{{language.locale | replace: '-', '_'}}" />
{% endfor %}
