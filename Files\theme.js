// Mobile Header Buttons
$("#mobile-nav-btn").click(function() {
    $("#mobile-nav").toggle("slow");
  });
  $("#close-mobile-nav-btn").click(function() {
    $("#mobile-nav").toggle();
  });
  $("#cart-btn").click(function() {
    $("#cart").toggle();
  });
  $("#close-cart-btn").click(function() {
    $("#cart").toggle();
  });
  $("#search-btn").click(function() {
    $("#search_mini_form").toggle();
  });

// Manejo mejorado para el buscador móvil
$("#search-btn-mobile").click(function(e) {
  e.stopPropagation(); // Prevenir que el clic se propague al documento
  var searchForm = $("#search_mini_form_mobile");
  
  // Cierra otros paneles si están abiertos (ej. menú lateral)
  if ($("#mobile-nav").is(":visible")) {
    $("#mobile-nav").hide(); 
  }
  if ($("#cart-mobile").is(":visible")) {
    $("#cart-mobile").hide(); 
  }

  if (searchForm.hasClass('open')) {
    searchForm.removeClass('open').slideUp(200); // Cierra con animación
  } else {
    searchForm.addClass('open').slideDown(200); // Abre con animación
    searchForm.find('input[name="q"]').focus(); // Poner foco en el input
  }
});

// Cierra el buscador móvil si se hace clic fuera de él
$(document).click(function(e) {
  var searchForm = $("#search_mini_form_mobile");
  var mobileNav = $("#mobile-nav");
  var cartMobile = $("#cart-mobile");

  // Si el buscador está abierto y el clic NO fue dentro del buscador ni en su botón
  if (searchForm.hasClass('open') && 
      !searchForm.is(e.target) && 
      searchForm.has(e.target).length === 0 && 
      !$('#search-btn-mobile').is(e.target) && 
      $('#search-btn-mobile').has(e.target).length === 0) {
    searchForm.removeClass('open').slideUp(200);
  }
  
  // Considerar cerrar también el menú o carrito si se hace clic fuera (si es el comportamiento deseado)
  // if (mobileNav.is(":visible") && !mobileNav.is(e.target) && mobileNav.has(e.target).length === 0 && !$('#mobile-nav-btn').is(e.target) && $('#mobile-nav-btn').has(e.target).length === 0) {
  //   mobileNav.hide();
  // }
  // if (cartMobile.is(":visible") && !cartMobile.is(e.target) && cartMobile.has(e.target).length === 0 && !$('#cart-btn-mobile').is(e.target) && $('#cart-btn-mobile').has(e.target).length === 0) {
  //   cartMobile.hide();
  // }
});

  $("#categories-menu").click(function() {
    $("ul#categories").toggle();
  });
  $(".dropdown-btn").click(function() {
    let id = $(this).attr("data-id");
    let icon = $(this).children();
    $(`#${id}`).toggle();
    if ($(icon).is(".icon-0823-plus")) {
      $(icon).addClass("icon-0824-minus");
      $(icon).removeClass("icon-0823-plus");
    } else {
      $(icon).addClass("icon-0823-plus");
      $(icon).removeClass("icon-0824-minus");
    }
  });
  
  $(".utility-nav .button").click(function() {
    let id = $(this).attr("data-id");
    let icon = $(this).children(".linear-icon");
    $(`#${id}`).toggle();
    if ($(icon).is(".icon-0823-plus")) {
      $(icon).addClass("icon-0824-minus");
      $(icon).removeClass("icon-0823-plus");
      d;
    } else {
      $(icon).addClass("icon-0823-plus");
      $(icon).removeClass("icon-0824-minus");
    }
  });
  
  function secondarySliderArrows() {
    $("#secondary-slider .owl-next span").empty();
    $("#secondary-slider .owl-next span").append(
      '<i class="linear-icon icon-0829-chevron-right"></i>'
    );
    $("#secondary-slider .owl-prev span").empty();
    $("#secondary-slider .owl-prev span").append(
      '<i class="linear-icon icon-0828-chevron-left"></i>'
    );
  }
  
  secondarySliderArrows();
  
  $(window).scroll(function() {
    let sc = $(window).scrollTop();
    if (sc > 300) {
      $("#order-summary").addClass("fixed");
    } else {
      $("#order-summary").removeClass("fixed");
    }
  });
  
  $("#cart-btn-mobile").click(function() {
    $("#cart-mobile").toggle();
  });

  $("#close-cart-mobile-btn").click(function() {
    $("#cart-mobile").toggle();
  });
  