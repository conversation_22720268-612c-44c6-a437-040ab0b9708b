    <!-- {% if options.slider-1-image == empty and options.slider-2-image == empty and options.slider-3-image == empty  %}
    <div id="principal-slider" class="owl-carousel home-slider principal">
        <div class="item">
            <div class="layer"></div>
            <img src="{{ 'principal-slide-demo-1.jpg' | asset }}" alt="{{store.name}}">
            <a href="{{store.url}}/admin/themes/options/{{theme.id}}" >
                <div class="carousel-info-inner">
                <div class="btn btn-primary"
                    title="{% t 'Add your own Sliders' %}">{% t 'Add your own Sliders' %}
                </div>
                </div>
            </a>
        </div>
        <div class="item">
            <div class="layer"></div>
            <img src="{{ 'principal-slide-demo-2.jpg' | asset }}" alt="{{store.name}}">
            <a href="{{store.url}}/admin/themes/options/{{theme.id}}" >
            <div class="carousel-info-inner">
                <div class="btn btn-primary"
                    title="{% t 'Add your own Sliders' %}">{% t 'Add your own Sliders' %}
                </div>
            </div>
            </a>
        </div>
        <div class="item">
            <div class="layer"></div>
            <img src="{{ 'principal-slide-demo-3.jpg' | asset }}" alt="{{store.name}}">
                <a href="{{store.url}}/admin/themes/options/{{theme.id}}" >
            <div class="carousel-info-inner">
                <div class="btn btn-primary"
                    title="{% t 'Add your own Sliders' %}">{% t 'Add your own Sliders' %}
                </div>
            </div>
            </a>
        </div>
    </div>
    {% else %}
    <div class="owl-carousel principal-home-slider">
    <div id="principal-slider" class="owl-carousel home-slider principal">
    {% if options.slider-1-image != empty %}
        <div class="item">
            <div class="layer"></div>
            <img src="{{ options.slider-1-image }}" alt="{{ options.slider-1-text-button }}">
            {% if options.slider-1-link != empty %}
                    <a href="{{ options.slider-1-link }}" >
                <div class="carousel-info-inner">
                    {%if options.slider-1-text-button != empty %}
                    <div class="btn btn-primary"
                        title="{{ options.slider-1-text-button }}">{{ options.slider-1-text-button }}
                    </div>
                    {% endif %}
                </div>
                    </a>
            {% endif %}
        </div>
    {% endif %}
    {% if options.slider-2-image != empty %}
        <div class="item">
            <div class="layer"></div>
            <a href="{{ options.slider-2-link }}">
                <img src="{{ options.slider-2-image }}" alt="{{ options.slider-2-text-button }}">
            </a>
            {% if options.slider-2-link != empty %}
                    <a href="{{ options.slider-2-link }}" >
                <div class="carousel-info-inner">
                    {%if options.slider-2-text-button != empty %}
                    <div class="btn btn-primary"
                        title="{{ options.slider-2-text-button }}">{{ options.slider-2-text-button }}
                    </div>
                    {% endif %}
                </div>
                    </a>
            {% endif %}
        </div>
    {% endif %}
    {% if options.slider-3-image != empty %}
        <div class="item">
            <div class="layer"></div>
                <a href="{{ options.slider-3-link }}">
                <img src="{{ options.slider-3-image }}" alt="{{ options.slider-3-text-button }}">
            </a>
            {% if options.slider-3-link != empty %}
                    <a href="{{ options.slider-3-link }}" >
                <div class="carousel-info-inner">
                    {%if options.slider-3-text-button != empty %}
                    <div class="btn btn-primary"
                        title="{{ options.slider-3-text-button }}">{{ options.slider-3-text-button }}
                    </div>
                    {% endif %}
                </div>
                    </a>
            {% endif %}
        </div>
    {% endif %}
    </div>
    </div>
    {% endif %}
    <script>
        $('#principal-slider').owlCarousel({
            loop: {% if options.slider_loop %}true{% else %}false{% endif %},
            autoplay: {% if options.slider_autoplay %}true{% else %} false{% endif %},
            autoplayHoverPause: {% if options.slider_pause %} true{% else %} false{% endif %},
            autoplayTimeout: {{ options.slider_autoplay_speed }},
            nav: false,
            dots: true,
            items: 1,
            autoHeight:true
        })
    </script> -->

    {% if options.slider-1-image == empty and options.slider-2-image == empty and options.slider-3-image == empty %}
        <div id="principal-slider" class="swiper home-slider principal">
          <div class="swiper-wrapper">
            <div class="swiper-slide">
              <div class="layer"></div>
              <img src="{{ 'principal-slide-demo-1.jpg' | asset }}" alt="{{store.name}}">
              <a href="{{store.url}}/admin/themes/options/{{theme.id}}">
                <div class="carousel-info-inner">
                  <div class="btn btn-primary" title="{% t 'Add your own Sliders' %}">{% t 'Add your own Sliders' %}</div>
                </div>
              </a>
            </div>
            <div class="swiper-slide">
              <div class="layer"></div>
              <img src="{{ 'principal-slide-demo-2.jpg' | asset }}" alt="{{store.name}}">
              <a href="{{store.url}}/admin/themes/options/{{theme.id}}">
                <div class="carousel-info-inner">
                  <div class="btn btn-primary" title="{% t 'Add your own Sliders' %}">{% t 'Add your own Sliders' %}</div>
                </div>
              </a>
            </div>
            <div class="swiper-slide">
              <div class="layer"></div>
              <img src="{{ 'principal-slide-demo-3.jpg' | asset }}" alt="{{store.name}}">
              <a href="{{store.url}}/admin/themes/options/{{theme.id}}">
                <div class="carousel-info-inner">
                  <div class="btn btn-primary" title="{% t 'Add your own Sliders' %}">{% t 'Add your own Sliders' %}</div>
                </div>
              </a>
            </div>
          </div>
          <div class="swiper-pagination"></div>
        </div>
      {% else %}
        <div id="principal-slider" class="swiper principal-home-slider">
          <div class="swiper-wrapper">
            {% if options.slider-1-image != empty %}
              <div class="swiper-slide">
                <div class="layer"></div>
                <img src="{{ options.slider-1-image }}" alt="{{ options.slider-1-text-button }}">
                {% if options.slider-1-link != empty %}
                  <a href="{{ options.slider-1-link }}">
                    <div class="carousel-info-inner">
                      {% if options.slider-1-text-button != empty %}
                        <div class="btn btn-primary" title="{{ options.slider-1-text-button }}">{{ options.slider-1-text-button }}</div>
                      {% endif %}
                    </div>
                  </a>
                {% endif %}
              </div>
            {% endif %}
            {% if options.slider-2-image != empty %}
              <div class="swiper-slide">
                <div class="layer"></div>
                <img src="{{ options.slider-2-image }}" alt="{{ options.slider-2-text-button }}">
                {% if options.slider-2-link != empty %}
                  <a href="{{ options.slider-2-link }}">
                    <div class="carousel-info-inner">
                      {% if options.slider-2-text-button != empty %}
                        <div class="btn btn-primary" title="{{ options.slider-2-text-button }}">{{ options.slider-2-text-button }}</div>
                      {% endif %}
                    </div>
                  </a>
                {% endif %}
              </div>
            {% endif %}
            {% if options.slider-3-image != empty %}
              <div class="swiper-slide">
                <div class="layer"></div>
                <img src="{{ options.slider-3-image }}" alt="{{ options.slider-3-text-button }}">
                {% if options.slider-3-link != empty %}
                  <a href="{{ options.slider-3-link }}">
                    <div class="carousel-info-inner">
                      {% if options.slider-3-text-button != empty %}
                        <div class="btn btn-primary" title="{{ options.slider-3-text-button }}">{{ options.slider-3-text-button }}</div>
                      {% endif %}
                    </div>
                  </a>
                {% endif %}
              </div>
            {% endif %}
          </div>
          <div class="swiper-pagination"></div>
        </div>
      {% endif %}
      
      <script>
        var swiper = new Swiper('#principal-slider', {
          loop: {% if options.slider_loop %}true{% else %}false{% endif %},
          autoplay: {% if options.slider_autoplay %}true{% else %} false{% endif %},
          autoplay: {
            delay: {{ options.slider_autoplay_speed }},
            pauseOnMouseEnter: {% if options.slider_pause %} true{% else %} false{% endif %},
            disableOnInteraction: false, // Evita que el autoplay se detenga al interactuar
          },
          pagination: {
            el: ".swiper-pagination",
            clickable: true,
          },
          slidesPerView: 1,
          autoHeight: true, // Importante para contenido de altura variable
        });
      </script>