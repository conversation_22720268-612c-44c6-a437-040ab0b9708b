{% comment %}
  Component: luxury_brands_showcase.liquid
  Descripción: Showcase premium para marcas de lujo con diseño elegante y personalizable
  Versión: 1.0
  Autor: Desarrollado para Jumpseller
{% endcomment %}

<style>
  .luxury-showcase {
    --primary-color: #1a1a1a;
    --secondary-color: #ffffff;
    --accent-color: #d4af37;
    --text-primary: #1a1a1a;
    --text-secondary: #ffffff;
    --spacing-unit: 0.75rem;
    --transition-duration: 0.5s;
  }

  .luxury-showcase {
    max-width: 1660px;
    margin: 32px auto;

  }

  .luxury-dual-banner {
    display: grid;
    grid-template-columns: 1fr 1fr;
    # gap: calc(var(--spacing-unit) * 0.5);
    position: relative;
    margin-bottom: calc(var(--spacing-unit) * 2);
  }

  @media (max-width: 768px) {
    .luxury-dual-banner {
      grid-template-columns: 1fr;
    }
  }

  .luxury-banner-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 3/4;
  }

  .luxury-banner-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-duration);
  }

  .luxury-banner-image:hover img {
    transform: scale(1.05);
  }

  .luxury-banner-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent 50%, rgba(0,0,0,0.7));
    opacity: 0;
    transition: opacity var(--transition-duration);
  }

  .luxury-banner-image:hover .luxury-banner-overlay {
    opacity: 1;
  }

  .luxury-center-text {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #ffffff !important;
    z-index: 2;
    width: 80%;
  }

  .luxury-center-text h2 {
    font-family: 'Roboto', serif !important;
    font-size: 3.5rem !important;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
    font-weight: 400;
    letter-spacing: 0.1em;
    color: #ffffff !important;
    margin-bottom: 1rem;
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-duration);
  }

  .luxury-dual-banner:hover .luxury-center-text h2 {
    opacity: 1;
    transform: translateY(0);
  }

  .luxury-center-text p {
    font-size: 1rem;
    letter-spacing: 0.05em;
    margin-bottom: 1.5rem;
    opacity: 0;
    color: white !important;
    transform: translateY(20px);
    transition: all var(--transition-duration) 0.2s;
  }

  .luxury-dual-banner:hover .luxury-center-text p {
    opacity: 1;
    transform: translateY(0);
  }

  .luxury-button {
    display: inline-block;
    padding: 0.75rem 2rem;
    background-color: var(--secondary-color);
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 0.2em;
    font-size: 0.875rem;
    transition: all var(--transition-duration);
    border: 1px solid var(--secondary-color);
    opacity: 0;
    transform: translateY(20px);
  }

  .luxury-dual-banner:hover .luxury-button {
    opacity: 1;
    transform: translateY(0);
  }

  .luxury-button:hover {
    background-color: transparent;
    color: var(--secondary-color);
  }

  .luxury-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: calc(var(--spacing-unit) * 0.5);
    margin-top: calc(var(--spacing-unit) * 2);
  }

  .luxury-product-card {
    position: relative;
    overflow: hidden;
  }

  .luxury-product-image {
    aspect-ratio: 1;
    overflow: hidden;
  }

  .luxury-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-duration);
  }

  .luxury-product-card:hover .luxury-product-image img {
    transform: scale(1.1);
  }

  .luxury-product-info {
    padding: 1rem;
    text-align: center;
  }

  .luxury-product-title {
    font-family: 'Didot', serif;
    font-size: 1.125rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
  }

  .luxury-product-price {
    color: var(--accent-color);
    font-weight: 500;
  }
</style>

{% if options.showcase_enabled %}
<div class="luxury-showcase">
  <!-- Banner Dual con Texto Central -->
  <div class="luxury-dual-banner">
    <div class="luxury-banner-image">
      {% if options.showcase_banner_left %}
        <img src="{{ options.showcase_banner_left.url }}" alt="Luxury Fashion">
      {% else %}
        <div style="background: #f5f5f5; height: 100%; display: flex; align-items: center; justify-content: center;">
          <p>Por favor, configura la imagen del banner izquierdo en Temas > Personalizar > Luxury Showcase</p>
        </div>
      {% endif %}
      <div class="luxury-banner-overlay"></div>
    </div>
    <div class="luxury-banner-image">
      {% if options.showcase_banner_right %}
        <img src="{{ options.showcase_banner_right.url }}" alt="Designer Collection">
      {% else %}
        <div style="background: #f5f5f5; height: 100%; display: flex; align-items: center; justify-content: center;">
          <p>Por favor, configura la imagen del banner derecho en Temas > Personalizar > Luxury Showcase</p>
        </div>
      {% endif %}
      <div class="luxury-banner-overlay"></div>
    </div>
    <div class="luxury-center-text">
      <h2>{{ options.showcase_title | default: 'MICHAEL KORS' }}</h2>
      <p>{{ options.showcase_subtitle | default: 'Discover the Latest Collection' }}</p>
      <a href="{{ options.showcase_button_url }}" class="luxury-button">
        {{ options.showcase_button_text | default: 'Shop Now' }}
      </a>
    </div>
  </div>

  <!-- Grid de Productos -->
  {% if options.showcase_collection != blank %}
    <div class="luxury-products-grid">
      {% assign collection = collections[options.showcase_collection] %}
      {% for product in collection.products limit: 4 %}
        <div class="luxury-product-card">
          <div class="luxury-product-image">
            <img src="{{ product.image | product_img_url: 'large' }}" alt="{{ product.title }}">
          </div>
          <div class="luxury-product-info">
            <h3 class="luxury-product-title">{{ product.title }}</h3>
            <p class="luxury-product-price">{{ product.price | money }}</p>
          </div>
        </div>
      {% endfor %}
    </div>
  {% endif %}
</div>
{% endif %} 