<div class="checkout-content">
  <div class="container">
    <!-- Breadcrumb Checkout -->
    <div class="row">
      <div class="col-12 col-md-8 mr-auto ml-auto d-flex breadcrumb-cart">
        <div class="item complete">
          <a id="cart-link" href="{{order.url}}"><span>1</span></a>
          <a id="cart-link" href="{{order.url}}">{% t "Cart" %}</a>
        </div>
        <div class="item complete">
          <a href="{{order.checkout_url}}"><span>2</span></a>
          <a href="{{order.checkout_url}}">{% t "Checkout" %}</a>
        </div>
        <div class="item complete">
          <span>3</span>
          <a href="#">{% t "Review" %}</a>
        </div>
        <div class="item active">
          <span>4</span>
          <a href="#">{% t "Success" %}</a>
        </div>
      </div>
    </div><!-- /.row -->
    <div class="row checkout-success mb-4">
      <div class="col-12">
        <h1 class="page-header">{% t "Thank you for your order" %}!</h1>
        <h5 class="subtitle">{% t "This is your order number" %}: <strong>#{{order.id}}</strong></h5>
      </div>
    </div>
    <div class="row checkout-success">
      <div class="col-12 col-md-8">
        <div class="row review-order-info checkout-container">
          <div class="col-12">
            {% if order.shipping_required %}
            <div class="info mb-5">
              <h2 class="mt-1">{% t "Order will be Shipped to" %}</h2>
              <p>{{order.shipping_address.formatted}}</p>
            </div>
            {% endif %}
            <div class="info mb-5">
              <h2>{% t "Information for Payment" %}</h2>
              <p>{{ order.payment_method }}<br />{{ order.payment_information}}</p>
            </div>

            <div class="aditional-info mb-5">
              <p>{% t "An email with your order summary has been sent to" %}: <strong>{{order.email}}</strong></p>
              <p>{% t "Bought in:" %} <b>{{store.name}}</b> - <b>{{store.url}}</b></p>
              <p>{% t "If you have any question or special requirement about reimbursements or shipping please contact us at the email:" %} <b><a href="mailto:{{ store.email }}">{{ store.email }}</a></b>.</p>
            </div>

            <div id="send-to-messenger" class="fb-send-to-messenger" data-ref={{order.id}}></div>
          </div>
        </div>
      </div>

      <div class="col-12 col-md-4">
        <div class="card summary mb-3">
          <div class="card-header">
            <h2 class="card-title">{% t "Order Summary" %}</h2>
          </div>
          <div class="card-body">
            <!-- Review Order Table -->
            <form id="cart-update-form" method="post" action="{{order.update_url}}">
              <div class="table-responsive">
                <table class="table">
                  {% for ordered_product in order.products %}
                  <tr>
                    <td>
                      <a href="{{ ordered_product.url }}" class="trsn" title="{{ordered_product.name | escape}}">
                        {% if ordered_product.image %}
                        <img src="{{ ordered_product.image | resize:'120x150'}}" alt="{{ordered_product.name | escape}}" title="{{ordered_product.name | escape}}">
                        {% else %}
                        <img src="{{ 'no-image-cart.jpg' | asset }}" alt="{{ordered_product.name | escape}}">
                        {% endif %}
                      </a>
                    </td>
                    <td colspan="2">
                      <h3>{{ordered_product.name}}</h3>
                      {% for option in ordered_product.options %}
                      <strong>{{option.name}}:</strong> {{option.value}}<br>
                      {% endfor %}
                      <div class="price mt-3">
                        {% if ordered_product.discount > 0 %}
                        <div class="cart-product-discount">
                          {{ordered_product.qty}} x <span class="order-product-price-amount">
                            {{ ordered_product.price | minus:ordered_product.discount | price }}
                          </span>
                          <small class="order-product-price-amount">({{ ordered_product.price | price }})</small>
                        </div>
                        {% else %}
                        {{ordered_product.qty}} x <span class="order-product-price-amount">{{ ordered_product.price | price }}</span>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </table>
              </div>
            </form>
            <div id="review-cart-totals" class="cart-totals">
              <table class="table">
                {% if order.subtotal != order.total %}
                <tr class="totals">
                  <td colspan="1" class="text-left">{% t "Subtotal" %}</td>
                  <td colspan="1" class="text-right"><span class="order-product-price-amount">{{order.subtotal | price }}</span></td>
                </tr>
                {% endif %}

                {% if order.shipping_required %}
                <tr class="totals">
                  <td colspan="1" class="text-left">{% t "Shipping" %}</td>
                  <td colspan="1" class="text-right"><span class="order-product-price-amount">{{order.shipping | price }}</span></td>
                </tr>
                {% endif %}

                {% if order.tax > 0 and store.tax_on_product_price != true %}
                <tr class="totals">
                  <td colspan="1" class="text-left">{% t "Tax" %}</td>
                  <td colspan="1" class="text-right"><span class="order-product-price-amount">{{order.tax | price }}</span></td>
                </tr>
                {% endif %}

                {% if order.discount > 0 %}
                <tr class="totals">
                  <td colspan="1" class="text-left">{% t "Discount" %}</td>
                  <td colspan="1" class="text-right">-<span class="order-product-price-amount">{{order.discount | price }}</span></td>
                </tr>
                {% endif %}

                <tr class="totals key">
                  <td colspan="1" class="text-left">
                    <strong>{% t "Total" %}</strong>
                  </td>
                  <td colspan="1" class="text-right">
                    <strong><span class="order-product-price-amount">{{order.total | price }}</span></strong>
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>
      {% if customer == nil and store.customers_enabled and store.customers_optional %}
      <div class="col-12 col-md-8 mt-5">
        <div class="card mb-3">
          <div class="card-header">
            <h2 class="card-title">{% t "Create your Customer Account" %}</h2>
          </div>
          <div class="card-body">
            {{ customer_reset_password_form }}
          </div>
        </div>
      </div>
      {% endif %}

    </div><!-- /.row -->

  </div>
</div>
<script>
  $(document).ready(function() {
    $('form#create_password #submit_password').val("{% t 'Create Password' %}");
  });
</script>
