<script src="//ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js"></script>
<script type="text/javascript">
  WebFont.load({
    google: {
      families: ["{{options.general_font}}:300,400,500,600,700,800", "{{options.titles_font}}:300,400,500,600,700,800", "{{options.store_name_font}}:300,400,500,600,700,800"]
    }
  });
</script>

<style>
  body, .lateral-header-btn span, button#cart-btn span, .ig_account {
     font-family: '{{ options.general_font }}' !important;
  }
  .page-header, h2,  .banner-info h4, .banner-info a, .blog-post h3, .blog-post a.btn, .top-footer h3,
  #footer-features h4, .sidebar .card h4,  .breadcrumb-cart span,
  #customer_details h4.title, #customer_address h2, .text-formatted h1, .cart-actions .list-inline a{
   font-family: '{{ options.titles_font }}' !important;
  }
  .price-mob,.product-form-price{
  	font-family: '{{ options.price_font }}' !important;
  }
  .navbar-brand, .text-logo {
   font-family: '{{ options.store_name_font }}' !important;
  }

   p, .caption h4, label, table, .panel, #contactpage > h2.success, #contactpage h2.error, .top-footer a, 	.ig_account, .text-formatted{
    font-size: {{ options.general_font_size }} !important;
  }

  h2, h2.page-header {
    font-size: {{ options.title_font_size }} !important;
  }
  .navbar-brand, .text-logo {
    font-size: {{ options.store_name_font_size }} !important;
  }
  .navbar a, #navbarMobile .nav-item a{
    font-size: {{ options.menu_font_size }} !important;
  }
  .btn-primary, .btn, .btn.primary{
  	 font-family: '{{ options.titles_font }}' !important;
	}


</style>
