# Guía Técnica: Configuración de `options.json` en Jumpseller

## Introducción

Este documento resume las mejores prácticas, tipos válidos y soluciones a problemas comunes encontrados al configurar el archivo `Config/options.json` para temas de Jumpseller. El objetivo es proporcionar una guía técnica robusta y expandible basada en la experiencia práctica durante el desarrollo de la sección "Galería SUCLA Personalizada".

## Estructura General de `options.json`

El archivo `options.json` define las opciones personalizables que aparecen en el editor visual de temas de Jumpseller. Su estructura principal es un objeto JSON donde cada clave de primer nivel representa un **grupo de opciones**.

```json
{
  "Nombre del Grupo 1": {
    "icon": "nombre-icono-fontawesome", // Opcional: Ícono para el grupo
    "options": {
      "id_opcion_1": {
        "name": "Nombre Visible Opción 1", // Usar 'name', no 'label'
        "type": "tipo_valido",
        "default": "valor_por_defecto", // Opcional, pero recomendado
        "info": "Texto de ayuda adicional", // Opcional
        // ... otras propiedades según el tipo (ej: options para select)
      },
      "id_opcion_2": { ... }
      // ... más opciones
    }
  },
  "Nombre del Grupo 2": { ... }
  // ... más grupos
}
```

## Tipos de Campo Válidos (`type`)

Durante el desarrollo, hemos confirmado experimentalmente y a través de mensajes de error de Jumpseller la validez de los siguientes tipos de campo. Es crucial usar **exactamente** estos strings:

*   **Texto:**
    *   `text`: Campo de texto simple o multilínea (¡Usar `text`, **NO** `textarea`!).
    *   `input`: Campo de texto de una sola línea.
    *   `html`: Editor de texto enriquecido (WYSIWYG).
*   **Selección:**
    *   `checkbox`: Casilla de verificación (booleano).
    *   `select`: Menú desplegable. Requiere una propiedad `options` con un array de objetos `{"label": "Visible", "value": "guardado"}`. **Tiene un límite estricto de 100 opciones.**
    *   `slider`: Selector de rango numérico. Requiere propiedades `min`, `max`, `step`. (¡Usar `slider`, **NO** `range`!).
    *   `color`: Selector de color. **Requiere un `default` válido (puede ser `""` para "ninguno").**
    *   `google_font`: Selector de fuentes de Google Fonts.
*   **Archivos e Imágenes:**
    *   `file`: Selector de archivos genérico.
    *   `image`: Selector de imágenes específico. **En los templates Liquid, se debe acceder a la URL de la imagen usando `.url` (ej: `{{ options.mi_imagen.url }}`).**
*   **Contenido de Jumpseller:**
    *   `category`: Selector de categoría de producto.
    *   `category_list`: Selector múltiple de categorías de producto.
    *   `collection`: (Alias obsoleto o alternativo para `category`? Se usó en el código original, verificar su validez exacta si es necesario, preferir `category`).
    *   `link`: Selector de enlaces internos/externos (¡Usar `link`, **NO** `url`!).
    *   `page`: Selector de página estática.
    *   `page_list`: Selector múltiple de páginas estáticas.
    *   `page_category`: Selector de categoría de página (Blog).
    *   `page_category_list`: Selector múltiple de categorías de página.
    *   `product`: Selector de producto.
    *   `product_list`: Selector múltiple de productos.
    *   `partners`: (Propósito específico, investigar si es necesario).
    *   `pack`: (Relacionado con paquetes de productos, investigar si es necesario).
    *   `bundle`: (Relacionado con bundles, investigar si es necesario).
*   **Iconos:**
    *   `icon`: Selector visual de iconos (usualmente Font Awesome por defecto). **Alternativa preferida a `select` para listas largas de iconos.**
    *   `fa_icon`: Específicamente para Font Awesome (puede ser intercambiable con `icon`).
    *   `ph_icon`: Específicamente para Phosphor Icons.
*   **Estructura:**
    *   `heading`: Añade un título/separador dentro del grupo de opciones.
*   **Video:**
    *   `video`: Campo para enlaces de video (ej. YouTube, Vimeo).

**Importante:** El tipo `repeatable` **NO es soportado** por Jumpseller en `options.json`.

## Manejo de Listas / Bloques Repetidos (Simulación)

Dado que `repeatable` no es un tipo válido, la forma estándar de manejar configuraciones donde el usuario necesita añadir múltiples elementos (como imágenes de un slider, categorías destacadas, bloques de contenido, etc.) es **simularlo usando campos individuales numerados**.

**Método:**

1.  Define un número máximo de items que permitirás (ej. 6 categorías, 3 bloques).
2.  Crea un conjunto de campos de opciones para cada item, usando un prefijo y un número.

**Ejemplo (para 3 Bloques de Contenido):**

```json
"options": {
  // ... otras opciones ...
  "content_blocks_enabled": {"type": "checkbox", "name": "Mostrar Bloques Contenido", "default": false},
  "content_blocks_title": {"type": "text", "name": "Título Sección Bloques (Opcional)"},
  // ... otras opciones generales de la sección ...

  // --- INICIO: Campos Numerados para Bloques de Contenido ---
  "block_1_image": {"type": "image", "name": "Bloque 1 - Imagen"},
  "block_1_title": {"type": "text", "name": "Bloque 1 - Título"},
  "block_1_text": {"type": "text", "name": "Bloque 1 - Texto"},
  "block_1_link": {"type": "link", "name": "Bloque 1 - Enlace (Opcional)"},
  "block_1_link_text": {"type": "text", "name": "Bloque 1 - Texto Enlace", "default": "Leer más"},

  "block_2_image": {"type": "image", "name": "Bloque 2 - Imagen"},
  "block_2_title": {"type": "text", "name": "Bloque 2 - Título"},
  "block_2_text": {"type": "text", "name": "Bloque 2 - Texto"},
  "block_2_link": {"type": "link", "name": "Bloque 2 - Enlace (Opcional)"},
  "block_2_link_text": {"type": "text", "name": "Bloque 2 - Texto Enlace", "default": "Leer más"},

  "block_3_image": {"type": "image", "name": "Bloque 3 - Imagen"},
  "block_3_title": {"type": "text", "name": "Bloque 3 - Título"},
  "block_3_text": {"type": "text", "name": "Bloque 3 - Texto"},
  "block_3_link": {"type": "link", "name": "Bloque 3 - Enlace (Opcional)"},
  "block_3_link_text": {"type": "text", "name": "Bloque 3 - Texto Enlace", "default": "Leer más"},
  // --- FIN: Campos Numerados para Bloques de Contenido ---
  // ... resto de opciones ...
}
```

**Ventajas:**
*   Compatible con Jumpseller.
*   Funciona y permite la configuración deseada.

**Desventajas:**
*   Menos elegante en `options.json`.
*   El editor de temas muestra muchos campos.
*   Requiere lógica Liquid específica (bucles `for i in (1..N)`) para leer los datos.

## Errores Comunes y Soluciones

*   **`[id_opcion]: type: Expected one of: ...`**:
    *   **Causa:** Se está usando un `type` inválido o no reconocido por Jumpseller para esa opción.
    *   **Solución:** Reemplazar el valor de `type` por uno de la lista de tipos válidos confirmados. Errores específicos encontrados:
        *   Usar `range` -> Cambiar a `slider`.
        *   Usar `textarea` -> Cambiar a `text`.
        *   Usar `url` -> Cambiar a `link`.
        *   Usar `repeatable` -> Reemplazar con campos numerados individuales (ver sección anterior).
        *   Usar `label` en lugar de `name` para la opción principal.
*   **`[id_opcion]: select options: Too large (X, range 1..100)`**:
    *   **Causa:** El array `options` definido para un campo `type: "select"` tiene más de 100 elementos.
    *   **Solución:**
        1.  Reducir el número de opciones en el array a 100 o menos.
        2.  Si aplica (ej. selección de iconos), cambiar el `type` a uno más adecuado como `icon` o `ph_icon`, que proporciona un selector visual sin límite de lista.
*   **`[id_opcion]: default: Expected a color string`**:
    *   **Causa:** Un campo `type: "color"` no tiene un valor `default` o el valor no es una cadena de color válida.
    *   **Solución:** Añadir `"default": ""` o `"default": "#codigohex"` a la definición del campo de color. Un string vacío (`""`) es válido para indicar "sin color por defecto".
*   **Errores genéricos de JSON al guardar:**
    *   **Causa:** Error de sintaxis JSON (comas extra al final de un elemento, comas faltantes entre elementos, llaves `{}` o corchetes `[]` no balanceados).
    *   **Solución:** Validar cuidadosamente la sintaxis JSON antes de intentar guardar. Usar un validador JSON online o las herramientas del editor de código puede ayudar.

## Buenas Prácticas y Consejos

*   **Validar JSON:** Siempre valida la sintaxis del archivo `options.json` antes de intentar guardarlo en Jumpseller, especialmente después de hacer cambios manuales.
*   **Usar `name`:** Utiliza la propiedad `"name"` para el texto visible de la opción en el editor de temas. Evita usar `"label"` para este propósito.
*   **Tipos Específicos:** Prefiere tipos específicos (`icon`, `link`, `category`) sobre tipos genéricos (`select`, `input`) cuando sea posible. Mejora la experiencia de usuario y puede evitar límites (como el de `select`).
*   **Defaults Sensatos:** Proporciona valores `default` razonables para la mayoría de las opciones. Para campos opcionales (como colores), `""` puede ser un default aceptable.
*   **Nomenclatura Consistente:** Usa un esquema de nombrado claro y consistente para los IDs de las opciones, especialmente para los campos numerados (ej. `prefijo_numero_propiedad`).
*   **Comentarios:** Aunque los comentarios no son parte del JSON estándar, puedes añadirlos temporalmente en tu editor para explicar secciones complejas (y quitarlos antes de subir si causan problemas, aunque Jumpseller a veces los tolera).
*   **Documentación `info`:** Usa la propiedad `"info"` para añadir descripciones o ayudas útiles directamente en el editor de temas.
*   **Pruebas Incrementales:** Guarda y prueba los cambios en Jumpseller de forma incremental, especialmente al añadir nuevas secciones o tipos de campo complejos, para identificar errores más fácilmente.

## Ejemplo Práctico: "Galería SUCLA Personalizada" (Estructura Final)

```json
    "Galeria SUCLA Personalizada": {
      "icon": "image",
      "options": {
        // --- Opciones Generales ---
        "general_max_width": {"type": "text", "name": "Ancho Máximo Contenido (ej: 1660px)", "default": "1660px"},
        "section_padding_y": {"type": "text", "name": "Padding Vertical Secciones (ej: 3rem)", "default": "3rem"},
        "section_padding_x": {"type": "text", "name": "Padding Horizontal Secciones (ej: 1rem)", "default": "1rem"},
        "grid_gap": {"type": "text", "name": "Espacio en Grids/Carruseles (ej: 1.5rem)", "default": "1.5rem"},
        "border_radius": {"type": "text", "name": "Radio de Borde General (ej: 4px)", "default": "4px"},

        // --- Hero Banner ---
        "hero_enabled": {"type": "checkbox", "name": "Mostrar Hero Banner", "default": true},
        "hero_image": {"type": "image", "name": "Imagen de Fondo"},
        "hero_image_position": {"type": "select", "name": "Posición Imagen Fondo", "options": [{"label":"Centro","value":"center"}, {"label":"Arriba","value":"top"}, {"label":"Abajo","value":"bottom"}, {"label":"Izquierda","value":"left"}, {"label":"Derecha","value":"right"}], "default": "center"},
        "hero_alt_text": {"type": "text", "name": "Texto Alternativo Imagen"},
        "hero_min_height_vh": {"type": "text", "name": "Altura Mínima (%) Pantalla (vh)", "default": "65vh", "info": "Altura relativa al alto de la pantalla. Ej: 65vh"},
        "hero_min_height_vh_mobile": {"type": "text", "name": "Altura Mínima Móvil (%) Pantalla (vh)", "default": "55vh"},
        "hero_overlay_color": {"type": "color", "name": "Color Superposición (Overlay)", "default": "rgba(0, 0, 0, 0.35)"},
        "hero_text_color": {"type": "color", "name": "Color Texto General (Hero)", "default": "#FFFFFF"},
        "hero_title": {"type": "text", "name": "Título Principal", "default": "Título Impactante Aquí"},
        "hero_title_color": {"type": "color", "name": "Color Título (Opcional)", "info": "Si vacío, usa Color Texto General", "default": ""},
        "hero_title_weight": {"type": "text", "name": "Grosor Fuente Título (100-900)", "default": "700"},
        "hero_subtitle": {"type": "text", "name": "Subtítulo", "default": "Describe tu propuesta de valor o promoción."},
        "hero_subtitle_color": {"type": "color", "name": "Color Subtítulo (Opcional)", "default": ""},
        "hero_cta_text": {"type": "text", "name": "Texto Botón (CTA)", "default": "Comprar Ahora"},
        "hero_cta_link": {"type": "link", "name": "Enlace Botón (CTA)"},
        "hero_cta_bg_color": {"type": "color", "name": "Color Fondo Botón", "default": "#007bff"},
        "hero_cta_text_color": {"type": "color", "name": "Color Texto Botón", "default": "#FFFFFF"},
        "hero_cta_bg_hover_color": {"type": "color", "name": "Color Fondo Botón (Hover)", "default": "#0056b3"},

        // --- Categorías Destacadas ---
        "featured_cats_enabled": {"type": "checkbox", "name": "Mostrar Categorías Destacadas", "default": true},
        "featured_cats_title": {"type": "text", "name": "Título Sección Categorías", "default": "Compra por Categoría"},
        "featured_cats_layout": {"type": "select", "name": "Diseño Categorías", "options": [{"value": "grid", "label": "Grid"}, {"value": "carousel", "label": "Carrusel"}], "default": "grid"},
        "featured_cats_min_carousel": {"type": "slider", "name": "Mínimo items para Carrusel Cat.", "min": 2, "max": 6, "step": 1, "default": 3, "info": "Si hay menos items, se mostrará en grid."},
        "featured_cats_loop": {"type": "checkbox", "name": "Loop Infinito Categorías (Carrusel)", "default": false},
        "category_aspect_ratio": {"type": "text", "name": "Ratio Imagen Categoría (ej: 4 / 3)", "default": "4 / 3"},
        "featured_cats_slides_large": {"type": "slider", "name": "# Slides Cat. Escritorio Grande", "min": 2, "max": 6, "step": 1, "default": 5},
        "featured_cats_slides_desktop": {"type": "slider", "name": "# Slides Cat. Escritorio", "min": 2, "max": 6, "step": 1, "default": 4},
        "featured_cats_slides_tablet": {"type": "slider", "name": "# Slides Cat. Tablet", "min": 2, "max": 5, "step": 1, "default": 3},
        "featured_cats_slides_mobile": {"type": "slider", "name": "# Slides Cat. Móvil", "min": 1, "max": 3, "step": 1, "default": 2},
        "featured_cats_spacing": {"type": "slider", "name": "Espacio Slides Cat. (px, Carrusel)", "min": 5, "max": 40, "step": 1, "default": 15},
        "category_1_image": {"type": "image", "name": "Categoría 1 - Imagen"},
        "category_1_title": {"type": "text", "name": "Categoría 1 - Título"},
        "category_1_link": {"type": "link", "name": "Categoría 1 - Enlace"},
        "category_2_image": {"type": "image", "name": "Categoría 2 - Imagen"},
        "category_2_title": {"type": "text", "name": "Categoría 2 - Título"},
        "category_2_link": {"type": "link", "name": "Categoría 2 - Enlace"},
        "category_3_image": {"type": "image", "name": "Categoría 3 - Imagen"},
        "category_3_title": {"type": "text", "name": "Categoría 3 - Título"},
        "category_3_link": {"type": "link", "name": "Categoría 3 - Enlace"},
        "category_4_image": {"type": "image", "name": "Categoría 4 - Imagen"},
        "category_4_title": {"type": "text", "name": "Categoría 4 - Título"},
        "category_4_link": {"type": "link", "name": "Categoría 4 - Enlace"},
        "category_5_image": {"type": "image", "name": "Categoría 5 - Imagen"},
        "category_5_title": {"type": "text", "name": "Categoría 5 - Título"},
        "category_5_link": {"type": "link", "name": "Categoría 5 - Enlace"},
        "category_6_image": {"type": "image", "name": "Categoría 6 - Imagen"},
        "category_6_title": {"type": "text", "name": "Categoría 6 - Título"},
        "category_6_link": {"type": "link", "name": "Categoría 6 - Enlace"},

        // --- Carrusel Productos ---
        "product_carousel_enabled": {"type": "checkbox", "name": "Mostrar Carrusel Productos", "default": true},
        "product_carousel_title": {"type": "text", "name": "Título Sección Productos", "default": "Novedades"},
        "product_carousel_collection": {"type": "category", "name": "Colección a Mostrar Productos"}, // Cambiado a 'category'
        "product_carousel_limit": {"type": "slider", "name": "Máximo Productos", "min": 3, "max": 24, "step": 1, "default": 8},
        "product_carousel_loop": {"type": "checkbox", "name": "Loop Infinito Productos", "default": false},
        "product_card_aspect_ratio": {"type": "text", "name": "Ratio Imagen Producto (ej: 1 / 1)", "default": "1 / 1", "info": "Afecta las cards en este carrusel."},
        "product_carousel_show_view_all_button": {"type": "checkbox", "name": "Mostrar Botón 'Ver Todo' Productos", "default": true},
        "product_carousel_view_all_link": {"type": "link", "name": "Enlace 'Ver Todo' Productos (Opcional)", "info": "Si vacío, usa enlace de colección."},
        "product_carousel_view_all_text": {"type": "text", "name": "Texto Botón 'Ver Todo' Productos", "default": "Ver toda la colección"},
        "product_carousel_bg_color": {"type": "color", "name": "Color Fondo Sección Productos", "default": "#f9f9f9"},
        "product_carousel_slides_large": {"type": "slider", "name": "# Slides Prod. Escritorio Grande", "min": 3, "max": 8, "step": 1, "default": 6},
        "product_carousel_slides_desktop": {"type": "slider", "name": "# Slides Prod. Escritorio", "min": 3, "max": 7, "step": 1, "default": 5},
        "product_carousel_slides_tablet": {"type": "slider", "name": "# Slides Prod. Tablet", "min": 2, "max": 5, "step": 1, "default": 4},
        "product_carousel_slides_sm_tablet": {"type": "slider", "name": "# Slides Prod. Tablet Pequeña", "min": 2, "max": 4, "step": 1, "default": 3},
        "product_carousel_slides_mobile": {"type": "slider", "name": "# Slides Prod. Móvil", "min": 1, "max": 3, "step": 1, "default": 2},
        "product_carousel_spacing": {"type": "slider", "name": "Espacio lógico Slides Prod. (px)", "min": 5, "max": 40, "step": 1, "default": 15, "info": "El espacio visual se crea con padding."},

        // --- Banner Promocional ---
        "promo_banner_enabled": {"type": "checkbox", "name": "Mostrar Banner Promocional", "default": false},
        "promo_banner_image": {"type": "image", "name": "Imagen Banner Promo"},
        "promo_banner_image_position": {"type": "select", "name": "Posición Imagen Banner Promo", "options": [{"label":"Centro","value":"center"}, {"label":"Arriba","value":"top"}, {"label":"Abajo","value":"bottom"}, {"label":"Izquierda","value":"left"}, {"label":"Derecha","value":"right"}], "default": "center"},
        "promo_banner_alt_text": {"type": "text", "name": "Texto Alternativo Imagen Banner Promo"},
        "promo_banner_min_height": {"type": "text", "name": "Altura Mínima Banner Promo (ej: 400px)", "default": "400px"},
        "promo_banner_min_height_mobile": {"type": "text", "name": "Altura Mínima Móvil Banner Promo (ej: 300px)", "default": "300px"},
        "promo_banner_overlay_color": {"type": "color", "name": "Color Overlay Banner Promo", "default": "rgba(0, 0, 0, 0.45)"},
        "promo_banner_text_color": {"type": "color", "name": "Color Texto Banner Promo", "default": "#FFFFFF"},
        "promo_banner_title": {"type": "text", "name": "Título Banner Promo", "default": "Título Promocional"},
        "promo_banner_title_color": {"type": "color", "name": "Color Título Banner Promo (Opcional)", "default": ""},
        "promo_banner_title_weight": {"type": "text", "name": "Grosor Fuente Título Banner Promo", "default": "600"},
        "promo_banner_subtitle": {"type": "text", "name": "Subtítulo Banner Promo", "default": "Texto descriptivo de la promoción."},
        "promo_banner_subtitle_color": {"type": "color", "name": "Color Subtítulo Banner Promo (Opcional)", "default": ""},
        "promo_banner_cta_text": {"type": "text", "name": "Texto Botón Banner Promo", "default": "Descubrir"},
        "promo_banner_cta_link": {"type": "link", "name": "Enlace Botón Banner Promo"},
        "promo_banner_cta_bg_color": {"type": "color", "name": "Color Fondo Botón Banner Promo", "default": "transparent"},
        "promo_banner_cta_text_color": {"type": "color", "name": "Color Texto Botón Banner Promo", "default": "#FFFFFF"},
        "promo_banner_cta_border_color": {"type": "color", "name": "Color Borde Botón Banner Promo", "default": "#FFFFFF"},
        "promo_banner_cta_bg_hover_color": {"type": "color", "name": "Color Fondo Botón Banner Promo (Hover)", "default": ""},
        "promo_banner_cta_text_hover_color": {"type": "color", "name": "Color Texto Botón Banner Promo (Hover)", "default": ""},
        "promo_banner_cta_border_hover_color": {"type": "color", "name": "Color Borde Botón Banner Promo (Hover)", "default": ""},

        // --- Bloques de Contenido ---
        "content_blocks_enabled": {"type": "checkbox", "name": "Mostrar Bloques Contenido", "default": false},
        "content_blocks_title": {"type": "text", "name": "Título Sección Bloques (Opcional)"},
        "content_blocks_bg_color": {"type": "color", "name": "Color Fondo Sección Bloques", "default": "#ffffff"},
        "content_blocks_border_color": {"type": "color", "name": "Color Borde Bloques", "default": "#eeeeee"},
        "block_aspect_ratio": {"type": "text", "name": "Ratio Imagen Bloque (ej: 16 / 9)", "default": "16 / 9"},
        "block_1_image": {"type": "image", "name": "Bloque 1 - Imagen"},
        "block_1_title": {"type": "text", "name": "Bloque 1 - Título"},
        "block_1_text": {"type": "text", "name": "Bloque 1 - Texto"},
        "block_1_link": {"type": "link", "name": "Bloque 1 - Enlace (Opcional)"},
        "block_1_link_text": {"type": "text", "name": "Bloque 1 - Texto Enlace", "default": "Leer más"},
        "block_2_image": {"type": "image", "name": "Bloque 2 - Imagen"},
        "block_2_title": {"type": "text", "name": "Bloque 2 - Título"},
        "block_2_text": {"type": "text", "name": "Bloque 2 - Texto"},
        "block_2_link": {"type": "link", "name": "Bloque 2 - Enlace (Opcional)"},
        "block_2_link_text": {"type": "text", "name": "Bloque 2 - Texto Enlace", "default": "Leer más"},
        "block_3_image": {"type": "image", "name": "Bloque 3 - Imagen"},
        "block_3_title": {"type": "text", "name": "Bloque 3 - Título"},
        "block_3_text": {"type": "text", "name": "Bloque 3 - Texto"},
        "block_3_link": {"type": "link", "name": "Bloque 3 - Enlace (Opcional)"},
        "block_3_link_text": {"type": "text", "name": "Bloque 3 - Texto Enlace", "default": "Leer más"},

        // --- Instagram Feed ---
        "instagram_enabled": {"type": "checkbox", "name": "Mostrar Feed Instagram", "default": false},
        "instagram_title": {"type": "text", "name": "Título Sección Instagram", "default": "Síguenos en Instagram"},
        "instagram_username": {"type": "text", "name": "Usuario Instagram (sin @)"},
        "instagram_bg_color": {"type": "color", "name": "Color Fondo Sección Instagram", "default": "#fafafa"},
        "instagram_gap": {"type": "slider", "name": "Espacio entre Fotos Instagram (px)", "min": 0, "max": 20, "step": 1, "default": 8},
        "instagram_image_1": {"type": "image", "name": "Instagram Imagen 1"},
        "instagram_image_2": {"type": "image", "name": "Instagram Imagen 2"},
        "instagram_image_3": {"type": "image", "name": "Instagram Imagen 3"},
        "instagram_image_4": {"type": "image", "name": "Instagram Imagen 4"},
        "instagram_image_5": {"type": "image", "name": "Instagram Imagen 5"},
        "instagram_image_6": {"type": "image", "name": "Instagram Imagen 6"},

        // --- Opciones Swiper ---
        "swiper_nav_size": {"type": "text", "name": "Tamaño Flechas Nav (px)", "default": "44px"},
        "swiper_nav_bg": {"type": "color", "name": "Fondo Flechas Nav", "default": "rgba(255, 255, 255, 0.9)"},
        "swiper_nav_color": {"type": "color", "name": "Color Icono Flechas", "default": "#333333"},
        "swiper_nav_bg_hover": {"type": "color", "name": "Fondo Flechas (Hover)", "default": ""},
        "swiper_nav_icon_size": {"type": "text", "name": "Tamaño Icono Flechas (px)", "default": "18px"},
        "swiper_pag_bullet_size": {"type": "text", "name": "Tamaño Puntos Pag (px)", "default": "10px"},
        "swiper_pag_bullet_color": {"type": "color", "name": "Color Puntos Pag Inactivos", "default": "#333333"},
        "swiper_pag_bullet_active_color": {"type": "color", "name": "Color Punto Activo", "info": "Si vacío, usa color primario", "default": ""}
      }
    }
```

