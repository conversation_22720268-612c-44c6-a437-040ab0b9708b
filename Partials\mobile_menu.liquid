{% comment %}
  Itera sobre los elementos principales del menú.
  Para cada elemento principal, usa el parcial 'navigation_mobile_menu.liquid'
  que se encargará de mostrar ese elemento y sus posibles submenús (hijos).
{% endcomment %}
<ul class="navbar-nav">
  {% for item in menu.main.items %}
    {% include 'navigation_mobile_menu' with item %}
  {% else %}
    <li class="nav-item"><span class="nav-link">{% t "El menú principal no tiene elementos." %}</span></li>
  {% endfor %}
</ul>

{% comment %} Sección de utilidades: Login/Cuenta {% endcomment %}
{% if store.customers_enabled %}
  <ul class="navbar-nav utility-nav">
    <li class="nav-item">
      <a href="{% if customer %}{{customer_account_url}}{% else %}{{customer_login_url}}{% endif %}" class="nav-link trsn">
        <span class="customer-name">
          {% if customer %}{{ customer.name }}{% else %}{% t "Login" %}{% endif %}
        </span>
      </a>
    </li>
    {% if customer %}
      <li class="nav-item">
        <a title="{% t 'Logout' %}" href="{{customer.logout_url}}" class="nav-link trsn">
          <span>{% t 'Logout' %}</span>
        </a>
      </li>
    {% endif %}
  </ul>
{% endif %}

{% comment %}
  Opcional: Aquí podrías añadir selectores de idioma y moneda si los necesitas en el menú móvil.
  De forma similar a como están en el backup/Diseño.liquid (líneas 206-240 aprox).
  También podrías añadir los iconos de redes sociales.
  Por ejemplo:
{% endcomment %}

{% comment %}
{% if languages.size > 1 %}
  <ul class="navbar-nav utility-nav">
    <li class="nav-item dropdown">
      <a href="#" class="nav-link dropdown-toggle button" data-id="languages-action-mobile">
        <span>{{languages.first.name}}</span>
        <i class="linear-icon icon-0823-plus"></i>
      </a>
      <ul id="languages-action-mobile" class="sub-menu" style="display:none;">
        {% for language in languages %}
          <li><a href="{{language.url}}" class="trsn nav-link" title="{{language.name}}">{{language.name}}</a></li>
        {% endfor %}
      </ul>
    </li>
  </ul>
{% endif %}

<ul class="social list-inline social-networks">
  {% if social.facebook_url != blank %}
    <li class="list-inline-item">
      <a href="{{ social.facebook_url }}" class="trsn" title="{% t 'Go to' %} Facebook" target="_blank">
        <i class="fab fa-facebook-f"></i>
      </a>
    </li>
  {% endif %}
  {% if social.instagram_url != blank %}
    <li class="list-inline-item">
      <a href="{{ social.instagram_url }}" class="trsn" title="{% t 'Go to' %} Instagram" target="_blank">
        <i class="fab fa-instagram"></i>
      </a>
    </li>
  {% endif %}
  
</ul>
{% endcomment %}
