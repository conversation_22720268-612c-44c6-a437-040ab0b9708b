{% comment %}
============================================================
  Partial: product_card.liquid
  Descripción: Tarjeta de producto reutilizable
  Version: 1.0
============================================================
{% endcomment %}

{% comment %} Parámetros esperados:
  product: Objeto de producto de Jumpseller
  product_card_aspect_ratio: (Opcional) Relación de aspecto para las imágenes (default: options.sucla_product_card_aspect_ratio)
{% endcomment %}

{% assign product_card_aspect_ratio = product_card_aspect_ratio | default: options.sucla_product_card_aspect_ratio | default: "1/1" %}

{% assign product_url = product.url %}
{% assign product_title = product.name | escape %}
{% assign product_image = product.images.first %}
{% assign product_price = product.price | price %}
{% assign product_compare_price = product.discount | price %}

<div class="product-card">
  <a href="{{ product_url }}" class="product-card-link">
    <div class="product-card-image-wrapper">
      {% if product_image %}
        <img
          src="{{ product_image | resize: '300x300' }}"
          alt="{{ product_title }}"
          class="product-card-image"
          loading="lazy"
          width="300"
          height="300"
        >
      {% else %}
        <div class="product-card-no-image">
          <span>{% t "Sin imagen" %}</span>
        </div>
      {% endif %}
      
      {% if product.discount > 0 %}
        <div class="product-card-discount-badge">
          {% assign discount_percentage = product.discount | divided_by: product.price | times: 100 | round %}
          -{{ discount_percentage }}%
        </div>
      {% endif %}
    </div>

    <div class="product-card-content">
      <h3 class="product-card-title">{{ product_title }}</h3>
      
      <div class="product-card-prices">
        {% if product.discount > 0 %}
          <span class="product-card-compare-price">{{ product_compare_price }}</span>
        {% endif %}
        <span class="product-card-price">{{ product_price }}</span>
      </div>
      
      {% if product.stock == 0 and product.stock_unlimited == false %}
        <div class="product-card-out-of-stock">
          {% t "Agotado" %}
        </div>
      {% endif %}
    </div>
  </a>

  {% if product.stock > 0 or product.stock_unlimited %}
    <form action="{{ product.add_to_cart_url }}" method="post" class="product-card-form">
      <button type="submit" class="product-card-add-to-cart">
        {% t "Agregar al carrito" %}
      </button>
    </form>
  {% endif %}
</div>

<style>
.product-card {
  position: relative;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.product-card-link {
  text-decoration: none;
  color: inherit;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-card-image-wrapper {
  position: relative;
  padding-top: 100%;
  background: #f5f5f5;
  overflow: hidden;
}

.product-card-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.product-card:hover .product-card-image {
  transform: scale(1.05);
}

.product-card-no-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  color: #666;
  font-size: 0.9rem;
}

.product-card-discount-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #ff4444;
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 600;
}

.product-card-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-card-title {
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 0.5rem;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-card-prices {
  margin-top: auto;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.product-card-price {
  font-size: 1.1rem;
  font-weight: 600;
  color: #222;
}

.product-card-compare-price {
  font-size: 0.9rem;
  color: #999;
  text-decoration: line-through;
}

.product-card-out-of-stock {
  margin-top: 0.5rem;
  color: #ff4444;
  font-size: 0.9rem;
  font-weight: 500;
}

.product-card-form {
  padding: 0 1rem 1rem;
}

.product-card-add-to-cart {
  width: 100%;
  padding: 0.8rem;
  background: var(--hero-home-primary-color, #007bff);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.product-card-add-to-cart:hover {
  background: color-mix(in srgb, var(--hero-home-primary-color, #007bff) 85%, black);
}

@media (max-width: 767px) {
  .product-card-title {
    font-size: 0.95rem;
  }
  
  .product-card-price {
    font-size: 1rem;
  }
  
  .product-card-add-to-cart {
    padding: 0.7rem;
    font-size: 0.85rem;
  }
}
</style> 