html,
body {
  height: 100%;
  display: flex;
  flex-direction: column;
}

a:hover {
  text-decoration: none;
}

/* Transition for Hover & Focus states */
a,
a:hover,
.trsn,
.trsn:hover,
.trsn:focus,
input.text,
input.text:focus,
button.trsn:hover,
select.select,
select.select:hover {
  text-decoration: none !important;
  -webkit-transition: all 0.2s linear !important;
  -moz-transition: all 0.2s linear !important;
  -ms-transition: all 0.2s linear !important;
  -o-transition: all 0.2s linear !important;
  transition: all 0.2s linear !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #222;
}

h2 {
  font-size: 1.75em !important;
}

ol,
ul {
  list-style: none;
}

img {
  max-width: 100%;
}

.btn-primary {
  border-radius: 0;
  padding-left: 20px;
  padding-right: 20px;
}

.breadcrumb {
  margin-top: 30px;
  background: #f5f5f5;
}

.page-header {
  padding-bottom: 9px;
  margin: 20px 0 20px;
  font-weight: normal;
  font-size: 1.3em !important;
  font-family: Lato !important;
}

.page p:empty {
  min-height: 1.5em;
}

.page ol {
  list-style: decimal;
}

.page ul {
  list-style: disc;
}

.store-image {
  max-height: 100px;
}

.product-block-description {
  height: 90px;
  overflow: hidden;
}

.product-block-description p:empty {
  min-height: 1.5em;
}

.product-block .caption {
  min-height: 150px;
}

.product-block .caption.no-price {
  min-height: 76px;
}

.product-block .caption .price-mob {
  font-size: 16px;
  font-weight: 600 !important;
  color:#2C3E50 !important;
}

.no-label {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

figure img {
  width: 100%;
  height: auto;
  margin: 1em 0;
}

form span.error {
  font-size: 0.85em;
  color: #dc3545;
}

/**** Flex display ****/
.flex {
  display: flex;
}

.flex-start {
  justify-content: flex-start;
}

.flex-center {
  justify-content: center;
}

.flex-end {
  justify-content: flex-end;
}

/****  MENU  ****/
.navbar {
  border-radius: 0;
}

.navbar-header h1 {
  margin: 0;
}

#search_mini_form button {
  margin-left: 5px;
}

.fixed-top .navbar-dark {
  padding-top: 0;
  padding-bottom: 0;
}

.navbar-dark .navbar-nav .nav-link.dropdown-menu > .active > a,
.dropdown-menu > .active > a:focus,
.dropdown-menu > .active > a:hover {
  background-color: transparent;
  color: #777;
}

.navbar-brand h1 {
  margin-bottom: 0;
}

.navbar-light {
  min-height: 51px;
  padding: 0;
}

.navbar-dark {
  padding: 0;
}

#navbarsContainer-2 li.show .dropdown-menu {
  padding: 0;
}

#navbarsContainer-2 li.show .dropdown-menu a.nav-link {
  padding: 0.5rem 1rem;
}

.sfhover > ul.dropdown-menu {
  display: block;
}

.nav-link {
  padding: 1em 15px !important;
}

.navbar-dark .navbar-nav .dropdown-menu a.nav-link {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  clear: both;
  font-weight: 400;
  color: #292b2c;
  text-align: inherit;
  white-space: nowrap;
  background: 0 0;
  border: 0;
  font-size: 0.9rem;
}

.dropdown-menu li a.nav-link {
  padding: 5px 0;
}

#navbarsContainer-2 > .navbar-nav > li > a.nav-link {
  padding: 1.3em 15px !important;
}

.navbar-dark .navbar-nav .dropdown-menu a.nav-link:hover {
  color: #1d1e1f;
  text-decoration: none;
  background-color: #f7f7f9;
}

#login-link span.customer-name {
  max-width: 100px;
  overflow: hidden;
  display: block;
  float: right;
  height: 20px;
}

.nav-top {
  font-size: 13px;
}

#navbarsContainer-2 .navbar-nav {
  display: block;
}

#navbarsContainer-2 > ul.navbar-nav > li {
  display: inline-block;
  position: inherit;
  height: 55px;
}

.add-link {
  display: block;
  margin-bottom: 10px;
}

.nav-top > li > a {
  padding: 15px 10px;
}

@media (min-width: 992px) {
  .navbar-toggleable-md .navbar-nav .nav-link {
    padding-right: 1rem;
    padding-left: 1rem;
  }
}

ul.nav > li.dropdown > ul > li {
  position: relative;
}

ul.nav > li.dropdown > ul > li > .dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: -6px;
  margin-left: -1px;
  -webkit-border-radius: 0 6px 6px 6px;
  -moz-border-radius: 0 6px 6px 6px;
  border-radius: 0 6px 6px 6px;
}

ul.dropdown-menu > li > .dropdown-menu {
  left: 100%;
  top: 0;
}

ul.nav > li.dropdown > ul > li:hover > .dropdown-menu {
  display: block;
}

ul.nav > li.dropdown > ul > li > .icon::after {
  display: block;
  content: ' ';
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-left-color: #ccc;
  margin-top: 5px;
  margin-right: -10px;
}

.navbar-dark .navbar-nav > .active > a,
.navbar-dark .navbar-nav > .active > a:focus,
.navbar-dark .navbar-nav > .active > a:hover {
  background-color: rgba(0, 0, 0, 0.6);
}

.navbar-dark .dropdown-menu.multi-level {
  margin: -1px 0 0 0;
  border-radius: 0 0 0.25rem 0.25rem;
  padding: 0;
}

.navbar-dark .dropdown-menu.multi-level .dropdown > a::after {
  display: block;
  content: '';
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-left-color: #ccc;
  margin-top: 5px;
  margin-right: -10px;
}

/**  Social  **/
.social {
  margin: 0;
}

.social li a {
  color: #9d9d9d;
  font-size: 18px;
}

.social li a:hover {
  color: #fff;
}

/** Category **/
h2.category-description {
  font-size: 1em !important;
  line-height: 1.5em;
  margin-bottom: 25px;
}

/**  Product  **/
.nopaddingtop {
  padding-top: 0 !important;
}

.maxStockQty {
  border-color: red !important;
}

.maxStockAdc {
  background: #c0c0c0 !important;
  border-color: #c0c0c0 !important;
  pointer-events: none;
  cursor: default;
}

.hidden {
  display: none !important;
}

.main-product-image img {
  width: 100%;
  height: 100%;
}

.product-block-discount,
.product-form-discount {
  font-size: 12px;
  font-style: italic;
  color: #666;
  text-decoration: line-through;
}

.product-form-price {
  font-weight: bold;
  font-size: 18px;
}

.description p:empty {
  min-height: 1.5em;
}

.description iframe {
  width: 100%;
}

.description img {
  max-width: 100%;
}

.description ol {
  list-style: decimal;
}

.description ul {
  list-style: disc;
}

.carousel-control {
  color: #ccc;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.9);
}

.carousel-control.right,
.carousel-control.left {
  background: none;
}

.carousel-control:focus,
.carousel-control:hover {
  opacity: 0.3;
}

.carousel-control-next-icon,
.carousel-control-prev-icon {
  width: 30px;
  height: 30px;
}

/**  Contact  **/

ul#contact-list {
  padding: 0;
  margin-bottom: 20px;
}

#contactpage > h2.success,
#coupon_form > .success {
  color: #155724;
  background-color: #d4edda;
  padding: 0.55rem 1.25rem !important;
  margin-bottom: 1rem !important;
  margin-top: 1rem;
  border: none;
  text-align: center;
  text-transform: none;
  width: 100%;
  border-radius: 0.25rem;
}

.notice {
  color: #0c5460;
  background-color: #d1ecf1;
  padding: 0.55rem 1.25rem !important;
  margin-bottom: 1rem !important;
  margin-top: 1rem;
  border: none;
  border-radius: 0.25rem;
  text-align: center;
}

.warning {
  color: #856404;
  background-color: #fff3cd;
  padding: 0.55rem 1.25rem !important;
  margin-bottom: 1rem !important;
  margin-top: 1rem;
  border: none;
  border-radius: 0.25rem;
  text-align: center;
}

#credentials .error,
#customer_details .error,
#coupon_form .error,
#contactpage h2.error {
  color: red;
  background-color: #ffe9e9;
  padding: 0.55rem 1.25rem !important;
  margin-bottom: 1rem !important;
  margin-top: 1rem;
  border: none;
  text-align: center;
  text-transform: none;
  width: 100%;
  border-radius: 0.25rem;
}

#customer_details #contacts_email .error a {
  text-decoration: underline;
  color: #721c24;
}

.badge-danger {
  color: #dc3546;
  background-color: #fff;
}

/**  Cart  **/
#cart-update-form h3 {
  font-size: 16px;
  font-weight: bold;
}

.cart-product-discount small {
  font-style: italic;
  text-decoration: line-through;
}

/**  Checkout  **/
#payments_options ul,
#shipping_options ul {
  padding: 0;
}

#payments_options .payment_information {
  padding: 0 0 0 17px;
}

#payments_options .payment_information img {
  width: auto;
  max-width: 100%;
}

#shipping_options .shipping_information {
  padding: 0 0 0 17px;
}

#review-cart-totals h5 {
  font-weight: bold;
}

.table-qty {
  width: 85px;
}

@media (max-width: 720px) {
  .table-qty {
    width: 95px;
  }
}

#contacts h2.legend,
#shipping_address h2.legend,
#billing_address h2.legend,
#other h2.legend {
  margin-bottom: 15px;
}

.checkout-container .checkbox input[type='checkbox'],
.checkout-container .checkbox-inline input[type='checkbox'],
.checkout-container .radio input[type='radio'],
.checkout-container .radio-inline input[type='radio'] {
  margin-left: 0;
  margin-top: 3px;
}

.checkout-container #contacts {
  margin-bottom: 25px;
  margin-top: 15px;
}

.checkout-container #other {
  margin-top: 30px;
}

.checkbox label {
  display: inline;
}

#checkout #contacts_accepts_marketing {
  padding-top: 5px !important;
}

.loader {
  border: 4px solid #ccc;
  border-top: 4px solid #999;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  margin: 7px auto 0;
  left: 0;
  right: 0;
  animation: spin 2s linear infinite;
  position: absolute;
}

h2.card-title {
  font-size: 20px !important;
  margin-bottom: 0;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.alert-box.alert {
  background: none;
  border: none;
  margin-bottom: 0;
  padding: 3px 0 0 1px;
  font-size: 11px;
  color: #f00;
}

.badge {
  font-size: 100%;
}

.badge a {
  color: #fff;
  text-decoration: underline !important;
}

#checkout .form-group {
  min-height: 80px;
}

/**  Customer  **/
#credentials {
  overflow: auto;
}

#credentials .label-danger {
  margin: 10px 0 0 0;
  float: left;
  padding: 5px 10px;
}

.billing_address,
.shipping_address {
  padding: 15px 0;
  border-top: solid 1px #ddd;
}

a.product_digital {
  font-size: 12px;
}

#customer-order-table .table > tbody > tr > td {
  padding: 20px 8px;
}

@media (max-width: 640px) {
  .navbar-left {
    float: left !important;
  }

  .navbar-right {
    margin-right: -15px;
  }

  .navbar-right ~ .navbar-right {
    margin-right: 0;
  }
}

@media only screen and (max-width: 991px) {
  /****  MENU  ****/
  .navbar-form {
    margin: 0;
    border-top: none;
  }

  #login-link span.customer-name {
    width: 100%;
    display: inline;
    float: none;
  }

  #social {
    margin: 0;
  }

  #social li {
    background-color: #fff;
    padding: 5px 5px;
    margin: 0 0 10px 0;
  }

  #social li a {
    color: #777;
  }

  #social li a:hover {
    color: #333;
  }

  #navbar-collapse-1 {
    overflow-y: auto !important;
    max-height: 350px !important;
    overflow-x: hidden !important;
  }

  #navbar-collapse-1 > ul.nav.navbar-nav.navbar-right > li.dropdown.open > ul > li.dropdown > ul,
  #navbar-collapse-1 > ul.nav.navbar-nav.navbar-right > li.dropdown.open > ul > li.dropdown > ul > li > ul {
    display: block;
    padding: 0 0 0 10px;
  }

  #navbarContainerMobile li.show ul.dropdown-menu {
    display: block;
  }

  #navbarContainerMobile li ul.dropdown-menu {
    background: none;
    border: 0;
    padding: 0;
    font-size: 1em;
  }

  #navbarContainerMobile .dropdown-menu li.nav-item {
    padding-left: 15px;
  }

  .checkout-container #contacts {
    margin-bottom: 50px;
    margin-top: 0;
  }
}

@media (max-width: 991px) {
  .navbar-header {
    float: none;
  }

  .navbar-toggler {
    display: block;
    margin-top: 0;
    position: absolute;
    right: 1rem;
    top: 10px;
    padding: 0.2rem 0.5rem;
  }

  .navbar-collapse {
    border-top: 1px solid transparent;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  #navbarContainer {
    padding: 0 1rem 1rem 1rem;
    overflow-y: auto !important;
    max-height: 350px !important;
    overflow-x: hidden !important;
  }

  .navbar-nav {
    float: none !important;
  }

  .navbar-nav > li {
    float: none;
  }

  .navbar-nav > li > a {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .navbar-text {
    float: none;
    margin: 15px 0;
  }

  /* since 3.1.0 */
  .navbar-collapse.collapse.in {
    display: block !important;
  }

  .collapsing {
    overflow: hidden !important;
  }

  ul.social.navbar-right {
    float: left !important;
    padding-bottom: 15px;
  }

  #search_mini_form input {
    min-height: 40px;
  }

  .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
  }

  #navbar-collapse-1 li.dropdown > a::after {
    content: '\f0d7';
    font-family: 'Font Awesome 5 Free';
    font-style: normal;
    font-weight: 900;
    text-decoration: inherit;
    padding-left: 0.5em;
  }

  #navbar-collapse-1 > ul.nav.navbar-nav.navbar-right > li.dropdown.open > ul > li.dropdown > a::after,
  #navbar-collapse-1 > ul.nav.navbar-nav.navbar-right > li.dropdown.open > ul > li.dropdown > ul > li > a::after {
    display: none;
  }

  #navbar-collapse-1 .caret {
    display: none;
  }
}

@media (max-width: 768px) {
  #whatsapp {
    display: inline-block;
  }

  #product-sharing i.fab {
    font-size: 2em;
  }

  .product-block .caption {
    min-height: 90px;
  }
}

@media (max-width: 576px) {
  #cart-update-form .mob-hide {
    display: none;
  }
}

@media (max-width: 481px) {
  .price-mob {
    float: none;
    margin-bottom: 20px;
  }
}

/** Slider **/
#carousel-home .item .layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

#carousel-home .owl-nav button {
  color: #fff !important;
  opacity: 0.5;
  font-size: 3em !important;
  z-index: 2;
  position: absolute;
  top: 50%;
  margin-top: -25px;
}

#carousel-home .owl-nav button.owl-prev {
  left: 15px;
}

#carousel-home .owl-nav button.owl-next {
  right: 15px;
}

#carousel-home .owl-nav button:hover {
  opacity: 1;
}

#carousel-home .item.item-background {
  height: 550px;
  background-size: cover;
  background-position: center;
}

#carousel-home .item .carousel-info {
  width: 550px;
  height: 100%;
  margin: 0 0 0 -275px;
  text-align: center;
  display: table;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 50%;
  z-index: 2;
}

#carousel-home .item .carousel-info a.carousel-button {
  display: block;
  border: 1px solid #fff;
  color: #fff;
  text-transform: uppercase;
  text-align: center;
  height: 48px;
  line-height: 48px;
  max-width: 230px;
  margin: 0 auto;
  min-width: 200px;
}

#carousel-home .item .carousel-info a.carousel-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

#carousel-home .item .carousel-info .carousel-info-inner {
  display: table-cell;
  vertical-align: middle;
}

#carousel-home .item .carousel-info h2 {
  color: #fff;
  font-size: 3em !important;
  font-weight: 700;
  text-shadow: 0 0 4px rgba(0, 0, 0, 0.25);
}

@media (max-width: 1280px) {
  #carousel-home .item.item-background {
    height: 335px;
  }
}

@media (max-width: 767px) {
  #carousel-home .item .carousel-info {
    width: 100%;
    left: 0;
    margin: 0;
  }

  #carousel-home .item .carousel-info-inner {
    padding: 0 35px;
  }

  #carousel-home .owl-nav button {
    margin-top: -13px;
  }

  #carousel-home .item .carousel-info h2 {
    margin-bottom: 10px;
  }

  #carousel-home .item .carousel-info h2,
  #carousel-home .owl-nav button {
    font-size: 1.25em !important;
  }

  #carousel-home .item .carousel-info .btn {
    padding: 3px 10px;
    font-size: 0.875rem;
  }
}

footer .powerd-by {
  margin-top: 8px;
}

footer ul.payment {
  text-align: right;
  margin-bottom: 10px;
}

footer ul.payment li {
  display: inline-block;
  float: none;
}

footer ul.payment li span {
  display: block;
  border: 1px solid #e1e1e1;
  width: 50px;
  height: 30px;
  border-radius: 3px;
  overflow: hidden;
  background: #f5f5f5;
  position: relative;
}

footer ul.payment li span img {
  max-width: 100%;
  display: block;
  position: relative;
  z-index: 2;
}

footer ul.payment li span::before {
  content: '';
  width: 100px;
  height: 100px;
  background: #fff;
  position: absolute;
  z-index: 0;
  transform: rotate(60deg);
  top: -76px;
  left: -50px;
  border: 1px solid #eee;
}

@media (max-width: 767px) {
  footer .powerd-by {
    text-align: center;
    padding-bottom: 20px;
  }

  footer ul.payment {
    text-align: center;
    margin: 10px 0;
    padding: 0;
  }
}

/* Video responsive */
.videoWrapper {
  position: relative;
  padding-bottom: 65%;
  width: 100%;
  margin: 0 auto 30px;
}

.videoWrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
}

/* Filters */
#show_filters {
  border-width: 1px;
}

.category_menu_list .count::before {
  content: '(';
}

.category_menu_list .count::after {
  content: ')';
}

.parent-2 {
  display: none;
}

#side-filters {
  display: none;
}

@media (min-width: 768px) {
  #side-filters {
    display: block !important;
  }
}

#side-filters h4 {
  font-size: 1.1em;
  font-weight: 700;
  text-transform: uppercase;
}

#side-filters h5 {
  font-size: 1em;
  font-weight: 700;
}

#side-filters .side-filters_selected .clearAll {
  text-decoration: underline;
}

#side-filters .clearFilter {
  background: #eee;
}

#side-filters .badge {
  font-size: 75% !important;
}

#side-filters .badge a {
  color: #fff;
  opacity: 0.7;
}

#side-filters .badge a:hover {
  opacity: 1;
}

.category_menu_list .count {
  color: #aeaeae;
}

.category_menu_trigger_child.active i {
  transform: rotate(180deg);
}

#side-filters .side-filters_selected .clearFilter {
  margin-bottom: 2px;
}

#side-filters .filter_wrapper > ul {
  margin: 0;
  padding: 0;
  max-height: 220px;
  overflow: auto;
}

#side-filters .filter_wrapper > ul > li {
  margin-bottom: 5px;
}

#side-filters .filter_wrapper > ul li > ul {
  margin: 0 0 10px 5px;
  padding: 0;
  display: none;
}

#side-filters .filter_wrapper > ul li > .custom-control {
  line-height: 1.9;
}

#side-filters .category_menu_trigger_child {
  display: block;
  position: relative;
}

#side-filters .category_menu_trigger_child i {
  float: right;
  margin-right: 10px;
}

#side-filters #price-filter .price_message {
  display: none;
}

.search-form {
  position: relative;
  width: 100%;
}

.search-form input.form-control {
  margin: 0;
  padding-right: 40px;
  width: 100%;
  font-size: 1rem;
  cursor: pointer;
}

.search-form button {
  border: 0;
  background: none;
  position: absolute;
  right: 0;
  top: 0;
  height: 38px;
  width: 44px;
  padding: 0;
  margin: 0;
  font-size: 1.5em;
  z-index: 2;
}
/* Reset y estilos base */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Arial, sans-serif;
  background: #ffffff;
  color: #1a1a1a;
}

/* Manejo de imágenes mejorado */
.product-image-container {
  position: relative;
  width: 100%;
  padding-top: 125%; /* Ratio 4:5 para imágenes de producto */
  overflow: hidden;
  background: #f8f8f8;
}

.product-image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}

.product-image-container:hover img {
  transform: scale(1.05);
}

/* Estilos de producto */
.product-block {
  margin-bottom: 2rem;
  transition: transform 0.2s ease;
}

.product-block:hover {
  transform: translateY(-5px);
}

.product-block .caption {
  padding: 1rem 0;
  text-align: center;
  min-height: auto;
}

.product-block-price {
  font-size: 1.1rem;
  font-weight: 500;
  color: #1a1a1a;
}

.product-block-title {
  font-size: 0.95rem;
  margin: 0.5rem 0;
  font-weight: 400;
  color: #1a1a1a;
}

/* Navegación */
.navbar {
  background: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  padding: 1rem 0;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 500;
  color: #1a1a1a;
}

.nav-link {
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.5rem 1rem !important;
}

/* Botones */
.btn-primary {
  background: #1a1a1a;
  border: none;
  border-radius: 0;
  padding: 0.8rem 2rem;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: background-color 0.2s ease;
}

.btn-primary:hover {
  background: #333333;
}

/* Grid de productos */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  padding: 2rem 0;
}

/* Carrito y checkout */
.cart-item {
  display: grid;
  grid-template-columns: 100px 1fr auto;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #e5e5e5;
}

.cart-item-image {
  position: relative;
  padding-top: 100%;
  background: #f8f8f8;
}

.cart-item-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Responsive */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
  }
  
  .product-block-title {
    font-size: 0.85rem;
  }
  
  .cart-item {
    grid-template-columns: 80px 1fr;
  }
}

/* Animaciones y transiciones */
.trsn, 
.trsn:hover, 
.trsn:focus {
  transition: all 0.3s ease !important;
}

/* Mejoras de accesibilidad */
:focus {
  outline: none;
  box-shadow: 0 0 2px rgba(26, 26, 26, 0.2);
}

/* Loader mejorado */
.loader {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1a1a1a;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mensajes y alertas */
.alert {
  border-radius: 0;
  border: none;
  padding: 1rem;
  margin-bottom: 1rem;
}

.alert-success {
  background: #f4f9f4;
  color: #1a1a1a;
}

.alert-danger {
  background: #fff5f5;
  color: #1a1a1a;
}
/* Estilos para el mini carrito */
.cart-item {
  display: flex;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  position: relative;
  background: white;
  transition: all 0.3s ease;
}

.cart-item:hover {
  background: #f8f9fa;
}

.cart-item-image {
  width: 80px;
  height: 80px;
  margin-right: 1rem;
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cart-item-details {
  flex: 1;
}

.cart-item-name {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.cart-item-price {
  font-size: 0.85rem;
  color: #666;
}

.remove-item {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  border: none;
  background: none;
  color: #dc3545;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.remove-item:hover {
  opacity: 1;
}

/* Animaciones */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Estilos para el spinner de carga */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Estilos para el mensaje de error de stock */
.swal-stock-error {
  font-size: 1.1rem;
  line-height: 1.5;
  color: #484848;
  padding: 0.5rem;
}

.swal-error-link {
  color: #3085d6;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.swal-error-link:hover {
  color: #2B77C0;
  text-decoration: underline;
}

/* Personalización adicional del SweetAlert para errores */
.swal2-popup.swal2-modal {
  padding: 2em;
  border-radius: 12px;
}

.swal2-icon.swal2-error {
  border-color: #ff5757;
  color: #ff5757;
}

.swal2-title {
  color: #333;
  font-size: 1.5rem;
}

.swal2-confirm.swal2-styled {
  padding: 0.5em 2em;
  font-weight: 500;
  border-radius: 6px;
}
/* Estilos para la notificación estilo Michael Kors */
.mk-notification-popup {
  padding: 0 !important;
  border-radius: 0 !important;
}

.mk-cart-notification {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.mk-notification-title {
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  padding: 20px 15px;
  margin: 0;
  border-bottom: 1px solid #e5e5e5;
  letter-spacing: 1px;
}

.mk-product-details {
  display: flex;
  padding: 20px;
  gap: 20px;
  border-bottom: 1px solid #e5e5e5;
}

.mk-product-image {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
}

.mk-product-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.mk-product-info {
  flex: 1;
}

.mk-product-info h4 {
  font-size: 14px;
  margin: 0 0 10px 0;
  font-weight: 500;
}

.mk-product-info p {
  font-size: 12px;
  margin: 5px 0;
  color: #666;
}

.mk-notification-actions {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mk-view-bag,
.mk-continue-shopping {
  width: 100%;
  padding: 12px;
  border: none;
  font-size: 13px;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mk-view-bag {
  background-color: #000;
  color: white;
}

.mk-view-bag:hover {
  background-color: #333;
}

.mk-continue-shopping {
  background-color: transparent;
  border: 1px solid #000;
  color: #000;
}

.mk-continue-shopping:hover {
  background-color: #f5f5f5;
}

/* Animación de entrada */
.swal2-show {
  animation: mkSlideDown 0.3s ease-out;
}

@keyframes mkSlideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Estilos para el botón de cerrar */
.swal2-close {
  position: absolute !important;
  right: 10px !important;
  top: 10px !important;
  color: #000 !important;
}

.swal2-close:hover {
  color: #666 !important;
}

/* producto */
/* Contenedor principal del producto */
.product-page {
  padding: 40px 0;
  max-width: 1200px;
  margin: 0 auto;
}

/* Estilos para la galería de imágenes */
.main-product-image {
  position: relative;
  background: #fff;
  margin-bottom: 20px;
}

.main-product-image img {
  width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}

.product-page-thumbs {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.product-page-thumbs .thumbs {
  border: 1px solid #e5e5e5;
  display: block;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.product-page-thumbs .thumbs:hover,
.product-page-thumbs .thumbs.active {
  opacity: 1;
  border-color: #000;
}

/* Controles del carrusel */
.carousel-control-prev,
.carousel-control-next {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: all 0.3s ease;
}

.main-product-image:hover .carousel-control-prev,
.main-product-image:hover .carousel-control-next {
  opacity: 1;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  width: 20px;
  height: 20px;
}

/* Información del producto */
.product-info {
  padding-left: 40px;
}

.product-info h1.page-header {
  font-size: 24px;
  font-weight: 400;
  margin-bottom: 20px;
  color: #000;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Precio */
.product-form-price {
  font-size: 24px;
  color: #000;
  font-weight: 400;
  margin-bottom: 20px;
}

.product-form-discount {
  color: #999;
  text-decoration: line-through;
  margin-left: 10px;
  font-size: 18px;
}

/* Selectores y cantidad */
.qty-select label {
  display: block;
  text-transform: uppercase;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.qty-select select,
.qty input {
  height: 45px;
  border: 1px solid #ddd;
  border-radius: 0;
  padding: 0 15px;
  width: 100%;
  margin-bottom: 20px;
}

/* Botón de añadir al carrito */
.adc {
  background-color: #000 !important;
  border: none;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 14px;
  padding: 15px 30px;
  width: 100%;
  transition: all 0.3s ease;
}

.adc:hover {
  background-color: #333 !important;
}

/* Descripción del producto */
.text-formatted {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  margin-top: 30px;
}

/* Compartir en redes sociales */
#product-sharing {
  margin-top: 30px;
}

#product-sharing label {
  text-transform: uppercase;
  font-size: 12px;
  color: #666;
  margin-bottom: 10px;
  display: block;
}

.social-networks {
  display: flex;
  gap: 15px;
}

.social-networks a {
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  color: #000;
  transition: all 0.3s ease;
}

.social-networks a:hover {
  background: #000;
  color: #fff;
}

/* Zoom de imagen */
.zoom-img {
  cursor: zoom-in;
}

.zoom-img.zoom-active {
  cursor: zoom-out;
}

/* Responsive */
@media (max-width: 991px) {
  .product-info {
    padding-left: 15px;
    margin-top: 30px;
  }
  
  .product-page-thumbs {
    flex-direction: row;
    overflow-x: auto;
    padding-bottom: 10px;
  }
  
  .product-page-thumbs .thumbs {
    min-width: 80px;
  }
}

/* Animaciones */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.product-page {
  animation: fadeIn 0.5s ease;
}

/* MEDIOS DE PAGO */
.payment-info-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.payment-info-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.installments-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-bottom: 15px;
}

.installment-option {
  background: white;
  padding: 10px;
  border-radius: 6px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.installment-number {
  font-weight: 600;
  color: #333;
  display: block;
}

.installment-price {
  color: #28a745;
  font-size: 18px;
  display: block;
}

.installment-text {
  font-size: 11px;
  color: #666;
}

.payment-methods-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
  align-items: center;
}

.payment-icon {
  max-height: 30px;
  object-fit: contain;
}

.shipping-options {
  background: white;
  padding: 15px;
  border-radius: 6px;
}

.shipping-option {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.shipping-option:last-child {
  margin-bottom: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .installments-grid {
    grid-template-columns: 1fr;
  }

  .payment-methods-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* @media (min-width: 1664px) {
  .container {
    max-width: 1664px;
  } */

