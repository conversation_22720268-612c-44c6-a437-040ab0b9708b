<!-- Blog Home -->
{% if options.home_blog and pages.categories.category["Post"] != blank %}
    <section class="container">
    <div id="home-blog">
      <div class="row">
        <div class="col-12">
          <h2 class="page-header">{% t 'Latest Posts' %}</h2>
        </div>
      </div>
      <div class="row">
      {% unless pages.categories.category["Post"] == blank %}
      {% paginate pages.categories.category["Post"].pages by options.page_post_per_page reversed %}
      {% for page in paged.pages reversed %}
        {% if forloop.index0 == 3 %}{% break %}{% endif %}
        <div class="col-12 col-md-4 blog-post">
          {% if page.images == empty %}
          <a href="{{ page.url }}" title="{{ page.title }}">
            <img class="img-fluid" src="//placehold.it/800x500" alt="{{ page.title }}">
          </a>
          {% else %}
          <a href="{{ page.url }}" title="{{ page.title }}">
            <img class="img-fluid" src="{{ page.images.first | thumb: '800x500' }}" alt="{{ page.title }}" />
          </a>
          {% endif %}
          <h3><a class="title-blog-home" href="{{ page.url }}">{{ page.title }}</a></h3>
        </div>
      {% endfor %}
      {% endpaginate %}
        <div class="col-12 text-center block-footer">
          <a href="blog" title="{% t 'Go to Blog' %}" class="btn-primary btn">{% t 'Go to Blog' %}</a>
        </div>
         {% endunless %}
      </div>
    </div>
    </section>
    {% endif %}
    